import React, { useState } from "react";
import { useN<PERSON><PERSON>, Link } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  Eye, 
  EyeOff, 
  Shield, 
  Brain, 
  DollarSign, 
  PieChart, 
  TrendingUp, 
  Building, 
  Calculator,
  Sparkles,
  ArrowLeft,
  CheckCircle,
  UserPlus,
  Mail,
  Lock,
  User,
  Award,
  Star,
  Users,
  Zap
} from "lucide-react";

const Register = () => {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    password: "",
    confirmPassword: "",
  });
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const { signup } = useAuth();
  const navigate = useNavigate();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");

    if (formData.password !== formData.confirmPassword) {
      return setError("Passwords do not match");
    }

    if (formData.password.length < 6) {
      return setError("Password should be at least 6 characters");
    }

    try {
      setLoading(true);
      await signup(formData.email, formData.password, formData.name);
      navigate("/dashboard");
    } catch (error: any) {
      setError(error.message || "Failed to create an account");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-slate-900 text-white overflow-hidden relative">
      {/* Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-indigo-900"></div>
      
      {/* Financial background pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%2359B6F4' fill-opacity='0.4'%3E%3Cpath d='M30 30c0-11.046 8.954-20 20-20s20 8.954 20 20-8.954 20-20 20-20-8.954-20-20zm0 0c0 11.046-8.954 20-20 20S-10 41.046-10 30s8.954-20 20-20 20 8.954 20 20z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          backgroundSize: '120px 120px'
        }}></div>
      </div>

      {/* Floating financial icons */}
      <div className="absolute inset-0 pointer-events-none">
        <DollarSign className="absolute top-20 right-20 h-16 w-16 text-green-400/20 animate-pulse" style={{animationDelay: '0s'}} />
        <PieChart className="absolute top-40 left-20 h-12 w-12 text-blue-400/20 animate-pulse" style={{animationDelay: '1s'}} />
        <Calculator className="absolute bottom-40 right-40 h-14 w-14 text-purple-400/20 animate-pulse" style={{animationDelay: '2s'}} />
        <TrendingUp className="absolute bottom-60 left-40 h-10 w-10 text-emerald-400/20 animate-pulse" style={{animationDelay: '3s'}} />
        <Building className="absolute top-2/3 right-1/3 h-12 w-12 text-cyan-400/20 animate-pulse" style={{animationDelay: '4s'}} />
      </div>

      {/* Floating orbs */}
      <div className="absolute top-20 right-20 w-96 h-96 bg-purple-500/20 rounded-full blur-3xl animate-pulse"></div>
      <div className="absolute bottom-20 left-20 w-80 h-80 bg-blue-500/20 rounded-full blur-3xl animate-pulse" style={{animationDelay: '2s'}}></div>

      {/* Header */}
      <div className="relative z-10 p-6">
        <div className="flex items-center justify-between">
          <Link to="/" className="flex items-center gap-3 group">
            <ArrowLeft className="h-6 w-6 text-white/70 group-hover:text-white transition-colors" />
            <div className="flex items-center gap-3">
              <div className="h-10 w-10 rounded-xl bg-gradient-to-br from-blue-600 to-purple-600 flex items-center justify-center text-white font-bold">
                P
              </div>
              <span className="text-xl font-bold text-white">PKF Audit AI</span>
            </div>
          </Link>
          
          <div className="flex items-center gap-4">
            <span className="text-white/70">Already have an account?</span>
            <Link to="/login">
              <Button variant="outline" className="bg-white/15 border-white/70 text-white hover:bg-white/30 hover:border-white font-semibold backdrop-blur-sm">
                Sign In
              </Button>
            </Link>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 flex items-center justify-center min-h-[calc(100vh-120px)] px-6 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center max-w-7xl w-full">
          
          {/* Left side - Branding */}
          <div className="space-y-8 text-center lg:text-left">
            <div className="inline-flex items-center px-6 py-3 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 text-white">
              <Sparkles className="h-5 w-5 mr-2 text-purple-300" />
              <span className="font-medium">Join PKF Audit AI Revolution</span>
            </div>

            <div className="space-y-6">
              <h1 className="text-5xl md:text-6xl lg:text-7xl font-black leading-none">
                <span className="block bg-gradient-to-r from-white via-purple-200 to-white bg-clip-text text-transparent">
                  Start Your
                </span>
                <span className="block bg-gradient-to-r from-purple-400 via-pink-400 to-blue-400 bg-clip-text text-transparent">
                  AI Journey
                </span>
              </h1>

              <p className="text-xl text-white/90 leading-relaxed max-w-2xl">
                Join thousands of financial professionals who are transforming their audit 
                workflow with our <span className="text-purple-400 font-semibold">AI-powered platform</span>. 
                Get started with a free trial today.
              </p>
            </div>

            {/* Trust indicators */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-2xl">
              <div className="flex items-center gap-3 bg-white/5 backdrop-blur-sm border border-white/20 rounded-2xl p-4">
                <div className="h-12 w-12 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl flex items-center justify-center">
                  <Users className="h-6 w-6 text-white" />
                </div>
                <div>
                  <div className="text-white font-semibold">10,000+ Users</div>
                  <div className="text-white/60 text-sm">Trusted globally</div>
                </div>
              </div>

              <div className="flex items-center gap-3 bg-white/5 backdrop-blur-sm border border-white/20 rounded-2xl p-4">
                <div className="h-12 w-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
                  <Award className="h-6 w-6 text-white" />
                </div>
                <div>
                  <div className="text-white font-semibold">Industry Leader</div>
                  <div className="text-white/60 text-sm">Award winning</div>
                </div>
              </div>
            </div>

            {/* Customer testimonial */}
            <div className="bg-white/5 backdrop-blur-sm border border-white/20 rounded-2xl p-6 max-w-2xl">
              <div className="flex items-center gap-1 mb-3">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                ))}
              </div>
              <p className="text-white/80 italic mb-4">
                "PKF Audit AI has revolutionized our audit process. We've reduced our audit time by 80% 
                while improving accuracy significantly."
              </p>
              <div className="flex items-center gap-3">
                <div className="h-10 w-10 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white font-bold">
                  JS
                </div>
                <div>
                  <div className="text-white font-semibold">John Smith</div>
                  <div className="text-white/60 text-sm">Senior Auditor, ABC Corp</div>
                </div>
              </div>
            </div>
          </div>

          {/* Right side - Registration Form */}
          <div className="flex justify-center lg:justify-end">
            <Card className="w-full max-w-md bg-white/5 backdrop-blur-xl border border-white/20 shadow-2xl">
              <CardHeader className="space-y-1 text-center">
                <div className="mx-auto h-16 w-16 bg-gradient-to-br from-purple-600 to-pink-600 rounded-2xl flex items-center justify-center mb-4">
                  <UserPlus className="h-8 w-8 text-white" />
                </div>
                <CardTitle className="text-3xl font-bold text-white">
                  Create an Account
                </CardTitle>
                <CardDescription className="text-white/70">
                  Sign up to access the Audit Report Guardian AI
                </CardDescription>
              </CardHeader>
              
              <CardContent>
                {error && (
                  <div className="bg-red-500/20 border border-red-500/30 text-red-200 p-4 rounded-xl mb-6 backdrop-blur-sm">
                    <div className="flex items-center gap-2">
                      <div className="h-5 w-5 bg-red-500 rounded-full flex items-center justify-center flex-shrink-0">
                        <span className="text-white text-xs">!</span>
                      </div>
                      <span className="text-sm">{error}</span>
                    </div>
                  </div>
                )}
                
                <form onSubmit={handleSubmit} className="space-y-5">
                  <div className="space-y-2">
                    <Label htmlFor="name" className="text-white/90 font-medium">Full Name</Label>
                    <div className="relative">
                      <User className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-white/50" />
                      <Input
                        id="name"
                        name="name"
                        type="text"
                        value={formData.name}
                        onChange={handleChange}
                        placeholder="John Doe"
                        className="pl-12 bg-white/10 border-white/20 text-white placeholder-white/50 focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 rounded-xl h-12"
                        required
                      />
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="email" className="text-white/90 font-medium">Email</Label>
                    <div className="relative">
                      <Mail className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-white/50" />
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        value={formData.email}
                        onChange={handleChange}
                        placeholder="<EMAIL>"
                        className="pl-12 bg-white/10 border-white/20 text-white placeholder-white/50 focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 rounded-xl h-12"
                        required
                      />
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="password" className="text-white/90 font-medium">Password</Label>
                    <div className="relative">
                      <Lock className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-white/50" />
                      <Input
                        id="password"
                        name="password"
                        type={showPassword ? "text" : "password"}
                        value={formData.password}
                        onChange={handleChange}
                        placeholder="••••••••"
                        className="pl-12 pr-12 bg-white/10 border-white/20 text-white placeholder-white/50 focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 rounded-xl h-12"
                        required
                      />
                      <button
                        type="button"
                        className="absolute right-4 top-1/2 transform -translate-y-1/2 text-white/50 hover:text-white/70 transition-colors"
                        onClick={() => setShowPassword(!showPassword)}
                      >
                        {showPassword ? (
                          <EyeOff className="h-5 w-5" />
                        ) : (
                          <Eye className="h-5 w-5" />
                        )}
                      </button>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="confirmPassword" className="text-white/90 font-medium">Confirm Password</Label>
                    <div className="relative">
                      <Lock className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-white/50" />
                      <Input
                        id="confirmPassword"
                        name="confirmPassword"
                        type={showConfirmPassword ? "text" : "password"}
                        value={formData.confirmPassword}
                        onChange={handleChange}
                        placeholder="••••••••"
                        className="pl-12 pr-12 bg-white/10 border-white/20 text-white placeholder-white/50 focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 rounded-xl h-12"
                        required
                      />
                      <button
                        type="button"
                        className="absolute right-4 top-1/2 transform -translate-y-1/2 text-white/50 hover:text-white/70 transition-colors"
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                      >
                        {showConfirmPassword ? (
                          <EyeOff className="h-5 w-5" />
                        ) : (
                          <Eye className="h-5 w-5" />
                        )}
                      </button>
                    </div>
                  </div>
                  
                  <Button 
                    type="submit"
                    className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white rounded-xl h-12 text-base font-semibold shadow-lg transform hover:scale-[1.02] transition-all duration-200" 
                    disabled={loading}
                  >
                    {loading ? "Creating account..." : "Sign Up"}
                  </Button>

                  <div className="text-center text-xs text-white/60 leading-relaxed">
                    By creating an account, you agree to our{" "}
                    <Link to="/terms" className="text-purple-400 hover:text-purple-300 underline">
                      Terms of Service
                    </Link>{" "}
                    and{" "}
                    <Link to="/privacy" className="text-purple-400 hover:text-purple-300 underline">
                      Privacy Policy
                    </Link>
                  </div>
                </form>
              </CardContent>
              
              <CardFooter className="flex justify-center border-t border-white/10 pt-6">
                <p className="text-sm text-white/70">
                  Already have an account?{" "}
                  <Link to="/login" className="text-purple-400 hover:text-purple-300 font-semibold transition-colors">
                    Log in
                  </Link>
                </p>
              </CardFooter>
            </Card>
          </div>
        </div>
      </div>

      {/* Trust indicators */}
      <div className="relative z-10 py-8">
        <div className="max-w-7xl mx-auto px-6">
          <div className="flex items-center justify-center gap-8 text-center">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-400" />
              <span className="text-white/70 text-sm">Free 14-Day Trial</span>
            </div>
            <div className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-blue-400" />
              <span className="text-white/70 text-sm">No Credit Card Required</span>
            </div>
            <div className="flex items-center gap-2">
              <Zap className="h-5 w-5 text-yellow-400" />
              <span className="text-white/70 text-sm">Setup in Minutes</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Register;