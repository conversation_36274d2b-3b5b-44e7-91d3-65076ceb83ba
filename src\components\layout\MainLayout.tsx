import React, { useEffect, useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import DashboardSidebar from "./DashboardSidebar";
import Header from "./Header";
import LoadingScreen from "@/components/common/LoadingScreen";

interface MainLayoutProps {
  children: React.ReactNode;
}

const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  const { currentUser } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [loading, setLoading] = useState(true);
  
  // Check if this is the landing page or auth pages that need full-width layout
  const isLandingPage = location.pathname === '/';
  const isAuthPage = ['/login', '/register', '/forgot-password'].includes(location.pathname);
  const isContentPage = ['/blog', '/resources', '/case-studies', '/terms', '/privacy', '/cookies', '/security', '/compliance', '/gdpr'].includes(location.pathname);
  const isDashboardPage = location.pathname.startsWith('/dashboard');
  
  // Debug info
  useEffect(() => {
    console.log("MainLayout rendered with currentUser:", currentUser ? "Logged in" : "Not logged in");
    console.log("Current location:", location.pathname);
    console.log("Is landing page:", isLandingPage);
    console.log("Is auth page:", isAuthPage);
    console.log("Is content page:", isContentPage);
    console.log("Is dashboard page:", isDashboardPage);
  }, [currentUser, location, isLandingPage, isAuthPage, isContentPage, isDashboardPage]);
  
  // Handle loading state
  useEffect(() => {
    // Show loading state when changing routes
    setLoading(true);
    
    // Hide loading after a short delay to simulate content loading
    const timer = setTimeout(() => {
      setLoading(false);
    }, 500);
    
    return () => clearTimeout(timer);
  }, [location.pathname]);

  // If user is logged in and at the root path, redirect them to the dashboard
  useEffect(() => {
    if (currentUser && location.pathname === '/') {
      console.log("User is logged in and at root path, redirecting to dashboard");
      navigate('/dashboard');
    }
  }, [currentUser, location, navigate]);

  // Show loading screen during route transitions
  if (loading) {
    return <LoadingScreen />;
  }

  // Landing page - Full width, no containers
  if (isLandingPage) {
    return (
      <div className="min-h-screen w-full">
        {children}
      </div>
    );
  }
  
  // Auth pages (Login, Register, Forgot Password) - Full width with dark background
  if (isAuthPage) {
    return (
      <div className="min-h-screen w-full">
        {children}
      </div>
    );
  }
  
  // Content pages - Full width with dark background
  if (isContentPage) {
    return (
      <div className="min-h-screen w-full bg-slate-900">
        {children}
      </div>
    );
  }

  // Dashboard pages - Layout with sidebar and header
  if (isDashboardPage && currentUser) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />

        <div className="flex min-h-[calc(100vh-64px)]">
          {/* Sidebar placeholder for spacing */}
          <div id="sidebar-placeholder" className="w-64 flex-shrink-0 transition-all duration-300" />

          <main className="flex-1 p-4 md:p-6 overflow-auto">
            <div className="max-w-7xl mx-auto">
              {children}
            </div>
          </main>

          {/* Actual Sidebar - Fixed Position */}
          <DashboardSidebar />
        </div>
      </div>
    );
  }

  // Redirect unauthenticated users trying to access dashboard
  if (isDashboardPage && !currentUser) {
    useEffect(() => {
      navigate('/login');
    }, [navigate]);
    
    return <LoadingScreen />;
  }

  // Default layout for other authenticated pages
  if (currentUser) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />

        <div className="flex min-h-[calc(100vh-64px)]">
          {/* Sidebar placeholder for spacing */}
          <div id="sidebar-placeholder" className="w-64 flex-shrink-0 transition-all duration-300" />

          <main className="flex-1 p-4 md:p-6 overflow-auto">
            <div className="max-w-7xl mx-auto">
              {children}
            </div>
          </main>

          {/* Actual Sidebar - Fixed Position */}
          <DashboardSidebar />
        </div>
      </div>
    );
  }

  // Default fallback for unauthenticated users
  return (
    <div className="min-h-screen w-full bg-slate-900">
      {children}
    </div>
  );
};

export default MainLayout;