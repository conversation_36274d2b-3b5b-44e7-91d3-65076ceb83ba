import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { CheckCircle, XCircle, AlertTriangle, ChevronDown, ChevronUp, FileText } from 'lucide-react';
import { CheckResult } from '@/lib/googleAI2';
import AuditReportItem from './AuditReportItem';

interface PDFCheckResultsProps {
  results: Record<string, CheckResult>;
  parameters: {
    company_name: string;
    audit_date: Date | string | null;
    profit_or_loss: string;
    company_listing_status: string;
    top_1000_or_500: string;
    audit_report_type: string;
    audit_opinion_type: string;
    is_nbfc: string;
    firm_name?: string;
    firm_registration_number?: string;
    partner_name?: string;
    partner_registration_number?: string;
    partner_uid_number?: string;
  };
  documents?: {
    auditReport?: { url: string; name: string };
    annexureA?: { url: string; name: string };
    annexureB?: { url: string; name: string };
    balanceSheet?: { url: string; name: string };
    notes?: { url: string; name: string };
    plNotes?: { url: string; name: string };
    annualReport?: { url: string; name: string };
    secretarialCompliance?: { url: string; name: string };
  };
}

// Map of check IDs to proper display titles
const CHECK_TITLES: Record<string, string> = {
  // Independent - Audit Report
  "company_format": "Company Format",
  "profit_loss": "Profit/Loss Consistency",
  "brsr_brr": "BRSR/BRR References",
  "signature_date": "Signature Date",
  "financial_statements_type": "Financial Statements Type",
  "opinion_type_consistency": "Opinion Type Consistency",
  "company_name_consistency": "Company Name Consistency",
  "clause_20": "Clause 20 Reference",
  "clause_21": "Clause 21 Reference",
  "benami_property_clause": "Benami Property Clause",
  "key_audit_matter": "Key Audit Matter",
  "audit_trail_software": "Audit-Trail Accounting Software",
  "section_197_reference": "Section 197(16) Reference",
  "ar_dispute_immovable": "Immovable Property Dispute in AR",
  
  "ar_fraud_mention": "Fraud Mention in AR",
  "ar_section_143_12": "Section 143(12) in AR",
  "ar_whistle_blower": "Whistle Blower in AR",

  // CARO
  "caro_clause_xxi": "CARO Clause XXI",
  "caro_clause_count": "CARO 20 Clauses Coverage",
  "caro_nbfc_iii_clause_present": "NBFC CARO Clause III Present",
  "caro_ppe_clause": "PPE Clause in CARO",
  "caro_intangible_clause": "Intangible Assets Clause in CARO",
  "caro_inventory_clause": "Inventory Clause in CARO",
  "caro_inventory_clause_iib": "Inventory Clause IIB in CARO",
  "caro_title_deed_clause": "Title Deed Clause in CARO",
  "caro_revaluation_clause": "Revaluation Clause in CARO",
  "caro_inventory_clause_covers_transit": "Inventory Transit Coverage in CARO",
  "caro_clause_iii_present": "Clause III Present in CARO",
  "caro_iii_a_guar_total": "Clause III(a) Guarantees Total",
  "caro_iii_b_to_e_present": "Clause III(b) to (e) Present",
  "caro_iii_f_present": "Clause III(f) Present",
  "caro_clause_present": "CARO Clause Present",
  "caro_fraud_mention": "Fraud Mention in CARO",
  "caro_section_143_12": "Section 143(12) in CARO",
  "caro_whistle_blower": "Whistle Blower in CARO",
  "caro_immovable_property_dispute": "Immovable Property Dispute in CARO",
  "caro_benami_reference": "Benami Reference in CARO",
  "caro_deemed_deposits": "Deemed Deposits in CARO",
  "caro_stat_dues": "Statutory Dues in CARO",
  "caro_loans_advances": "Loans & Advances in CARO",
  "caro_secured_borrowings": "Secured Borrowings in CARO",

  // IFC
  "ifc_opinion_type": "IFC Opinion Type",
  "ifc_material_weakness": "Material Weakness in IFC",
  "ifc_essential_components": "Essential Components in IFC",
  "ifc_inherent_limitations": "Inherent Limitations in IFC",

  // Interlinked
  "caro_interlink_ar": "CARO Reference in Audit Report",
  "caro_interlink_annexure": "Audit Report Reference in CARO",
  "ifc_interlink_ar": "IFC Reference in Audit Report",
  "ifc_interlink_annexure": "Audit Report Reference in IFC",

  // Balance Sheet & CARO
  "bs_ppe_caro": "PPE in Balance Sheet vs CARO",
  "bs_intangible_caro": "Intangible Assets vs CARO",
  "bs_inventory_caro": "Inventory vs CARO",
  "bs_immovable_property_caro": "Immovable Property vs CARO",
  "bs_secured_borrowings_caro": "Secured Borrowings vs CARO",
  "notes_investment_loans_caro": "New Investments/Loans vs CARO",
  "notes_contingent_liab_caro": "Contingent Liabilities vs CARO",
  "rpt_loans_caro": "Related Party Transactions vs CARO",
  "bs_stat_dues_caro": "Statutory Dues vs CARO",
  "bs_deemed_deposits_caro": "Deemed Deposits vs CARO",

  // Balance Sheet & P&L Validations
  "balance_sheet_ppe_value": "PPE Value in Balance Sheet",
  "balance_sheet_intangible_value": "Intangible Assets Value",
  "balance_sheet_inventory_value": "Inventory Value",
  "bs_has_immovable_property": "Immovable Property in Balance Sheet",
  "bs_has_revaluation": "Revaluation in Balance Sheet",
  "bs_secured_borrowings_gt5cr": "Secured Borrowings > 5 Cr",
  "bs_current_liab_vs_asset": "Current Liabilities vs Assets",
  "bs_opening_networth": "Opening Net Worth",
  "bs_fixed_deposits": "Fixed Deposits in Balance Sheet",
  "bs_ppe_disclosure": "PPE Disclosure in Balance Sheet",
  "bs_stat_dues": "Statutory Dues in Balance Sheet",
  "bs_stat_dues_same": "Statutory Dues Same as Last Year",
  "bs_secured_borrowings_gemini": "Secured Borrowings (Gemini)",
  "bs_deemed_deposits": "Deemed Deposits in Balance Sheet",
  "inventory_writeoff_check": "Inventory Write-off Check",
  "inventory_writeoff_caro": "Inventory Write-off vs CARO",
  "inventory_writeoff_gemini": "Inventory Write-off Detection",

  // Notes
  "notes_has_goods_in_transit": "Goods in Transit in Notes",
  "notes_rpt_has_loans": "Related Party Loans in Notes",
  "notes_rpt_loans_guar_total": "RPT Loans & Guarantees Total",
  "notes_contg_fin_guar_total": "Contingent Financial Guarantees Total",
  "notes_has_new_invest_loan_guar": "New Investments/Loans/Guarantees",
  "notes_new_finance_total": "New Finance Total",
  "notes_stat_dues": "Statutory Dues in Notes",
  "notes_stat_dues_gemini": "Statutory Dues (Gemini)",

  // Annual Report
  "annual_report_analysis": "Annual Report Analysis",
  "annual_report_fraud_mention": "Fraud Mention in Annual Report",
  "annual_report_section_143_12": "Section 143(12) in Annual Report",
  "annual_report_whistle_blower": "Whistle Blower in Annual Report",
  "annual_report_immovable_property_dispute": "Immovable Property Dispute",
  "annual_report_benami_reference": "Benami Reference in Annual Report",
  "secretarial_compliance_check": "Secretarial Compliance Check",

  // Annual Report & CARO Interlinks
  "annual_report_caro_interlink": "Annual Report & CARO Interlink",
  "annual_report_fraud_mention_caro": "Fraud Mention in AR vs CARO",
  "annual_report_section_143_12_caro": "Section 143(12) in AR vs CARO",
  "annual_report_whistle_blower_caro": "Whistle Blower in AR vs CARO",
  "annual_report_immovable_property_dispute_caro": "Property Dispute in AR vs CARO",
  "annual_report_benami_reference_caro": "Benami Reference in AR vs CARO",

  // Combined Checks
  "fraud_mention_caro": "Fraud Mention vs CARO",
  "section_143_12_caro": "Section 143(12) vs CARO",
  "whistle_blower_caro": "Whistle Blower vs CARO",
  "immovable_property_dispute_caro": "Property Dispute vs CARO",
  "benami_reference_caro": "Benami Reference vs CARO",

  // Signature Checks
  "signature_partner_name": "Partner Name in Signature",
  "signature_partner_membership": "Partner Membership Number",
  "signature_partner_uid": "Partner UID Number",
  "signature_firm_name": "Firm Name in Signature",
  "signature_firm_registration": "Firm Registration Number"
};

const PDFCheckResults: React.FC<PDFCheckResultsProps> = ({ results, parameters, documents }) => {
  const [activeTab, setActiveTab] = useState('audit-report');

  // Count the number of compliant checks
  const compliantCount = Object.values(results).filter(result => result.isCompliant).length;
  const totalChecks = Object.keys(results).length;
  const compliancePercentage = totalChecks > 0 ? Math.round((compliantCount / totalChecks) * 100) : 0;

  // Group results by document type and category
  const auditReportResults = Object.entries(results)
    .filter(([key]) =>
      ["company_format", "profit_loss", "brsr_brr", "signature_date",
       "financial_statements_type", "opinion_type_consistency",
       "company_name_consistency", "key_audit_matter", "clause_20",
       "audit_trail_software", "section_197_reference",
       "ar_dispute_immovable", "benami_property_clause", "ar_fraud_mention",
       "ar_section_143_12", "ar_whistle_blower"].includes(key))
    .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {});

  const caroResults = Object.entries(results)
    .filter(([key]) =>
      key.startsWith('caro_') &&
      !["caro_interlink_ar", "caro_interlink_annexure"].includes(key))
    .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {});

  const ifcResults = Object.entries(results)
    .filter(([key]) =>
      key.startsWith('ifc_') &&
      !["ifc_interlink_ar", "ifc_interlink_annexure"].includes(key))
    .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {});

  const balanceSheetResults = Object.entries(results)
    .filter(([key]) =>
      (key.startsWith('bs_') || key.startsWith('balance_sheet_')) &&
      !key.includes('_caro'))
    .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {});

  const interlinkResults = Object.entries(results)
    .filter(([key]) =>
      ["caro_interlink_ar", "caro_interlink_annexure",
       "ifc_interlink_ar", "ifc_interlink_annexure"].includes(key) ||
      key.includes('_caro'))
    .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {});

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>Analysis Results</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between mb-4">
            <div className="text-sm font-medium">
              {compliantCount}/{totalChecks} Checks Passed ({compliancePercentage}% Compliant)
            </div>
          </div>

          <div className="w-full bg-gray-200 rounded-full h-2.5 mb-6">
            <div
              className={`h-2.5 rounded-full ${
                compliancePercentage < 50 ? "bg-red-500" :
                compliancePercentage < 80 ? "bg-yellow-500" : "bg-green-500"
              }`}
              style={{ width: `${compliancePercentage}%` }}
            ></div>
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid grid-cols-7 mb-4">
              <TabsTrigger value="audit-report">Audit Report</TabsTrigger>
              <TabsTrigger value="caro">CARO</TabsTrigger>
              <TabsTrigger value="ifc">IFC</TabsTrigger>
              <TabsTrigger value="balance-sheet">Balance Sheet</TabsTrigger>
              <TabsTrigger value="notes">Notes</TabsTrigger>
              <TabsTrigger value="annual-report">Annual Report</TabsTrigger>
              <TabsTrigger value="interlinks">Interlinks</TabsTrigger>
            </TabsList>

            <TabsContent value="audit-report">
              <div className="space-y-4">
                <h2 className="text-xl font-bold">1.1 Audit Report Checks</h2>

                {Object.keys(auditReportResults).length > 0 ? (
                  Object.entries(auditReportResults).map(([key, result]) => (
                    <AuditReportItem
                      key={key}
                      title={CHECK_TITLES[key] || key}
                      result={result as CheckResult}
                    />
                  ))
                ) : (
                  <Alert>
                    <AlertTitle>Information</AlertTitle>
                    <AlertDescription>
                      No audit report checks have been run yet. Upload an Audit Report and run checks to see results.
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            </TabsContent>

            <TabsContent value="caro">
              <div className="space-y-4">
                <h2 className="text-xl font-bold">1.2 CARO Checks</h2>

                {Object.keys(caroResults).length > 0 ? (
                  Object.entries(caroResults).map(([key, result]) => (
                    <AuditReportItem
                      key={key}
                      title={CHECK_TITLES[key] || key}
                      result={result as CheckResult}
                    />
                  ))
                ) : (
                  <Alert>
                    <AlertTitle>Information</AlertTitle>
                    <AlertDescription>
                      No CARO checks have been run yet. Upload Annexure A (CARO) and run checks to see results.
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            </TabsContent>

            <TabsContent value="ifc">
              <div className="space-y-4">
                <h2 className="text-xl font-bold">1.3 IFC Checks</h2>

                {Object.keys(ifcResults).length > 0 ? (
                  Object.entries(ifcResults).map(([key, result]) => (
                    <AuditReportItem
                      key={key}
                      title={CHECK_TITLES[key] || key}
                      result={result as CheckResult}
                    />
                  ))
                ) : (
                  <Alert>
                    <AlertTitle>Information</AlertTitle>
                    <AlertDescription>
                      No IFC checks have been run yet. Upload Annexure B (IFC) and run checks to see results.
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            </TabsContent>

            <TabsContent value="balance-sheet">
              <div className="space-y-4">
                <h2 className="text-xl font-bold">3. Balance Sheet & P&L Validations</h2>

                {Object.keys(balanceSheetResults).length > 0 ? (
                  Object.entries(balanceSheetResults).map(([key, result]) => (
                    <AuditReportItem
                      key={key}
                      title={CHECK_TITLES[key] || key}
                      result={result as CheckResult}
                    />
                  ))
                ) : (
                  <Alert>
                    <AlertTitle>Information</AlertTitle>
                    <AlertDescription>
                      No Balance Sheet checks have been run yet. Upload Balance Sheet and run checks to see results.
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            </TabsContent>

            <TabsContent value="notes">
              <div className="space-y-4">
                <h2 className="text-xl font-bold">4. Notes to Accounts</h2>

                {Object.entries(results)
                  .filter(([key]) => key.startsWith('notes_') && !key.includes('_caro')).length > 0 ? (
                  Object.entries(results)
                    .filter(([key]) => key.startsWith('notes_') && !key.includes('_caro'))
                    .map(([key, result]) => (
                      <AuditReportItem
                        key={key}
                        title={CHECK_TITLES[key] || key}
                        result={result as CheckResult}
                      />
                    ))
                ) : (
                  <Alert>
                    <AlertTitle>Information</AlertTitle>
                    <AlertDescription>
                      No Notes checks have been run yet. Upload Notes to Accounts and run checks to see results.
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            </TabsContent>

            <TabsContent value="annual-report">
              <div className="space-y-4">
                <h2 className="text-xl font-bold">5. Annual Report & Secretarial Compliance</h2>

                {Object.entries(results)
                  .filter(([key]) => (key.startsWith('annual_report_') && !key.includes('_caro')) || key === 'secretarial_compliance_check').length > 0 ? (
                  Object.entries(results)
                    .filter(([key]) => (key.startsWith('annual_report_') && !key.includes('_caro')) || key === 'secretarial_compliance_check')
                    .map(([key, result]) => (
                      <AuditReportItem
                        key={key}
                        title={CHECK_TITLES[key] || key}
                        result={result as CheckResult}
                      />
                    ))
                ) : (
                  <Alert>
                    <AlertTitle>Information</AlertTitle>
                    <AlertDescription>
                      No Annual Report checks have been run yet. Upload Annual Report and run checks to see results.
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            </TabsContent>

            <TabsContent value="interlinks">
              <div className="space-y-4">
                <h2 className="text-xl font-bold">2. Interlinked References</h2>

                {Object.keys(interlinkResults).length > 0 ? (
                  Object.entries(interlinkResults).map(([key, result]) => (
                    <AuditReportItem
                      key={key}
                      title={CHECK_TITLES[key] || key}
                      result={result as CheckResult}
                    />
                  ))
                ) : (
                  <Alert>
                    <AlertTitle>Information</AlertTitle>
                    <AlertDescription>
                      No interlink checks have been run yet. Upload multiple documents and run checks to see results.
                    </AlertDescription>
                  </Alert>
                )}

                <h3 className="text-lg font-semibold mt-6">6. Signature Checks</h3>

                {Object.entries(results)
                  .filter(([key]) => key.startsWith('signature_')).length > 0 ? (
                  Object.entries(results)
                    .filter(([key]) => key.startsWith('signature_'))
                    .map(([key, result]) => (
                      <AuditReportItem
                        key={key}
                        title={CHECK_TITLES[key] || key}
                        result={result as CheckResult}
                      />
                    ))
                ) : (
                  <Alert>
                    <AlertTitle>Information</AlertTitle>
                    <AlertDescription>
                      No signature checks have been run yet. Run checks to see results.
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default PDFCheckResults;
