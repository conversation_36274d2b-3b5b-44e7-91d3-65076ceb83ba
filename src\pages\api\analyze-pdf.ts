import { NextApiRequest, NextApiResponse } from 'next';
import { processPdfWithGemini } from '@/lib/googleAI2';
import formidable from 'formidable';
import fs from 'fs';

// Disable the default body parser to handle file uploads
export const config = {
  api: {
    bodyParser: false,
  },
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Parse the form data
    const form = formidable({ multiples: false });
    
    const parseForm = async (): Promise<{ fields: formidable.Fields; files: formidable.Files }> => {
      return new Promise((resolve, reject) => {
        form.parse(req, (err, fields, files) => {
          if (err) reject(err);
          resolve({ fields, files });
        });
      });
    };

    const { fields, files } = await parseForm();
    
    // Extract parameters
    const checkType = fields.checkType?.[0] || '';
    const parameters = fields.parameters?.[0] ? JSON.parse(fields.parameters[0]) : {};
    
    // Get the uploaded file
    const fileField = files.pdfFile;
    if (!fileField || Array.isArray(fileField)) {
      return res.status(400).json({ error: 'No PDF file uploaded' });
    }

    // Read the file
    const fileData = fs.readFileSync(fileField.filepath);
    const pdfFile = new File([fileData], fileField.originalFilename || 'document.pdf', { 
      type: 'application/pdf' 
    });

    // Process the PDF with Gemini
    const result = await processPdfWithGemini(pdfFile, checkType, parameters);
    
    // Return the result
    return res.status(200).json(result);
  } catch (error) {
    console.error('Error processing PDF:', error);
    return res.status(500).json({ 
      error: 'Error processing PDF', 
      message: error instanceof Error ? error.message : String(error) 
    });
  }
}
