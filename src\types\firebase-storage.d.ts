declare module 'firebase/storage' {
  import { FirebaseStorage } from '@firebase/storage-types';
  
  export function ref(storage: FirebaseStorage, path?: string): any;
  export function getBlob(ref: any): Promise<Blob>;
  export function getDownloadURL(ref: any): Promise<string>;
  export function getMetadata(ref: any): Promise<any>;
  export function deleteObject(ref: any): Promise<void>;
  export function uploadBytesResumable(ref: any, data: Blob | Uint8Array | ArrayBuffer, metadata?: any): any;
}
