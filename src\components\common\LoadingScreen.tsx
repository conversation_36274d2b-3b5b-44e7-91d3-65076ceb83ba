import React from 'react';

const LoadingScreen = () => {
  return (
    <div className="fixed inset-0 flex items-center justify-center bg-slate-900 z-50">
      <div className="flex flex-col items-center">
        <div className="h-16 w-16 rounded-xl bg-gradient-to-br from-blue-600 to-purple-600 flex items-center justify-center text-white font-bold text-2xl animate-pulse">
          P
        </div>
        <div className="mt-6 space-y-4 text-center">
          <h2 className="text-2xl font-bold text-white">Loading</h2>
          <div className="flex justify-center space-x-2">
            <div className="w-3 h-3 rounded-full bg-blue-400 animate-bounce" style={{ animationDelay: "0s" }}></div>
            <div className="w-3 h-3 rounded-full bg-blue-400 animate-bounce" style={{ animationDelay: "0.2s" }}></div>
            <div className="w-3 h-3 rounded-full bg-blue-400 animate-bounce" style={{ animationDelay: "0.4s" }}></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoadingScreen;
