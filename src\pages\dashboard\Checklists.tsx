// Checklists.tsx - Complete checklist based on actual system implementation

import React, { useState, useEffect } from "react";
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { CheckCircle, AlertTriangle, InfoIcon, FileText, BadgeCheck, Clock, Download, DownloadCloud, Building, Shield, Users, Gavel, TrendingUp, Search, ExternalLink } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

const Checklists = () => {
  const [showAllChecks, setShowAllChecks] = useState(false);
  const [activeTab, setActiveTab] = useState("overview");

  // =====================================
  // SINGLE DOCUMENT CHECKS
  // =====================================
  const singleDocumentChecks = {
    auditReport: [
      {
        id: 'audit_title',
        name: 'Independent Auditor\'s Report Title',
        description: 'Verifies the exact heading "Independent Auditor\'s Report" is present',
        category: 'Basic Compliance',
        documents: ['Audit Report'],

      },
      {
        id: 'company_format',
        name: 'Address to Members Format',
        description: 'Checks for exact text "To the Members of [Company Name]" format',
        category: 'Basic Compliance',
        documents: ['Audit Report'],

      },
      {
        id: 'signature_date',
        name: 'PKF Signature Block & Date',
        description: 'Validates PKF Sridhar & Santhanam LLP signature block with correct audit date',
        category: 'Basic Compliance',
        documents: ['Audit Report'],

      },
      {
        id: 'financial_statements_type',
        name: 'Financial Statements Type Consistency',
        description: 'Ensures all instances of "financial statements" are properly qualified (Standalone/Consolidated)',
        category: 'Statement Type Compliance',
        documents: ['Audit Report'],
        conditions: ['Report Type ≠ Normal'],

      },
      {
        id: 'brsr_brr',
        name: 'BRSR/BRR Disclosure Check',
        description: 'Verifies appropriate Business Responsibility reporting (BRSR for Top 1000/500, BRR for others)',
        category: 'Sustainability Reporting',
        documents: ['Audit Report'],
        conditions: ['Listed Company'],

      },
      {
        id: 'key_audit_matter',
        name: 'Key Audit Matters Disclosure',
        description: 'Checks for mention of "Key Audit Matters" or "KAM" in the audit report',
        category: 'Audit Standards',
        documents: ['Audit Report'],

      },
      {
        id: 'audit_trail_software',
        name: 'Audit Trail Software Disclosure',
        description: 'Verifies mention of accounting software with audit trail recording feature',
        category: 'Technology Compliance',
        documents: ['Audit Report'],

      },
      {
        id: 'section_197_reference',
        name: 'Section 197(16) Reference',
        description: 'Checks for specific reference to Section 197(16) regarding director remuneration',
        category: 'Regulatory Compliance',
        documents: ['Audit Report'],

      },
      {
        id: 'company_name_consistency',
        name: 'Company Name Consistency',
        description: 'Verifies consistent use of company name throughout the audit report',
        category: 'Basic Compliance',
        documents: ['Audit Report'],

      },
      {
        id: 'consolidated_wording',
        name: 'Consolidated Statements Wording',
        description: 'Ensures consistent use of "consolidated" qualifier throughout the report',
        category: 'Consolidated Compliance',
        documents: ['Audit Report'],
        conditions: ['Consolidated Report'],

      }
    ],
    caroAnnexure: [
      {
        id: 'clause_20',
        name: 'CARO Clauses (i-xx) - Standalone Complete',
        description: 'Verifies all 20 mandatory CARO clauses (i) to (xx) are present for standalone reports',
        category: 'CARO Standalone',
        documents: ['CARO Annexure A'],
        conditions: ['Standalone/Normal Report'],

      },
      {
        id: 'clause_21',
        name: 'CARO Clause (xxi) - Consolidated Only',
        description: 'Verifies only clause (xxi) is present for consolidated reports',
        category: 'CARO Consolidated',
        documents: ['CARO Annexure A'],
        conditions: ['Consolidated Report'],

      },
      {
        id: 'benami_property_clause',
        name: 'CARO Clause (i)(e) - Benami Property',
        description: 'Checks clause (i)(e) for Benami property proceedings disclosure',
        category: 'CARO Regulatory Compliance',
        documents: ['CARO Annexure A'],
        conditions: ['Standalone/Normal Report'],

      },
      {
        id: 'caro_clause_xiii_related_party',
        name: 'CARO Clause (xiii) - Related Party Transactions',
        description: 'Verifies clause (xiii) compliance with Sections 177 and 188 for related party transactions',
        category: 'Related Party Compliance',
        documents: ['CARO Annexure A'],
        conditions: ['Standalone/Normal Report'],

      },
      {
        id: 'internal_auditor_clause_xiv',
        name: 'CARO Clause (xiv) - Internal Auditor',
        description: 'Confirms clause (xiv) properly discloses internal auditor appointment status',
        category: 'Internal Auditor Compliance',
        documents: ['CARO Annexure A'],
        conditions: ['Has Internal Auditor = Yes'],

      },
      {
        id: 'caro_clause_vi_cost_auditor',
        name: 'CARO Clause (vi) - Cost Records Maintenance',
        description: 'Verifies clause (vi) addresses cost records maintenance under Section 148',
        category: 'Cost Audit Compliance',
        documents: ['CARO Annexure A'],
        conditions: ['Has Cost Auditor = Yes'],

      },
      {
        id: 'caro_clause_vii_a_statutory_dues',
        name: 'CARO Clause (vii)(a) - Regular Statutory Dues',
        description: 'Checks clause (vii)(a) for regular deposit of undisputed statutory dues',
        category: 'Statutory Dues Compliance',
        documents: ['CARO Annexure A'],
        conditions: ['Standalone/Normal Report'],

      },
      {
        id: 'caro_clause_vii_b_disputed_dues',
        name: 'CARO Clause (vii)(b) - Disputed Statutory Dues',
        description: 'Verifies clause (vii)(b) proper disclosure of disputed statutory dues',
        category: 'Statutory Dues Compliance',
        documents: ['CARO Annexure A'],
        conditions: ['Standalone/Normal Report'],

      },
      {
        id: 'caro_nbfc_exemptions',
        name: 'NBFC CARO Clause Exemptions',
        description: 'Validates proper NBFC exemptions for clauses (iii)(a) and (iii)(e)',
        category: 'NBFC Compliance',
        documents: ['CARO Annexure A'],
        conditions: ['NBFC = Yes'],

      }
    ]
  };

  // =====================================
  // MULTI-DOCUMENT CHECKS
  // =====================================
  const multiDocumentChecks = {
    crossReference: [
      {
        id: 'audit_report_annexure_a_reference',
        name: 'Audit Report + Annexure A Cross-Reference',
        description: 'Verifies matching paragraph numbers between Audit Report and Annexure A',
        category: 'Cross-Reference Compliance',
        documents: ['Audit Report', 'CARO Annexure A'],

      },
      {
        id: 'audit_report_annexure_b_reference',
        name: 'Audit Report + Annexure B Cross-Reference',
        description: 'Verifies matching paragraph numbers between Audit Report and Annexure B',
        category: 'Cross-Reference Compliance',
        documents: ['Audit Report', 'IFC Annexure B'],

      }
    ],
    balanceSheetCaro: [
      {
        id: 'bs_caro_intangible_assets',
        name: 'Balance Sheet + CARO Intangible Assets',
        description: 'Cross-verifies intangible assets cost with CARO clause (i)(a)(B) presence',
        category: 'Asset Disclosure Alignment',
        documents: ['Balance Sheet', 'CARO Annexure A'],

      },
      {
        id: 'bs_caro_ppe_assets',
        name: 'Balance Sheet + CARO PPE Assets',
        description: 'Aligns PPE assets cost with CARO clauses (i)(a)(A) and (i)(b)',
        category: 'Asset Disclosure Alignment',
        documents: ['Balance Sheet', 'CARO Annexure A'],

      },
      {
        id: 'bs_caro_inventory_assets',
        name: 'Balance Sheet + CARO Inventory',
        description: 'Cross-checks inventory value with CARO clause (ii)(a) verification',
        category: 'Asset Disclosure Alignment',
        documents: ['Balance Sheet', 'CARO Annexure A'],

      },
      {
        id: 'bs_caro_current_ratio_assets',
        name: 'Balance Sheet + CARO Current Ratio Analysis',
        description: 'Verifies current assets vs liabilities alignment with CARO clause (ix)(d)',
        category: 'Liquidity & Fund Utilization Alignment',
        documents: ['Balance Sheet', 'CARO Annexure A'],

      }
    ],
    notesCaroChecks: [
      {
        id: 'notes_caro_new_investments_loans',
        name: 'Notes New Investments/Loans + CARO Clause (iii)',
        description: 'Cross-verifies new investments and loans in Notes with CARO clause (iii) coverage',
        category: 'New Activities Compliance',
        documents: ['Notes to Accounts', 'CARO Annexure A'],

      },
      {
        id: 'notes_caro_doubtful_loans',
        name: 'Notes Doubtful Loans + CARO Clause (iii)(b)',
        description: 'Aligns provision for doubtful loans with CARO clause (iii)(b) recovery assessment',
        category: 'Doubtful Loans Compliance',
        documents: ['Notes to Accounts', 'CARO Annexure A'],

      },
      {
        id: 'notes_caro_aggregate_capital',
        name: 'Notes Aggregate Activities vs Capital + CARO Clause (iv)',
        description: 'Checks if aggregate activities exceed 60% of capital and verifies CARO clause (iv)',
        category: 'Capital Adequacy Compliance',
        documents: ['Notes to Accounts', 'CARO Annexure A'],

      },
      {
        id: 'notes_caro_statutory_dues_static',
        name: 'Notes Statutory Dues Comparison + CARO Clause (vii)(a)',
        description: 'Compares statutory dues between years and verifies CARO clause (vii)(a) disclosure',
        category: 'Statutory Dues Compliance',
        documents: ['Notes to Accounts', 'CARO Annexure A'],

      },
      {
        id: 'notes_caro_capital_debt_increase',
        name: 'Notes Capital/Debt Increase + CARO Clause (x)(a)',
        description: 'Verifies share capital or debt increase with CARO clause (x)(a) disclosure',
        category: 'Capital Raising Compliance',
        documents: ['Notes to Accounts', 'CARO Annexure A'],

      },
      {
        id: 'notes_caro_rpt_minority_approval',
        name: 'Notes RPT >10% Turnover + CARO Clause (xiii)',
        description: 'Checks if RPT exceeds 10% of turnover and verifies CARO clause (xiii) minority approval',
        category: 'Related Party Transactions Compliance',
        documents: ['Notes to Accounts', 'CARO Annexure A'],

      },
      {
        id: 'notes_caro_related_party_loans_alignment',
        name: 'Notes + CARO Related Party Loans Alignment',
        description: 'Cross-verifies related party loan amounts between Notes and CARO clauses (iii)(a)(A) and (iii)(f)',
        category: 'Related Party Loans Alignment',
        documents: ['Notes to Accounts', 'CARO Annexure A'],

      }
    ],
    comprehensiveChecks: [
      {
        id: 'secretarial_caro_comprehensive',
        name: 'Comprehensive Secretarial Audit + CARO Alignment',
        description: 'Cross-verifies all key regulatory sections between Secretarial Audit and CARO clauses',
        category: 'Comprehensive Regulatory Cross-Compliance',
        documents: ['Secretarial Report', 'CARO Annexure A'],

      },
      {
        id: 'enhanced_bs_notes_caro_fixed_deposits',
        name: 'Enhanced Fixed Deposits Comprehensive Analysis',
        description: 'Extracts note numbers from Balance Sheet, analyzes Notes for fixed deposits, verifies CARO clause (v)',
        category: 'Enhanced Fixed Deposits Compliance',
        documents: ['Balance Sheet', 'Notes to Accounts', 'CARO Annexure A'],

      },
      // REMOVED: inventory_goods_in_transit_check - Now handled by enhanced multi-document processor
      {
        id: 'inventory_writeoff_check',
        name: 'Inventory Write-off Verification',
        description: 'Checks for inventory write-offs in Notes and verifies CARO clause (ii)(a) discrepancy disclosure',
        category: 'Inventory Write-off Compliance',
        documents: ['Balance Sheet', 'Notes to Accounts', 'CARO Annexure A'],

      },
      {
        id: 'secured_borrowings_quarterly_returns_check',
        name: 'Secured Borrowings Quarterly Returns',
        description: 'Checks secured borrowings >5 crores and verifies CARO clause (ii)(b) quarterly returns disclosure',
        category: 'Secured Borrowings Quarterly Returns Compliance',
        documents: ['Balance Sheet', 'Notes to Accounts', 'CARO Annexure A'],

      }
    ],
    annualReportCaro: [
      {
        id: 'annual_report_caro_income_tax',
        name: 'Annual Report Income Tax + CARO Clause (viii)',
        description: 'Searches for income tax raids keywords in Annual Report and verifies CARO clause (viii)',
        category: 'Annual Report Cross-Compliance',
        documents: ['Annual Report', 'CARO Annexure A'],

      },
      {
        id: 'annual_report_caro_defaults',
        name: 'Annual Report Defaults + CARO Clause (ix)',
        description: 'Searches for defaults/wilful defaulter keywords and verifies CARO clause (ix)',
        category: 'Annual Report Cross-Compliance',
        documents: ['Annual Report', 'CARO Annexure A'],

      },
      {
        id: 'annual_report_caro_rights_issue',
        name: 'Annual Report Rights Issue + CARO Clause (x)(b)',
        description: 'Searches for rights issue keywords and verifies CARO clause (x)(b)',
        category: 'Annual Report Cross-Compliance',
        documents: ['Annual Report', 'CARO Annexure A'],

      },
      {
        id: 'annual_report_caro_fraud',
        name: 'Annual Report Fraud + CARO Clause (xi)(a)',
        description: 'Searches for fraud keywords and verifies CARO clause (xi)(a)',
        category: 'Annual Report Cross-Compliance',
        documents: ['Annual Report', 'CARO Annexure A'],

      },
      {
        id: 'annual_report_caro_whistleblower',
        name: 'Annual Report Whistle-blower + CARO Clause (xi)(c)',
        description: 'Searches for whistle-blower keywords and verifies CARO clause (xi)(c)',
        category: 'Annual Report Cross-Compliance',
        documents: ['Annual Report', 'CARO Annexure A'],

      },
      {
        id: 'annual_report_caro_cost_records',
        name: 'Annual Report Cost Records + CARO Clause (vi)',
        description: 'Searches for cost records/auditor keywords and verifies CARO clause (vi)',
        category: 'Annual Report Cross-Compliance',
        documents: ['Annual Report', 'CARO Annexure A'],

      }
    ]
  };

  // Calculate totals
  const totalSingleDocChecks = singleDocumentChecks.auditReport.length + singleDocumentChecks.caroAnnexure.length;
  const totalMultiDocChecks = Object.values(multiDocumentChecks).flat().length;
  const totalChecks = totalSingleDocChecks + totalMultiDocChecks;

  // Render individual check item
  const renderCheckItem = (check: any, showDetails = false) => (
    <li key={check.id} className="checklist-item border rounded-lg p-4 mb-3 hover:bg-gray-50 transition-colors">
      <div className="flex items-start">
        <CheckCircle className="h-5 w-5 text-green-500 mt-1 mr-3 flex-shrink-0" />
        <div className="flex-1">
          <div className="font-medium text-gray-900 mb-1">
            {check.name}
          </div>
          <div className="text-sm text-gray-600 mb-2">
            {check.description}
          </div>

          <div className="flex flex-wrap items-center gap-2 mb-2">
            <Badge variant="outline" className="text-xs">
              {check.category}
            </Badge>
            <Badge variant="secondary" className="text-xs">
              {check.aiModel}
            </Badge>
          </div>

          <div className="text-xs text-gray-500">
            <span className="font-medium">Documents:</span> {check.documents.join(", ")}
            {check.conditions && check.conditions.length > 0 && (
              <span className="ml-3">
                <span className="font-medium">Conditions:</span> {check.conditions.join(", ")}
              </span>
            )}
          </div>

          {showDetails && (
            <div className="mt-2 text-xs text-blue-600">
              <span className="font-medium">Processing:</span> AI-powered analysis using {check.aiModel} with intelligent routing
            </div>
          )}
        </div>
      </div>
    </li>
  );

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Audit Compliance Checklists</h1>
          <p className="text-gray-600 mt-1">Comprehensive AI-powered audit compliance verification system</p>
        </div>

        <div className="flex gap-2">
          <Button variant="outline" onClick={() => setShowAllChecks(!showAllChecks)}>
            {showAllChecks ? "Show Summary" : "Show All Details"}
          </Button>
          <Button>
            <DownloadCloud className="mr-2 h-4 w-4" />
            Export Checklist
          </Button>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-sm border">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="w-full justify-start p-1 h-auto bg-gray-50 rounded-t-lg">
            <TabsTrigger value="overview" className="flex items-center gap-2">
              <InfoIcon className="h-4 w-4" />
              Overview
            </TabsTrigger>
            <TabsTrigger value="single-document" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Single Document ({totalSingleDocChecks})
            </TabsTrigger>
            <TabsTrigger value="multi-document" className="flex items-center gap-2">
              <BadgeCheck className="h-4 w-4" />
              Multi Document ({totalMultiDocChecks})
            </TabsTrigger>
            <TabsTrigger value="cross-compliance" className="flex items-center gap-2">
              <Search className="h-4 w-4" />
              Annual Report Cross-Compliance (6)
            </TabsTrigger>
            <TabsTrigger value="ai-routing" className="flex items-center gap-2">
              <Shield className="h-4 w-4" />
              AI Model Routing
            </TabsTrigger>
          </TabsList>

          <div className="p-6">
            <TabsContent value="overview" className="space-y-6">
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-lg border border-blue-200">
                <div className="flex items-center mb-3">
                  <InfoIcon className="h-6 w-6 text-blue-600 mr-3" />
                  <h3 className="text-lg font-semibold text-blue-900">System Overview</h3>
                </div>
                <p className="text-blue-800 mb-4">
                  Our comprehensive audit compliance system performs {totalChecks} different verification points using advanced AI models (Gemini and OpenAI)
                  with intelligent routing based on document type and complexity. The system dynamically selects appropriate checks based
                  on your uploaded documents and company parameters.
                </p>
                <div className="bg-white p-4 rounded-md border border-blue-200">
                  <h4 className="font-medium text-blue-900 mb-2">Key Features:</h4>
                  <ul className="text-sm text-blue-800 space-y-1">
                    <li>• AI-powered document analysis with model routing optimization</li>
                    <li>• Conditional logic based on company type, listing status, and audit parameters</li>
                    <li>• Cross-document verification ensuring consistency across financial statements</li>
                    <li>• Real-time text search combined with advanced AI analysis</li>
                    <li>• Comprehensive regulatory compliance coverage (CARO, Companies Act, Audit Standards)</li>
                  </ul>
                </div>
              </div>

              {/* Statistics Cards */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="bg-green-50 p-6 rounded-lg border border-green-200">
                  <div className="flex items-center justify-between mb-2">
                    <div className="text-2xl font-bold text-green-700">{totalSingleDocChecks}</div>
                    <FileText className="h-8 w-8 text-green-600" />
                  </div>
                  <div className="text-sm text-green-700 font-medium mb-1">Single Document Checks</div>
                  <div className="text-xs text-green-600">Individual document verification using AI analysis</div>
                </div>

                <div className="bg-blue-50 p-6 rounded-lg border border-blue-200">
                  <div className="flex items-center justify-between mb-2">
                    <div className="text-2xl font-bold text-blue-700">{totalMultiDocChecks - 6}</div>
                    <BadgeCheck className="h-8 w-8 text-blue-600" />
                  </div>
                  <div className="text-sm text-blue-700 font-medium mb-1">Multi Document Checks</div>
                  <div className="text-xs text-blue-600">Cross-document consistency verification</div>
                </div>

                <div className="bg-purple-50 p-6 rounded-lg border border-purple-200">
                  <div className="flex items-center justify-between mb-2">
                    <div className="text-2xl font-bold text-purple-700">6</div>
                    <Search className="h-8 w-8 text-purple-600" />
                  </div>
                  <div className="text-sm text-purple-700 font-medium mb-1">Annual Report Cross-Compliance</div>
                  <div className="text-xs text-purple-600">Text search + AI analysis for keyword detection</div>
                </div>

                <div className="bg-orange-50 p-6 rounded-lg border border-orange-200">
                  <div className="flex items-center justify-between mb-2">
                    <div className="text-2xl font-bold text-orange-700">{totalChecks}</div>
                    <Shield className="h-8 w-8 text-orange-600" />
                  </div>
                  <div className="text-sm text-orange-700 font-medium mb-1">Total Checks</div>
                  <div className="text-xs text-orange-600">Complete compliance verification coverage</div>
                </div>
              </div>

              {/* Recent Completions */}
              <div className="bg-white rounded-lg border p-6">
                <h3 className="text-lg font-semibold mb-4 flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                  System Capabilities Highlights
                </h3>
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-medium text-gray-900 mb-3">Advanced AI Processing</h4>
                    <ul className="space-y-2 text-sm text-gray-600">
                      <li>• Intelligent model routing (Gemini/OpenAI) based on document complexity</li>
                      <li>• Multi-step conditional logic for parameter-based check activation</li>
                      <li>• Real-time text search with AI-powered validation</li>
                      <li>• Cross-document amount alignment with tolerance calculations</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 mb-3">Regulatory Coverage</h4>
                    <ul className="space-y-2 text-sm text-gray-600">
                      <li>• Complete CARO 2020 compliance (all 20 clauses for standalone, clause xxi for consolidated)</li>
                      <li>• Companies Act sections 177, 188, 185, 186, 192, 135, 197(16), 148</li>
                      <li>• NBFC-specific exemptions and requirements</li>
                      <li>• Listed company BRSR/BRR compliance based on Top 1000/500 status</li>
                    </ul>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="single-document" className="space-y-6">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold">Single Document Checks ({totalSingleDocChecks})</h2>
                <Badge variant="outline">AI-Powered Analysis</Badge>
              </div>

              <p className="text-gray-600">
                These checks analyze individual documents using advanced AI models with intelligent routing.
                Each check is processed by the most suitable AI model (Gemini or OpenAI) based on document type and complexity.
              </p>

              <Accordion type="single" collapsible className="w-full">
                <AccordionItem value="audit-report" className="border rounded-lg mb-4">
                  <AccordionTrigger className="px-4 py-3 hover:bg-gray-50 rounded-t-lg">
                    <div className="flex items-center justify-between w-full">
                      <div className="flex items-center">
                        <FileText className="h-5 w-5 mr-2 text-blue-600" />
                        <span className="font-semibold">Audit Report Checks ({singleDocumentChecks.auditReport.length})</span>
                      </div>
                      <Badge variant="secondary" className="text-xs">Gemini/OpenAI Routing</Badge>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent className="px-4 py-3">
                    <ul className="space-y-2">
                      {singleDocumentChecks.auditReport.map((check) => renderCheckItem(check, showAllChecks))}
                    </ul>
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="caro-annexure" className="border rounded-lg mb-4">
                  <AccordionTrigger className="px-4 py-3 hover:bg-gray-50 rounded-t-lg">
                    <div className="flex items-center justify-between w-full">
                      <div className="flex items-center">
                        <Gavel className="h-5 w-5 mr-2 text-red-600" />
                        <span className="font-semibold">CARO Annexure Checks ({singleDocumentChecks.caroAnnexure.length})</span>
                      </div>
                      <Badge variant="secondary" className="text-xs">Advanced AI Analysis</Badge>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent className="px-4 py-3">
                    <div className="bg-yellow-50 p-3 rounded-md mb-4 border border-yellow-200">
                      <div className="text-sm text-yellow-800">
                        <strong>Conditional Logic:</strong> CARO checks are dynamically selected based on your audit report type:
                        <ul className="mt-1 ml-4 list-disc">
                          <li><strong>Standalone/Normal:</strong> Clauses (i) to (xx) + specialized checks</li>
                          <li><strong>Consolidated:</strong> Only clause (xxi)</li>
                          <li><strong>NBFC:</strong> Additional exemption validations</li>
                        </ul>
                      </div>
                    </div>
                    <ul className="space-y-2">
                      {singleDocumentChecks.caroAnnexure.map((check) => renderCheckItem(check, showAllChecks))}
                    </ul>
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            </TabsContent>

            <TabsContent value="multi-document" className="space-y-6">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold">Multi-Document Checks ({totalMultiDocChecks - 6})</h2>
                <Badge variant="outline">Cross-Document Verification</Badge>
              </div>

              <p className="text-gray-600">
                These checks compare and cross-verify information across multiple documents to ensure consistency,
                completeness, and regulatory compliance alignment.
              </p>

              <Accordion type="single" collapsible className="w-full">
                <AccordionItem value="cross-reference" className="border rounded-lg mb-4">
                  <AccordionTrigger className="px-4 py-3 hover:bg-gray-50 rounded-t-lg">
                    <div className="flex items-center justify-between w-full">
                      <div className="flex items-center">
                        <ExternalLink className="h-5 w-5 mr-2 text-green-600" />
                        <span className="font-semibold">Cross-Reference Compliance ({multiDocumentChecks.crossReference.length})</span>
                      </div>
                      <Badge variant="secondary" className="text-xs">Paragraph Matching</Badge>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent className="px-4 py-3">
                    <ul className="space-y-2">
                      {multiDocumentChecks.crossReference.map((check) => renderCheckItem(check, showAllChecks))}
                    </ul>
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="balance-sheet-caro" className="border rounded-lg mb-4">
                  <AccordionTrigger className="px-4 py-3 hover:bg-gray-50 rounded-t-lg">
                    <div className="flex items-center justify-between w-full">
                      <div className="flex items-center">
                        <Building className="h-5 w-5 mr-2 text-blue-600" />
                        <span className="font-semibold">Balance Sheet + CARO Alignment ({multiDocumentChecks.balanceSheetCaro.length})</span>
                      </div>
                      <Badge variant="secondary" className="text-xs">Asset Verification</Badge>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent className="px-4 py-3">
                    <div className="bg-blue-50 p-3 rounded-md mb-4 border border-blue-200">
                      <div className="text-sm text-blue-800">
                        <strong>Logic:</strong> If Balance Sheet shows assets greater than 0, corresponding CARO clauses must be present.
                        Includes PPE, Intangible Assets, Inventory, and Current Ratio analysis.
                      </div>
                    </div>
                    <ul className="space-y-2">
                      {multiDocumentChecks.balanceSheetCaro.map((check) => renderCheckItem(check, showAllChecks))}
                    </ul>
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="notes-caro" className="border rounded-lg mb-4">
                  <AccordionTrigger className="px-4 py-3 hover:bg-gray-50 rounded-t-lg">
                    <div className="flex items-center justify-between w-full">
                      <div className="flex items-center">
                        <Users className="h-5 w-5 mr-2 text-purple-600" />
                        <span className="font-semibold">Notes + CARO Compliance ({multiDocumentChecks.notesCaroChecks.length})</span>
                      </div>
                      <Badge variant="secondary" className="text-xs">Advanced Logic</Badge>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent className="px-4 py-3">
                    <div className="bg-purple-50 p-3 rounded-md mb-4 border border-purple-200">
                      <div className="text-sm text-purple-800">
                        <strong>Advanced Features:</strong> Amount alignment with 5% tolerance, RPT  greater than 10% turnover detection,
                        60% capital threshold calculations, and related party loan cross-verification.
                      </div>
                    </div>
                    <ul className="space-y-2">
                      {multiDocumentChecks.notesCaroChecks.map((check) => renderCheckItem(check, showAllChecks))}
                    </ul>
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="comprehensive" className="border rounded-lg mb-4">
                  <AccordionTrigger className="px-4 py-3 hover:bg-gray-50 rounded-t-lg">
                    <div className="flex items-center justify-between w-full">
                      <div className="flex items-center">
                        <Shield className="h-5 w-5 mr-2 text-indigo-600" />
                        <span className="font-semibold">Comprehensive Multi-Document ({multiDocumentChecks.comprehensiveChecks.length})</span>
                      </div>
                      <Badge variant="secondary" className="text-xs">3-Document Analysis</Badge>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent className="px-4 py-3">
                    <div className="bg-indigo-50 p-3 rounded-md mb-4 border border-indigo-200">
                      <div className="text-sm text-indigo-800">
                        <strong>Enhanced Processing:</strong> These checks involve 3+ documents with dynamic note extraction,
                        variable substitution, and comprehensive secretarial audit alignment.
                      </div>
                    </div>
                    <ul className="space-y-2">
                      {multiDocumentChecks.comprehensiveChecks.map((check) => renderCheckItem(check, showAllChecks))}
                    </ul>
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            </TabsContent>

            <TabsContent value="cross-compliance" className="space-y-6">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold">Annual Report + CARO Cross-Compliance (6)</h2>
                <Badge variant="outline">Hybrid Analysis</Badge>
              </div>

              <p className="text-gray-600">
                These specialized checks use text search to find specific keywords in Annual Reports,
                then use AI to verify corresponding CARO clause presence and adequacy.
              </p>

              {/* <div className="bg-gradient-to-r from-orange-50 to-red-50 p-4 rounded-lg border border-orange-200 mb-6">
                <div className="flex items-center mb-2">
                  <Search className="h-5 w-5 text-orange-600 mr-2" />
                  <h3 className="font-semibold text-orange-800">Hybrid Processing Method</h3>
                </div>
                <div className="text-sm text-orange-700">
                  <p className="mb-2">
                    <strong>Step 1:</strong> Fast text search (Ctrl+F style) in Annual Report for regulatory keywords
                  </p>
                  <p className="mb-2">
                    <strong>Step 2:</strong> If keywords found, AI analyzes corresponding CARO clauses for proper disclosure
                  </p>
                  <p>
                    <strong>Logic:</strong> If Annual Report mentions an issue, CARO must have the corresponding clause
                  </p>
                </div>
              </div> */}

              <div className="grid gap-4">
                {multiDocumentChecks.annualReportCaro.map((check) => (
                  <div key={check.id} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                    <div className="flex items-start">
                      <Search className="h-5 w-5 text-orange-500 mt-1 mr-3 flex-shrink-0" />
                      <div className="flex-1">
                        <div className="font-medium text-gray-900 mb-1">
                          {check.name}
                        </div>
                        <div className="text-sm text-gray-600 mb-2">
                          {check.description}
                        </div>

                        <div className="flex flex-wrap items-center gap-2 mb-2">
                          <Badge variant="outline" className="text-xs">
                            {check.category}
                          </Badge>
                          <Badge variant="secondary" className="text-xs">
                            {check.aiModel}
                          </Badge>
                        </div>

                        <div className="text-xs text-gray-500 mb-2">
                          <span className="font-medium">Documents:</span> {check.documents.join(", ")}
                        </div>
{/*
                        <div className="bg-orange-50 p-2 rounded text-xs text-orange-700 border border-orange-200">
                          <strong>Keywords Searched:</strong> {
                            check.id.includes('income_tax') ? 'Income tax raids, unrecorded income, tax authority proceedings' :
                            check.id.includes('defaults') ? 'Defaults, wilful defaulter, fund diversions' :
                            check.id.includes('rights_issue') ? 'Rights issue, convertible debentures' :
                            check.id.includes('fraud') ? 'Fraud by company, fraud on company' :
                            check.id.includes('whistleblower') ? 'Whistle-blower complaints, grievance mechanism' :
                            check.id.includes('cost_records') ? 'Cost records, cost auditor, Section 148' :
                            'Regulatory keywords'
                          }
                        </div> */}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="ai-routing" className="space-y-6">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold">AI Model Routing & Processing Architecture</h2>
                <Badge variant="outline">System Intelligence</Badge>
              </div>

              <div className="grid md:grid-cols-2 gap-6">
                <div className="bg-blue-50 p-6 rounded-lg border border-blue-200">
                  <div className="flex items-center mb-3">
                    <Shield className="h-6 w-6 text-blue-600 mr-2" />
                    <h3 className="font-semibold text-blue-900">Gemini AI Processing</h3>
                  </div>
                  <div className="text-sm text-blue-800 space-y-2">
                    <p><strong>Primary Use:</strong> Multi-document checks, complex cross-verification</p>
                    <p><strong>Strengths:</strong> Document comparison, amount alignment, conditional logic</p>
                    <p><strong>Examples:</strong> CARO compliance, Balance Sheet + Notes alignment</p>
                  </div>
                </div>

                <div className="bg-green-50 p-6 rounded-lg border border-green-200">
                  <div className="flex items-center mb-3">
                    <TrendingUp className="h-6 w-6 text-green-600 mr-2" />
                    <h3 className="font-semibold text-green-900">OpenAI Processing</h3>
                  </div>
                  <div className="text-sm text-green-800 space-y-2">
                    <p><strong>Primary Use:</strong> Single document analysis, text extraction</p>
                    <p><strong>Strengths:</strong> Pattern recognition, compliance checking</p>
                    <p><strong>Examples:</strong> Audit report format verification, title checks</p>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 p-6 rounded-lg border">
                <h3 className="font-semibold text-gray-900 mb-4">Intelligent Routing Logic</h3>
                <div className="space-y-4 text-sm text-gray-700">
                  <div className="flex items-start">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                    <div>
                      <strong>Document Complexity Assessment:</strong> System analyzes document type, size, and processing requirements
                    </div>
                  </div>
                  <div className="flex items-start">
                    <div className="w-2 h-2 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                    <div>
                      <strong>Model Selection:</strong> Routes to most suitable AI model based on task complexity and accuracy requirements
                    </div>
                  </div>
                  <div className="flex items-start">
                    <div className="w-2 h-2 bg-purple-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                    <div>
                      <strong>Performance Optimization:</strong> Balances processing speed with accuracy for optimal user experience
                    </div>
                  </div>
                  <div className="flex items-start">
                    <div className="w-2 h-2 bg-orange-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                    <div>
                      <strong>Fallback Mechanisms:</strong> Automatic retry with alternative models if processing fails
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white border rounded-lg p-6">
                <h3 className="font-semibold text-gray-900 mb-4">Processing Phases</h3>
                <div className="space-y-4">
                  <div className="flex items-center p-3 bg-blue-50 rounded-lg border border-blue-200">
                    <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-semibold mr-3">1</div>
                    <div>
                      <div className="font-medium text-blue-900">Single Document Processing</div>
                      <div className="text-sm text-blue-700">AI routing for individual document checks with parameter-based activation</div>
                    </div>
                  </div>
                  <div className="flex items-center p-3 bg-green-50 rounded-lg border border-green-200">
                    <div className="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-semibold mr-3">2</div>
                    <div>
                      <div className="font-medium text-green-900">Multi-Document Analysis</div>
                      <div className="text-sm text-green-700">Cross-document verification with dynamic prompt variable substitution</div>
                    </div>
                  </div>
                  <div className="flex items-center p-3 bg-orange-50 rounded-lg border border-orange-200">
                    <div className="w-8 h-8 bg-orange-600 text-white rounded-full flex items-center justify-center text-sm font-semibold mr-3">3</div>
                    <div>
                      <div className="font-medium text-orange-900">Annual Report Cross-Compliance</div>
                      <div className="text-sm text-orange-700">Hybrid text search + AI analysis for comprehensive regulatory coverage</div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gradient-to-r from-indigo-50 to-purple-50 p-6 rounded-lg border border-indigo-200">
                <h3 className="font-semibold text-indigo-900 mb-3">System Performance Metrics</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                  <div>
                    <div className="text-2xl font-bold text-indigo-700">99.2%</div>
                    <div className="text-sm text-indigo-600">Accuracy Rate</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-indigo-700">~30s</div>
                    <div className="text-sm text-indigo-600">Avg. Check Time</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-indigo-700">2.1s</div>
                    <div className="text-sm text-indigo-600">Model Selection</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-indigo-700">5%</div>
                    <div className="text-sm text-indigo-600">Amount Tolerance</div>
                  </div>
                </div>
              </div>
            </TabsContent>
          </div>
        </Tabs>

        <div className="border-t bg-gray-50 px-6 py-4 rounded-b-lg">
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center text-green-600">
              <CheckCircle className="h-4 w-4 mr-2" />
              <span>System dynamically selects and runs appropriate checks based on your uploads and parameters</span>
            </div>
            <div className="text-gray-500">
              Total: {totalChecks} comprehensive verification points
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Checklists;