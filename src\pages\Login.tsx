import React, { useState } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  Eye, 
  EyeOff, 
  Shield, 
  Brain, 
  Zap, 
  DollarSign, 
  PieChart, 
  TrendingUp, 
  Building, 
  Calculator,
  Sparkles,
  ArrowLeft,
  CheckCircle,
  Lock,
  Mail
} from "lucide-react";

const Login = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const { login } = useAuth();
  const navigate = useNavigate();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");

    try {
      setLoading(true);
      await login(email, password);
      navigate("/dashboard");
    } catch (error: any) {
      setError(error.message || "Failed to log in");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-slate-900 text-white overflow-hidden relative">
      {/* Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900"></div>
      
      {/* Financial background pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%2359B6F4' fill-opacity='0.4'%3E%3Cpath d='M30 30c0-11.046 8.954-20 20-20s20 8.954 20 20-8.954 20-20 20-20-8.954-20-20zm0 0c0 11.046-8.954 20-20 20S-10 41.046-10 30s8.954-20 20-20 20 8.954 20 20z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          backgroundSize: '120px 120px'
        }}></div>
      </div>

      {/* Floating financial icons */}
      <div className="absolute inset-0 pointer-events-none">
        <DollarSign className="absolute top-20 right-20 h-16 w-16 text-green-400/20 animate-pulse" style={{animationDelay: '0s'}} />
        <PieChart className="absolute top-40 left-20 h-12 w-12 text-blue-400/20 animate-pulse" style={{animationDelay: '1s'}} />
        <Calculator className="absolute bottom-40 right-40 h-14 w-14 text-purple-400/20 animate-pulse" style={{animationDelay: '2s'}} />
        <TrendingUp className="absolute bottom-60 left-40 h-10 w-10 text-emerald-400/20 animate-pulse" style={{animationDelay: '3s'}} />
        <Building className="absolute top-2/3 right-1/3 h-12 w-12 text-cyan-400/20 animate-pulse" style={{animationDelay: '4s'}} />
      </div>

      {/* Floating orbs */}
      <div className="absolute top-20 right-20 w-96 h-96 bg-blue-500/20 rounded-full blur-3xl animate-pulse"></div>
      <div className="absolute bottom-20 left-20 w-80 h-80 bg-purple-500/20 rounded-full blur-3xl animate-pulse" style={{animationDelay: '2s'}}></div>

      {/* Header */}
      <div className="relative z-10 p-6">
        <div className="flex items-center justify-between">
          <Link to="/" className="flex items-center gap-3 group">
            <ArrowLeft className="h-6 w-6 text-white/70 group-hover:text-white transition-colors" />
            <div className="flex items-center gap-3">
              <div className="h-10 w-10 rounded-xl bg-gradient-to-br from-blue-600 to-purple-600 flex items-center justify-center text-white font-bold">
                P
              </div>
              <span className="text-xl font-bold text-white">PKF Audit AI</span>
            </div>
          </Link>
          
          <div className="flex items-center gap-4">
            <span className="text-white/70">New to PKF Audit AI?</span>
            <Link to="/register">
              <Button variant="outline" className="bg-white/15 border-white/70 text-white hover:bg-white/30 hover:border-white font-semibold backdrop-blur-sm">
                Sign Up
              </Button>
            </Link>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 flex items-center justify-center min-h-[calc(100vh-120px)] px-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center max-w-7xl w-full">
          
          {/* Left side - Branding */}
          <div className="space-y-8 text-center lg:text-left">
            <div className="inline-flex items-center px-6 py-3 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 text-white">
              <Sparkles className="h-5 w-5 mr-2 text-blue-300" />
              <span className="font-medium">Welcome Back to PKF Audit AI</span>
            </div>

            <div className="space-y-6">
              <h1 className="text-5xl md:text-6xl lg:text-7xl font-black leading-none">
                <span className="block bg-gradient-to-r from-white via-blue-200 to-white bg-clip-text text-transparent">
                  Welcome
                </span>
                <span className="block bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent">
                  Back
                </span>
              </h1>

              <p className="text-xl text-white/90 leading-relaxed max-w-2xl">
                Sign in to access your AI-powered financial audit dashboard and continue 
                transforming your audit workflow with <span className="text-green-400 font-semibold">cutting-edge technology</span>.
              </p>
            </div>

            {/* Feature highlights */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-2xl">
              <div className="flex items-center gap-3 bg-white/5 backdrop-blur-sm border border-white/20 rounded-2xl p-4">
                <div className="h-12 w-12 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl flex items-center justify-center">
                  <Shield className="h-6 w-6 text-white" />
                </div>
                <div>
                  <div className="text-white font-semibold">Secure Login</div>
                  <div className="text-white/60 text-sm">Bank-grade security</div>
                </div>
              </div>

              <div className="flex items-center gap-3 bg-white/5 backdrop-blur-sm border border-white/20 rounded-2xl p-4">
                <div className="h-12 w-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
                  <Brain className="h-6 w-6 text-white" />
                </div>
                <div>
                  <div className="text-white font-semibold">AI-Powered</div>
                  <div className="text-white/60 text-sm">Smart analysis</div>
                </div>
              </div>
            </div>
          </div>

          {/* Right side - Login Form */}
          <div className="flex justify-center lg:justify-end">
            <Card className="w-full max-w-md bg-white/5 backdrop-blur-xl border border-white/20 shadow-2xl">
              <CardHeader className="space-y-1 text-center">
                <div className="mx-auto h-16 w-16 bg-gradient-to-br from-blue-600 to-purple-600 rounded-2xl flex items-center justify-center mb-4">
                  <Lock className="h-8 w-8 text-white" />
                </div>
                <CardTitle className="text-3xl font-bold text-white">
                  Login to Your Account
                </CardTitle>
                <CardDescription className="text-white/70">
                  Enter your email and password to access your account
                </CardDescription>
              </CardHeader>
              
              <CardContent>
                {error && (
                  <div className="bg-red-500/20 border border-red-500/30 text-red-200 p-4 rounded-xl mb-6 backdrop-blur-sm">
                    <div className="flex items-center gap-2">
                      <div className="h-5 w-5 bg-red-500 rounded-full flex items-center justify-center flex-shrink-0">
                        <span className="text-white text-xs">!</span>
                      </div>
                      <span className="text-sm">{error}</span>
                    </div>
                  </div>
                )}
                
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="space-y-2">
                    <Label htmlFor="email" className="text-white/90 font-medium">Email</Label>
                    <div className="relative">
                      <Mail className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-white/50" />
                      <Input
                        id="email"
                        type="email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        placeholder="<EMAIL>"
                        className="pl-12 bg-white/10 border-white/20 text-white placeholder-white/50 focus:border-blue-400 focus:ring-2 focus:ring-blue-400/20 rounded-xl h-12"
                        required
                      />
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="password" className="text-white/90 font-medium">Password</Label>
                      <Link
                        to="/forgot-password"
                        className="text-sm text-blue-400 hover:text-blue-300 transition-colors"
                      >
                        Forgot password?
                      </Link>
                    </div>
                    <div className="relative">
                      <Lock className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-white/50" />
                      <Input
                        id="password"
                        type={showPassword ? "text" : "password"}
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        placeholder="••••••••"
                        className="pl-12 pr-12 bg-white/10 border-white/20 text-white placeholder-white/50 focus:border-blue-400 focus:ring-2 focus:ring-blue-400/20 rounded-xl h-12"
                        required
                      />
                      <button
                        type="button"
                        className="absolute right-4 top-1/2 transform -translate-y-1/2 text-white/50 hover:text-white/70 transition-colors"
                        onClick={() => setShowPassword(!showPassword)}
                      >
                        {showPassword ? (
                          <EyeOff className="h-5 w-5" />
                        ) : (
                          <Eye className="h-5 w-5" />
                        )}
                      </button>
                    </div>
                  </div>
                  
                  <Button 
                    type="submit"
                    className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-xl h-12 text-base font-semibold shadow-lg transform hover:scale-[1.02] transition-all duration-200" 
                    disabled={loading}
                  >
                    {loading ? "Logging in..." : "Login"}
                  </Button>
                </form>
              </CardContent>
              
              <CardFooter className="flex justify-center border-t border-white/10 pt-6">
                <p className="text-sm text-white/70">
                  Don't have an account?{" "}
                  <Link to="/register" className="text-blue-400 hover:text-blue-300 font-semibold transition-colors">
                    Sign up
                  </Link>
                </p>
              </CardFooter>
            </Card>
          </div>
        </div>
      </div>

      {/* Trust indicators */}
      <div className="relative z-10 py-8">
        <div className="max-w-7xl mx-auto px-6">
          <div className="flex items-center justify-center gap-8 text-center">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-400" />
              <span className="text-white/70 text-sm">Bank-Grade Security</span>
            </div>
            <div className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-blue-400" />
              <span className="text-white/70 text-sm">SOC 2 Compliant</span>
            </div>
            <div className="flex items-center gap-2">
              <Zap className="h-5 w-5 text-yellow-400" />
              <span className="text-white/70 text-sm">Lightning Fast</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;