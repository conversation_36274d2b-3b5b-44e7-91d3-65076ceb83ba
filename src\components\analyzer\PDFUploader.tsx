
import React, { useCallback, useState } from "react";
import { useDropzone } from "react-dropzone";
import { FileText, Upload, X } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface PDFUploaderProps {
  onFileChange: (file: File | null) => void;
  label: string;
  description?: string;
  className?: string;
  value?: File | null;
}

const PDFUploader: React.FC<PDFUploaderProps> = ({
  onFileChange,
  label,
  description,
  className,
  value,
}) => {
  const [error, setError] = useState<string | null>(null);

  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      setError(null);
      if (acceptedFiles.length === 0) {
        return;
      }

      const file = acceptedFiles[0];

      // Validate file type
      if (file.type !== "application/pdf") {
        setError("Only PDF files are allowed");
        return;
      }

      // Validate file size (10MB max)
      const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
      if (file.size > MAX_FILE_SIZE) {
        setError(`File size exceeds the maximum limit of 10MB. Current size: ${(file.size / (1024 * 1024)).toFixed(2)}MB`);
        return;
      }

      onFileChange(file);
    },
    [onFileChange]
  );

  const { getRootProps, getInputProps, isDragActive, isDragAccept, isDragReject } = useDropzone({
    onDrop,
    accept: {
      "application/pdf": [".pdf"],
    },
    maxFiles: 1,
  });

  const removeFile = () => {
    onFileChange(null);
  };

  return (
    <div className={cn("flex flex-col", className)}>
      <div className="text-sm font-medium mb-2">{label}</div>
      {value ? (
        <div className="border rounded-lg p-4 bg-white">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <FileText className="h-8 w-8 text-primary" />
              <div>
                <div className="font-medium">{value.name}</div>
                <div className="text-sm text-gray-500">
                  {(value.size / 1024 / 1024).toFixed(2)} MB
                </div>
              </div>
            </div>
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={removeFile}
              className="text-gray-500 hover:text-red-500"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
      ) : (
        <div
          {...getRootProps()}
          className={cn(
            "pdf-dropzone border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors",
            isDragActive && "pdf-dropzone-active",
            isDragAccept && "pdf-dropzone-accept",
            isDragReject && "pdf-dropzone-reject",
            error && "border-red-500 bg-red-50"
          )}
        >
          <input {...getInputProps()} />
          <Upload className="h-10 w-10 mx-auto mb-4 text-gray-400" />
          <p className="text-sm font-medium">
            {isDragActive
              ? "Drop the PDF file here"
              : "Drag & drop a PDF file here, or click to select"}
          </p>
          {description && <p className="text-xs text-gray-500 mt-1">{description}</p>}
          {error && <p className="text-xs text-red-500 mt-2">{error}</p>}
        </div>
      )}
    </div>
  );
};

export default PDFUploader;
