import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { getUserDocuments, DocumentInfo, refreshDocumentUrl, checkFileExists } from '@/lib/storageService';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Loader2, AlertTriangle, RefreshCw, ExternalLink, FileText, Eye } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';

// PDF Viewer component for testing both Google Docs and direct viewing
const DocumentDiagnosticsViewer = ({ url, title }: { url: string; title: string }) => {
  const [viewerType, setViewerType] = useState<'google' | 'direct'>('direct');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [loadAttempts, setLoadAttempts] = useState(0);

  // Add cache busting parameter to prevent caching issues
  const cacheBustedUrl = `${url}${url.includes('?') ? '&' : '?'}_cb=${Date.now()}`;

  // Encode the URL for Google Docs Viewer
  const googleDocsViewerUrl = `https://docs.google.com/viewer?url=${encodeURIComponent(cacheBustedUrl)}&embedded=true`;

  // Handle iframe load event
  const handleIframeLoad = () => {
    console.log("Document loaded successfully");
    setIsLoading(false);
  };

  // Handle iframe error
  const handleIframeError = () => {
    console.error(`Error loading document (attempt ${loadAttempts + 1})`);
    setLoadAttempts(prev => prev + 1);

    if (loadAttempts >= 2) {
      setIsLoading(false);
      setError(`Failed to load the document. Please try a different viewer or download the document.`);
    } else {
      // Try again with a different viewer
      console.log("Switching viewer type");
      setViewerType(viewerType === 'google' ? 'direct' : 'google');
    }
  };

  return (
    <div className="w-full h-full">
      <div className="flex justify-between items-center mb-3">
        <div className="text-sm text-gray-500">
          Viewing: {title}
        </div>
        <div className="flex space-x-2">
          <Button
            size="sm"
            variant="outline"
            onClick={() => {
              setViewerType(viewerType === 'google' ? 'direct' : 'google');
              setIsLoading(true);
              setError(null);
              setLoadAttempts(0);
            }}
          >
            Switch to {viewerType === 'google' ? 'Direct' : 'Google'} View
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={() => window.open(url, '_blank')}
          >
            <ExternalLink className="h-4 w-4 mr-1" />
            Open in New Tab
          </Button>
        </div>
      </div>

      {error && (
        <Alert variant="destructive" className="mb-4">
          <AlertTriangle className="h-4 w-4 mr-2" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {isLoading && (
        <div className="flex justify-center items-center h-16 mb-4">
          <Loader2 className="h-6 w-6 animate-spin mr-2" />
          <span>Loading document...</span>
        </div>
      )}

      <iframe
        src={viewerType === 'google' ? googleDocsViewerUrl : cacheBustedUrl}
        className="w-full h-[600px] border border-gray-200 rounded-md"
        title={title}
        onLoad={handleIframeLoad}
        onError={handleIframeError}
      />
    </div>
  );
};

// Helper function to get container-friendly path
const getContainerFriendlyPath = (path: string): string => {
  // Always extract the path starting from 'users/' if present
  const userIndex = path.indexOf('users/');
  if (userIndex !== -1) {
    return path.substring(userIndex);
  }
  // If 'users/' is not found, return only the filename as fallback
  const lastSlash = path.lastIndexOf('/');
  return lastSlash !== -1 ? path.substring(lastSlash + 1) : path;
};

// Document Card component
const DocumentCard = ({ document, onView }: { document: DocumentInfo; onView: () => void }) => {
  // Get container-friendly path
  const containerPath = getContainerFriendlyPath(document.path);

  return (
    <Card className="mb-4">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg flex items-center">
          <FileText className="h-5 w-5 mr-2" />
          {document.name}
        </CardTitle>
        <CardDescription>
          Type: {document.type} | Uploaded: {new Date(document.uploadedAt).toLocaleString()}
        </CardDescription>
      </CardHeader>
      <CardContent className="pb-2">
        <div className="text-sm text-gray-500 mb-2">
          <div><strong>Path:</strong> {containerPath}</div>
          <div><strong>Status:</strong> <Badge variant={document.status === 'uploaded' ? 'default' : document.status === 'analyzed' ? 'success' : 'destructive'}>{document.status}</Badge></div>
        </div>
        <div className="text-xs text-gray-400 break-all">
          <strong>URL:</strong> {document.url}
        </div>
      </CardContent>
      <CardFooter>
        <Button variant="outline" onClick={onView}>
          <Eye className="h-4 w-4 mr-2" />
          View Document
        </Button>
      </CardFooter>
    </Card>
  );
};

// Main component
const DocumentDiagnosticsPage = () => {
  const { currentUser } = useAuth();
  const [documents, setDocuments] = useState<DocumentInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedDocument, setSelectedDocument] = useState<DocumentInfo | null>(null);
  const [viewerOpen, setViewerOpen] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [testResults, setTestResults] = useState<Record<string, any>>({});

  // Fetch documents
  const fetchDocuments = async () => {
    if (!currentUser) return;

    setLoading(true);
    setError(null);

    try {
      const docs = await getUserDocuments(currentUser.uid);
      // Filter to only include PDFs
      const pdfDocs = docs.filter(doc =>
        doc.name.toLowerCase().endsWith('.pdf') ||
        doc.type.includes('pdf') ||
        doc.path.toLowerCase().includes('pdf')
      );
      setDocuments(pdfDocs);
    } catch (err) {
      console.error("Error fetching documents:", err);
      setError(`Failed to fetch documents: ${err instanceof Error ? err.message : String(err)}`);
    } finally {
      setLoading(false);
    }
  };

  // Test document access
  const testDocument = async (doc: DocumentInfo) => {
    try {
      setTestResults(prev => ({
        ...prev,
        [doc.id]: { status: 'testing' }
      }));

      // Test 1: Check if file exists
      const fileCheck = await checkFileExists(doc.path);

      // Add container-friendly path to the result
      fileCheck.containerPath = getContainerFriendlyPath(doc.path);

      // Test 2: Try to refresh URL
      let refreshResult = { success: false, url: '', error: '' };
      try {
        const newUrl = await refreshDocumentUrl(currentUser!.uid, doc.id);
        refreshResult = { success: true, url: newUrl, error: '' };
      } catch (err) {
        refreshResult = {
          success: false,
          url: '',
          error: err instanceof Error ? err.message : String(err)
        };
      }

      // Test 3: Try direct fetch
      let fetchResult = { success: false, error: '' };
      try {
        const response = await fetch(doc.url, { method: 'HEAD' });
        fetchResult = {
          success: response.ok,
          status: response.status,
          statusText: response.statusText,
          error: response.ok ? '' : `HTTP ${response.status}: ${response.statusText}`
        };
      } catch (err) {
        fetchResult = {
          success: false,
          error: err instanceof Error ? err.message : String(err)
        };
      }

      setTestResults(prev => ({
        ...prev,
        [doc.id]: {
          status: 'complete',
          fileCheck,
          refreshResult,
          fetchResult,
          timestamp: new Date().toISOString()
        }
      }));
    } catch (err) {
      setTestResults(prev => ({
        ...prev,
        [doc.id]: {
          status: 'error',
          error: err instanceof Error ? err.message : String(err),
          timestamp: new Date().toISOString()
        }
      }));
    }
  };

  // Refresh all document URLs
  const refreshAllUrls = async () => {
    if (!currentUser || refreshing) return;

    setRefreshing(true);

    try {
      for (const doc of documents) {
        try {
          await refreshDocumentUrl(currentUser.uid, doc.id);
          // Update document in state with new URL
          setDocuments(prev =>
            prev.map(d =>
              d.id === doc.id
                ? { ...d, url: doc.url + (doc.url.includes('?') ? '&' : '?') + '_refreshed=' + Date.now() }
                : d
            )
          );
        } catch (err) {
          console.error(`Failed to refresh URL for ${doc.name}:`, err);
        }
      }

      // Fetch documents again to get updated URLs
      await fetchDocuments();
    } catch (err) {
      console.error("Error refreshing URLs:", err);
      setError(`Failed to refresh URLs: ${err instanceof Error ? err.message : String(err)}`);
    } finally {
      setRefreshing(false);
    }
  };

  // Handle view document
  const handleViewDocument = (doc: DocumentInfo) => {
    setSelectedDocument(doc);
    setViewerOpen(true);
  };

  // Load documents on mount
  useEffect(() => {
    if (currentUser) {
      fetchDocuments();
    }
  }, [currentUser]);

  if (!currentUser) {
    return (
      <div className="container mx-auto p-6">
        <Alert>
          <AlertTitle>Authentication Required</AlertTitle>
          <AlertDescription>
            Please sign in to view your documents.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">Document Diagnostics</h1>

      <div className="flex justify-between items-center mb-4">
        <div>
          <p className="text-gray-600">
            This page helps diagnose issues with PDF documents in Firebase Storage.
          </p>
        </div>
        <div className="flex space-x-2">
          <Button onClick={fetchDocuments} disabled={loading}>
            {loading ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : null}
            Refresh Documents
          </Button>
          <Button onClick={refreshAllUrls} disabled={refreshing} variant="outline">
            {refreshing ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : <RefreshCw className="h-4 w-4 mr-2" />}
            Refresh All URLs
          </Button>
        </div>
      </div>

      {error && (
        <Alert variant="destructive" className="mb-4">
          <AlertTriangle className="h-4 w-4 mr-2" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {loading ? (
        <div className="flex justify-center items-center h-40">
          <Loader2 className="h-8 w-8 animate-spin mr-2" />
          <span>Loading documents...</span>
        </div>
      ) : documents.length === 0 ? (
        <Alert className="mb-4">
          <AlertTitle>No PDF Documents Found</AlertTitle>
          <AlertDescription>
            No PDF documents were found in your storage. Please upload some documents first.
          </AlertDescription>
        </Alert>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {documents.map(doc => (
            <DocumentCard
              key={doc.id}
              document={doc}
              onView={() => handleViewDocument(doc)}
            />
          ))}
        </div>
      )}

      {/* Document Viewer Dialog */}
      {selectedDocument && (
        <Dialog open={viewerOpen} onOpenChange={setViewerOpen}>
          <DialogContent className="max-w-5xl w-[95vw] h-[85vh]">
            <DialogHeader>
              <DialogTitle>Document Viewer - {selectedDocument.name}</DialogTitle>
            </DialogHeader>
            <Tabs defaultValue="viewer">
              <TabsList>
                <TabsTrigger value="viewer">Document Viewer</TabsTrigger>
                <TabsTrigger value="diagnostics">Diagnostics</TabsTrigger>
                <TabsTrigger value="details">Document Details</TabsTrigger>
              </TabsList>

              <TabsContent value="viewer" className="h-[calc(85vh-120px)]">
                <DocumentDiagnosticsViewer url={selectedDocument.url} title={selectedDocument.name} />
              </TabsContent>

              <TabsContent value="diagnostics">
                <div className="space-y-4 p-4 max-h-[calc(85vh-120px)] overflow-y-auto">
                  <div className="flex justify-between">
                    <h3 className="text-lg font-medium">Document Diagnostics</h3>
                    <Button
                      size="sm"
                      onClick={() => testDocument(selectedDocument)}
                      disabled={testResults[selectedDocument.id]?.status === 'testing'}
                    >
                      {testResults[selectedDocument.id]?.status === 'testing' ? (
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      ) : (
                        <RefreshCw className="h-4 w-4 mr-2" />
                      )}
                      Run Tests
                    </Button>
                  </div>

                  {testResults[selectedDocument.id] ? (
                    <div className="space-y-4">
                      {testResults[selectedDocument.id].status === 'testing' ? (
                        <div className="flex items-center">
                          <Loader2 className="h-5 w-5 animate-spin mr-2" />
                          <span>Running tests...</span>
                        </div>
                      ) : testResults[selectedDocument.id].status === 'error' ? (
                        <Alert variant="destructive">
                          <AlertTriangle className="h-4 w-4 mr-2" />
                          <AlertTitle>Test Error</AlertTitle>
                          <AlertDescription>{testResults[selectedDocument.id].error}</AlertDescription>
                        </Alert>
                      ) : (
                        <>
                          <div className="bg-gray-50 p-4 rounded-md">
                            <h4 className="font-medium mb-2">File Check</h4>
                            <div className="space-y-1 text-sm">
                              <div>Exists: <Badge variant={testResults[selectedDocument.id].fileCheck.exists ? "success" : "destructive"}>{testResults[selectedDocument.id].fileCheck.exists ? "Yes" : "No"}</Badge></div>
                              {testResults[selectedDocument.id].fileCheck.containerPath && (
                                <div>
                                  <span>Path: </span>
                                  <span className="text-gray-600">{testResults[selectedDocument.id].fileCheck.containerPath}</span>
                                </div>
                              )}
                              {testResults[selectedDocument.id].fileCheck.size && (
                                <div>Size: {(testResults[selectedDocument.id].fileCheck.size / 1024).toFixed(2)} KB</div>
                              )}
                              {testResults[selectedDocument.id].fileCheck.error && (
                                <div className="text-red-500">Error: {testResults[selectedDocument.id].fileCheck.error}</div>
                              )}
                            </div>
                          </div>

                          <div className="bg-gray-50 p-4 rounded-md">
                            <h4 className="font-medium mb-2">URL Refresh</h4>
                            <div className="space-y-1 text-sm">
                              <div>Success: <Badge variant={testResults[selectedDocument.id].refreshResult.success ? "success" : "destructive"}>{testResults[selectedDocument.id].refreshResult.success ? "Yes" : "No"}</Badge></div>
                              {testResults[selectedDocument.id].refreshResult.url && (
                                <div className="break-all">
                                  <div>New URL:</div>
                                  <div className="text-xs text-gray-500">{testResults[selectedDocument.id].refreshResult.url}</div>
                                </div>
                              )}
                              {testResults[selectedDocument.id].refreshResult.error && (
                                <div className="text-red-500">Error: {testResults[selectedDocument.id].refreshResult.error}</div>
                              )}
                            </div>
                          </div>

                          <div className="bg-gray-50 p-4 rounded-md">
                            <h4 className="font-medium mb-2">Direct Fetch</h4>
                            <div className="space-y-1 text-sm">
                              <div>Success: <Badge variant={testResults[selectedDocument.id].fetchResult.success ? "success" : "destructive"}>{testResults[selectedDocument.id].fetchResult.success ? "Yes" : "No"}</Badge></div>
                              {testResults[selectedDocument.id].fetchResult.status && (
                                <div>Status: {testResults[selectedDocument.id].fetchResult.status} {testResults[selectedDocument.id].fetchResult.statusText}</div>
                              )}
                              {testResults[selectedDocument.id].fetchResult.error && (
                                <div className="text-red-500">Error: {testResults[selectedDocument.id].fetchResult.error}</div>
                              )}
                            </div>
                          </div>

                          <div className="text-xs text-gray-400">
                            Tests run at: {testResults[selectedDocument.id].timestamp}
                          </div>
                        </>
                      )}
                    </div>
                  ) : (
                    <div className="text-center p-8 text-gray-500">
                      <p>No tests have been run yet. Click "Run Tests" to diagnose this document.</p>
                    </div>
                  )}
                </div>
              </TabsContent>

              <TabsContent value="details">
                <div className="p-4 space-y-4 max-h-[calc(85vh-120px)] overflow-y-auto">
                  <h3 className="text-lg font-medium">Document Details</h3>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <div>
                        <span className="font-medium">Name:</span> {selectedDocument.name}
                      </div>
                      <div>
                        <span className="font-medium">Type:</span> {selectedDocument.type}
                      </div>
                      <div>
                        <span className="font-medium">ID:</span> {selectedDocument.id}
                      </div>
                      <div>
                        <span className="font-medium">Status:</span> <Badge>{selectedDocument.status}</Badge>
                      </div>
                      <div>
                        <span className="font-medium">Uploaded:</span> {new Date(selectedDocument.uploadedAt).toLocaleString()}
                      </div>
                      {selectedDocument.analysisId && (
                        <div>
                          <span className="font-medium">Analysis ID:</span> {selectedDocument.analysisId}
                        </div>
                      )}
                    </div>

                    <div className="space-y-2">
                      <div>
                        <span className="font-medium">Path:</span>
                        <div className="text-sm text-gray-600 break-all">{getContainerFriendlyPath(selectedDocument.path)}</div>
                      </div>
                      <div>
                        <span className="font-medium">URL:</span>
                        <div className="text-sm text-gray-600 break-all">{selectedDocument.url}</div>
                      </div>
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-2">
                    <h4 className="font-medium">Actions</h4>
                    <div className="flex flex-wrap gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => window.open(selectedDocument.url, '_blank')}
                      >
                        <ExternalLink className="h-4 w-4 mr-1" />
                        Open URL in New Tab
                      </Button>

                      <Button
                        size="sm"
                        variant="outline"
                        onClick={async () => {
                          try {
                            const newUrl = await refreshDocumentUrl(currentUser!.uid, selectedDocument.id);
                            setSelectedDocument({
                              ...selectedDocument,
                              url: newUrl
                            });
                          } catch (err) {
                            console.error("Error refreshing URL:", err);
                          }
                        }}
                      >
                        <RefreshCw className="h-4 w-4 mr-1" />
                        Refresh URL
                      </Button>
                    </div>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

export default DocumentDiagnosticsPage;
