import React from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import DashboardSidebar from "./DashboardSidebar";
import Header from "./Header";

interface DashboardLayoutProps {
  children: React.ReactNode;
}

const DashboardLayout: React.FC<DashboardLayoutProps> = ({ children }) => {
  const { currentUser } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <div className="flex min-h-[calc(100vh-64px)]">
        {/* Sidebar placeholder for spacing */}
        {currentUser && <div id="sidebar-placeholder" className="w-64 flex-shrink-0 transition-all duration-300" />}

        <main className="flex-1 p-4 md:p-6 overflow-auto">
          <div className="max-w-7xl mx-auto">
            {children}
          </div>
        </main>

        {/* Actual Sidebar - Fixed Position */}
        {currentUser && <DashboardSidebar />}
      </div>
    </div>
  );
};

export default DashboardLayout;
