// firebaseStorageService.ts - Handle Firebase Realtime Database operations

import { database } from '@/lib/firebase';
import { ref, push, set, get, query, orderByChild, equalTo } from 'firebase/database';
import { CheckResult } from './checkDefinitions';
import { AnalysisParameters } from './mainDocumentProcessor';

export interface StoredAnalysisResult {
  id: string;
  userId: string;
  companyName: string;
  timestamp: number;
  parameters: AnalysisParameters;
  results: Record<string, CheckResult>;
  documents: {
    audit_report?: { name: string; size: number; type: string };
    annexure_a?: { name: string; size: number; type: string };
    annexure_b?: { name: string; size: number; type: string };
    balance_sheet?: { name: string; size: number; type: string };
    notes?: { name: string; size: number; type: string };
    pl_notes?: { name: string; size: number; type: string };
    annual_report?: { name: string; size: number; type: string };
    secretarial_compliance?: { name: string; size: number; type: string };
  };
  summary: {
    total: number;
    compliant: number;
    nonCompliant: number;
    compliancePercentage: number;
  };
}

/**
 * Save analysis results to Firebase Realtime Database
 */
export async function saveAnalysisResults(
  userId: string,
  parameters: AnalysisParameters,
  results: Record<string, CheckResult>,
  documentFiles: Record<string, File>,
  summary: {
    total: number;
    compliant: number;
    nonCompliant: number;
    compliancePercentage: number;
  }
): Promise<string> {
  try {
    // Create document metadata (without the actual file content)
    const documents: StoredAnalysisResult['documents'] = {};
    
    for (const [key, file] of Object.entries(documentFiles)) {
      if (file) {
        documents[key as keyof StoredAnalysisResult['documents']] = {
          name: file.name,
          size: file.size,
          type: file.type
        };
      }
    }

    // Create the analysis data
    const analysisData: Omit<StoredAnalysisResult, 'id'> = {
      userId,
      companyName: parameters.company_name,
      timestamp: Date.now(),
      parameters: {
        ...parameters,
        audit_date: parameters.audit_date.toISOString() // Convert Date to string for storage
      } as any,
      results,
      documents,
      summary
    };

    // Save to Firebase Realtime Database
    const analysisRef = ref(database, 'analyses');
    const newAnalysisRef = push(analysisRef);
    
    await set(newAnalysisRef, analysisData);
    
    const analysisId = newAnalysisRef.key!;
    console.log('Analysis saved successfully with ID:', analysisId);
    
    return analysisId;
  } catch (error) {
    console.error('Error saving analysis results:', error);
    throw new Error(`Failed to save analysis results: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * Get analysis history for a user
 */
export async function getAnalysisHistory(userId: string): Promise<StoredAnalysisResult[]> {
  try {
    const analysesRef = ref(database, 'analyses');
    const userAnalysesQuery = query(analysesRef, orderByChild('userId'), equalTo(userId));
    
    const snapshot = await get(userAnalysesQuery);
    
    if (!snapshot.exists()) {
      return [];
    }

    const analyses: StoredAnalysisResult[] = [];
    snapshot.forEach((childSnapshot) => {
      const data = childSnapshot.val();
      analyses.push({
        id: childSnapshot.key!,
        ...data,
        // Convert audit_date string back to Date object
        parameters: {
          ...data.parameters,
          audit_date: new Date(data.parameters.audit_date)
        }
      });
    });

    // Sort by timestamp (newest first)
    return analyses.sort((a, b) => b.timestamp - a.timestamp);
  } catch (error) {
    console.error('Error fetching analysis history:', error);
    throw new Error(`Failed to fetch analysis history: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * Get a specific analysis by ID
 */
export async function getAnalysisById(analysisId: string, userId: string): Promise<StoredAnalysisResult | null> {
  try {
    const analysisRef = ref(database, `analyses/${analysisId}`);
    const snapshot = await get(analysisRef);
    
    if (!snapshot.exists()) {
      return null;
    }

    const data = snapshot.val();
    
    // Verify the analysis belongs to the user
    if (data.userId !== userId) {
      throw new Error('Unauthorized access to analysis');
    }

    return {
      id: analysisId,
      ...data,
      // Convert audit_date string back to Date object
      parameters: {
        ...data.parameters,
        audit_date: new Date(data.parameters.audit_date)
      }
    };
  } catch (error) {
    console.error('Error fetching analysis by ID:', error);
    throw new Error(`Failed to fetch analysis: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * Delete an analysis
 */
export async function deleteAnalysis(analysisId: string, userId: string): Promise<void> {
  try {
    // First verify the analysis belongs to the user
    const analysis = await getAnalysisById(analysisId, userId);
    if (!analysis) {
      throw new Error('Analysis not found or unauthorized');
    }

    const analysisRef = ref(database, `analyses/${analysisId}`);
    await set(analysisRef, null); // Setting to null deletes the node
    
    console.log('Analysis deleted successfully:', analysisId);
  } catch (error) {
    console.error('Error deleting analysis:', error);
    throw new Error(`Failed to delete analysis: ${error instanceof Error ? error.message : String(error)}`);
  }
}