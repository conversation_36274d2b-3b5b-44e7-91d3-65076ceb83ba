import React from 'react';
import AuditReportItem from './AuditReportItem';
import { CheckResult } from '@/lib/checkDefinitions';

const JsonFormattingTest = () => {
  // Test data that matches your example
  const testResult1: CheckResult = {
    isCompliant: true,
    explanation: "✅ COMPLIANT: Cross-reference alignment verified between Audit Report and Annexure A. Audit Report paragraph 1 properly contains both \"Companies (Auditors' Report) Order\" and \"Annexure A\" references under \"Report on Other Legal and Regulatory Requirements\" section. Annexure A correctly back-references paragraph 1 with the required pattern \"Referred to in paragraph X on 'Report on Other Legal and Regulatory Requirements'\". Both documents maintain proper audit trail linkage ensuring compliance with auditing documentation standards.",
    confidence: 0.95,
    detail: `{
  "auditReportReference": "1",
  "annexureABackReference": "1",
  "referencesMatch": true,
  "auditReportText": "1. As required by the Companies (Auditors' Report) Order, 2020 (\\"the Order\\"), issued by the Central Government of India in terms of Section 143 (11) of the Act, we give in the \\"Annexure A\\" a statement on the matters specified in paragraphs 3 and 4 of the Order, to the extent applicable.",
  "annexureAText": "Referred to in paragraph 1 on 'Report on Other Legal and Regulatory Requirements' of our report of even date to the members of TVS Srichakra Limited (\\"the Company\\") on the standalone financial statements as of and for the year ended 31 March 2024."
}`
  };

  const testResult2: CheckResult = {
    isCompliant: true,
    explanation: "✅ COMPLIANT: Cross-reference alignment verified between Audit Report and Annexure B. Audit Report paragraph 2(f) properly references \"Annexure B\" under \"Report on Other Legal and Regulatory Requirements\" section. Annexure B correctly back-references paragraph 2(f) with the required pattern \"Referred to in paragraph X on 'Report on Other Legal and Regulatory Requirements'\". Both documents maintain proper audit trail linkage for internal financial controls reporting compliance.",
    confidence: 0.95,
    detail: `{
  "auditReportReference": "2(f)",
  "annexureBBackReference": "2(f)",
  "referencesMatch": true,
  "auditReportText": "With respect to the adequacy of the internal financial controls with reference to the standalone financial statements of the Company and the operating effectiveness of such controls, refer to our separate Report in \\"Annexure B\\".",
  "annexureBText": "Referred to in paragraph 2(f) on 'Report on Other Legal and Regulatory Requirements' of our report of even date"
}`
  };

  const testResult3: CheckResult = {
    isCompliant: false,
    explanation: "❌ NON-COMPLIANT: Inventory verification disclosure inconsistency found. Inventories Note: N/A, Goods in Transit: Not Found, CARO Proper Disclosure: Yes. Alignment required between Notes and CARO for proper goods in transit verification reporting.",
    confidence: 0.90,
    detail: `{
  "inventoriesNote": "Not Found",
  "goodsInTransitPresent": "Not Present",
  "caroGoodsInTransitDisclosure": "Clause (ii)(a) Proper Disclosure"
}`
  };

  return (
    <div className="p-6 space-y-6">
      <h2 className="text-2xl font-bold mb-4">JSON Formatting Test</h2>
      <p className="text-gray-600 mb-6">
        This demonstrates how JSON data in the details section is automatically converted to readable text format.
      </p>
      
      <div className="space-y-6">
        <div className="border rounded-lg p-4">
          <h3 className="text-lg font-semibold mb-3">Test 1: Audit Report Annexure A Reference</h3>
          <AuditReportItem 
            title="Audit Report Annexure A Reference"
            result={testResult1}
          />
        </div>

        <div className="border rounded-lg p-4">
          <h3 className="text-lg font-semibold mb-3">Test 2: Audit Report Annexure B Reference</h3>
          <AuditReportItem 
            title="Audit Report Annexure B Reference"
            result={testResult2}
          />
        </div>

        <div className="border rounded-lg p-4">
          <h3 className="text-lg font-semibold mb-3">Test 3: Inventory Goods In Transit Check</h3>
          <AuditReportItem 
            title="Inventory Goods In Transit Check"
            result={testResult3}
          />
        </div>
      </div>

      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <h4 className="font-semibold mb-2">Expected Behavior:</h4>
        <ul className="list-disc list-inside space-y-1 text-sm">
          <li>Click "Show details" to see the formatted JSON data</li>
          <li>JSON field names should be converted to readable format (e.g., "auditReportReference" → "Audit Report Reference")</li>
          <li>Boolean values should show as "Yes/No" instead of true/false</li>
          <li>Long text should be properly formatted with line breaks</li>
          <li>The raw JSON should no longer be visible</li>
        </ul>
      </div>
    </div>
  );
};

export default JsonFormattingTest;
