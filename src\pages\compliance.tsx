import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Scale, Award } from "lucide-react";
import { <PERSON> } from "react-router-dom";
import { Button } from "@/components/ui/button";

const Compliance = () => {
  return (
    <div className="min-h-screen bg-slate-900 text-white">
      {/* Header */}
      <header className="bg-slate-900/95 backdrop-blur-lg border-b border-white/10">
        <div className="w-full px-6 md:px-12 lg:px-20 py-6 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="h-10 w-10 rounded-xl bg-gradient-to-br from-blue-600 to-purple-600 flex items-center justify-center text-white font-bold">
              P
            </div>
            <span className="text-xl font-bold text-white">PKF Audit AI</span>
          </div>

          <Link to="/">
            <Button variant="ghost" className="text-white">
              <ArrowL<PERSON>t className="h-4 w-4 mr-2" />
              Back to Home
            </Button>
          </Link>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-24 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-900 via-indigo-900 to-slate-900"></div>
        <div className="w-full px-6 md:px-12 lg:px-20 relative z-10">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Regulatory <span className="bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent">Compliance</span>
            </h1>
            <p className="text-xl md:text-2xl text-white/80 mb-8">
              Our commitment to meeting global financial and data protection regulations
            </p>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-16 w-full px-6 md:px-12 lg:px-20">
        <div className="max-w-4xl mx-auto space-y-12">
          <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-3xl p-8">
            <div className="flex items-start gap-4 mb-6">
              <div className="h-12 w-12 rounded-xl bg-green-600/20 flex items-center justify-center">
                <FileCheck className="h-6 w-6 text-green-400" />
              </div>
              <div>
                <h2 className="text-2xl font-bold mb-2">Audit Standards Compliance</h2>
                <p className="text-white/70 leading-relaxed">
                  PKF Audit AI is designed to help auditors comply with International Standards on Auditing (ISA), Generally Accepted Auditing Standards (GAAS), and other country-specific audit standards. Our system continuously updates to incorporate the latest regulatory changes, ensuring your audit processes always meet current requirements.
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-3xl p-8">
            <div className="flex items-start gap-4 mb-6">
              <div className="h-12 w-12 rounded-xl bg-blue-600/20 flex items-center justify-center">
                <ClipboardCheck className="h-6 w-6 text-blue-400" />
              </div>
              <div>
                <h2 className="text-2xl font-bold mb-2">Financial Reporting Compliance</h2>
                <p className="text-white/70 leading-relaxed">
                  Our platform helps ensure compliance with International Financial Reporting Standards (IFRS), Generally Accepted Accounting Principles (GAAP), and local financial reporting frameworks. We provide comprehensive checks for disclosure requirements and financial statement presentation to maintain regulatory compliance.
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-3xl p-8">
            <div className="flex items-start gap-4 mb-6">
              <div className="h-12 w-12 rounded-xl bg-purple-600/20 flex items-center justify-center">
                <BookOpen className="h-6 w-6 text-purple-400" />
              </div>
              <div>
                <h2 className="text-2xl font-bold mb-2">Data Protection Compliance</h2>
                <p className="text-white/70 leading-relaxed">
                  PKF Audit AI is fully compliant with major data protection regulations including GDPR (Europe), CCPA (California), PIPEDA (Canada), and other international data privacy laws. We implement privacy-by-design principles and provide tools to help you maintain compliance when processing personal or sensitive financial information.
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-3xl p-8">
            <div className="flex items-start gap-4 mb-6">
              <div className="h-12 w-12 rounded-xl bg-indigo-600/20 flex items-center justify-center">
                <Scale className="h-6 w-6 text-indigo-400" />
              </div>
              <div>
                <h2 className="text-2xl font-bold mb-2">Industry-Specific Regulations</h2>
                <p className="text-white/70 leading-relaxed">
                  Our platform supports compliance with industry-specific regulations including banking regulations (Basel III, Dodd-Frank), healthcare regulations (HIPAA), and industry-specific audit requirements. Our customizable compliance frameworks adapt to the specific regulatory needs of your industry and jurisdiction.
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-3xl p-8">
            <div className="flex items-start gap-4 mb-6">
              <div className="h-12 w-12 rounded-xl bg-yellow-600/20 flex items-center justify-center">
                <Award className="h-6 w-6 text-yellow-400" />
              </div>
              <div>
                <h2 className="text-2xl font-bold mb-2">Certifications & Attestations</h2>
                <p className="text-white/70 leading-relaxed">
                  PKF Audit AI has obtained multiple compliance certifications including SOC 1 Type II and SOC 2 Type II attestations, ISO 27001 certification, and is regularly audited by independent third parties to validate our compliance controls. We maintain a robust compliance program with regular assessments and updates.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Compliance Updates Section */}
      <section className="py-16 w-full px-6 md:px-12 lg:px-20 bg-gradient-to-br from-blue-900/30 to-purple-900/30">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl font-bold mb-6">Stay Updated on Compliance</h2>
          <p className="text-xl text-white/70 mb-8">
            Subscribe to receive notifications about regulatory changes that may affect your audit processes.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <input 
              type="email"
              placeholder="Enter your email"
              className="px-6 py-3 rounded-full bg-white/10 border border-white/20 text-white placeholder-white/50 focus:outline-none focus:border-blue-400"
            />
            <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 rounded-full px-8 py-3">
              Subscribe
            </Button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t border-white/10 py-8">
        <div className="w-full px-6 md:px-12 lg:px-20 text-center">
          <p className="text-white/60">
            © {new Date().getFullYear()} PKF Audit AI. All rights reserved.
          </p>
        </div>
      </footer>
    </div>
  );
};

export default Compliance;
