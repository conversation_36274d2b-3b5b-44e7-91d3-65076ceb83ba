// annualReportCaroProcessor.ts - Complete Interlinked Annual Report + CARO processor

import { CheckResult } from './checkDefinitions';
import { processSingleDocumentCheck } from './geminiProcessor';
import {
  checkIncomeeTaxRaidsKeywords,
  checkDefaultsKeywords,
  checkRightsIssueKeywords,
  checkFraudKeywords,
  checkWhistleBlowerKeywords,
  checkCostRecordsKeywords
} from './textSearchProcessor';

export interface AnnualReportCaroCheckResult extends CheckResult {
  annualReportFindings?: {
    keywordsFound: string[];
    contexts: Array<{keyword: string, context: string}>;
    totalMatches: number;
  };
  caroClauseAnalysis?: {
    clausePresent: boolean;
    clauseContent: string;
    complianceStatus: string;
    clauseNumber: string;
  };
  crossComplianceStatus?: {
    keywordsFoundInAnnualReport: boolean;
    expectedCaroClause: string;
    caroClausePresent: boolean;
    overallCompliant: boolean;
  };
}

/**
 * Process Annual Report + CARO Income Tax Raids Check
 * Keywords: "Income tax raids", "Raids by Income tax authorities", "Unrecorded income"
 * Expected CARO Clause: (viii)
 */
export async function processAnnualReportCaroIncomeTax(
  annualReportFile: File,
  caroFile: File,
  parameters: Record<string, any>
): Promise<AnnualReportCaroCheckResult> {
  try {
    console.log('Processing Annual Report + CARO Income Tax Raids check');
    
    // Step 1: Search Annual Report for income tax keywords
    const annualReportResult = await checkIncomeeTaxRaidsKeywords(annualReportFile);
    
    if (!annualReportResult.isCompliant) {
      // No keywords found, so CARO clause (viii) is not required
      return {
        isCompliant: true,
        explanation: 'No income tax raids, unrecorded income keywords found in annual report. CARO clause (viii) not required for this cross-compliance check.',
        confidence: 0.9,
        annualReportFindings: {
          keywordsFound: [],
          contexts: [],
          totalMatches: 0
        },
        caroClauseAnalysis: {
          clausePresent: false,
          clauseContent: 'Not required - no triggering keywords found',
          complianceStatus: 'Not applicable',
          clauseNumber: '(viii)'
        },
        crossComplianceStatus: {
          keywordsFoundInAnnualReport: false,
          expectedCaroClause: '(viii)',
          caroClausePresent: false,
          overallCompliant: true
        }
      };
    }
    
    // Step 2: Keywords found, check CARO clause (viii)
    const caroResult = await processSingleDocumentCheck(
      caroFile,
      'caro_clause_viii_check',
      'caro_clause_viii_income_tax',
      parameters
    );
    
    // Step 3: Evaluate compliance
    const isCompliant = annualReportResult.isCompliant && caroResult.isCompliant;
    
    return {
      isCompliant,
      explanation: isCompliant
        ? `✅ COMPLIANT: Income tax related keywords found in annual report (${annualReportResult.extractedData?.foundKeywords?.join(', ')}), and CARO clause (viii) is properly present and addresses income tax matters. Cross-document compliance maintained.`
        : `❌ NON-COMPLIANT: Income tax keywords found in annual report (${annualReportResult.extractedData?.foundKeywords?.join(', ')}), but CARO clause (viii) is ${caroResult.isCompliant ? 'present but inadequate' : 'missing'}. When annual report mentions income tax raids or unrecorded income, corresponding CARO clause (viii) must be included.`,
      confidence: 0.9,
      annualReportFindings: {
        keywordsFound: annualReportResult.extractedData?.foundKeywords || [],
        contexts: annualReportResult.extractedData?.contexts || [],
        totalMatches: annualReportResult.extractedData?.totalMatches || 0
      },
      caroClauseAnalysis: {
        clausePresent: caroResult.isCompliant,
        clauseContent: caroResult.explanation,
        complianceStatus: caroResult.isCompliant ? 'Compliant' : 'Non-compliant',
        clauseNumber: '(viii)'
      },
      crossComplianceStatus: {
        keywordsFoundInAnnualReport: true,
        expectedCaroClause: '(viii)',
        caroClausePresent: caroResult.isCompliant,
        overallCompliant: isCompliant
      }
    };
    
  } catch (error) {
    console.error('Error processing Annual Report + CARO Income Tax check:', error);
    return {
      isCompliant: false,
      explanation: `Technical error during Annual Report + CARO Income Tax cross-compliance check: ${error instanceof Error ? error.message : String(error)}`,
      confidence: 0.5,
      crossComplianceStatus: {
        keywordsFoundInAnnualReport: false,
        expectedCaroClause: '(viii)',
        caroClausePresent: false,
        overallCompliant: false
      }
    };
  }
}

/**
 * Process Annual Report + CARO Defaults Check
 * Keywords: "Defaults", "Wilful Defaulter", "Diversion"
 * Expected CARO Clause: (ix)
 */
export async function processAnnualReportCaroDefaults(
  annualReportFile: File,
  caroFile: File,
  parameters: Record<string, any>
): Promise<AnnualReportCaroCheckResult> {
  try {
    console.log('Processing Annual Report + CARO Defaults check');
    
    const annualReportResult = await checkDefaultsKeywords(annualReportFile);
    
    if (!annualReportResult.isCompliant) {
      return {
        isCompliant: true,
        explanation: 'No defaults, wilful defaulter, or diversion keywords found in annual report. CARO clause (ix) analysis not required for this cross-compliance check.',
        confidence: 0.9,
        annualReportFindings: {
          keywordsFound: [],
          contexts: [],
          totalMatches: 0
        },
        caroClauseAnalysis: {
          clausePresent: false,
          clauseContent: 'Not required - no triggering keywords found',
          complianceStatus: 'Not applicable',
          clauseNumber: '(ix)'
        },
        crossComplianceStatus: {
          keywordsFoundInAnnualReport: false,
          expectedCaroClause: '(ix)',
          caroClausePresent: false,
          overallCompliant: true
        }
      };
    }
    
    const caroResult = await processSingleDocumentCheck(
      caroFile,
      'caro_clause_ix_check',
      'caro_clause_ix_defaults',
      parameters
    );
    
    const isCompliant = annualReportResult.isCompliant && caroResult.isCompliant;
    
    return {
      isCompliant,
      explanation: isCompliant
        ? `✅ COMPLIANT: Defaults keywords found in annual report (${annualReportResult.extractedData?.foundKeywords?.join(', ')}), and CARO clause (ix) is properly present and addresses default-related matters. Cross-document compliance maintained.`
        : `❌ NON-COMPLIANT: Defaults keywords found in annual report (${annualReportResult.extractedData?.foundKeywords?.join(', ')}), but CARO clause (ix) is ${caroResult.isCompliant ? 'present but inadequate' : 'missing'}. When annual report mentions defaults or wilful defaulter status, corresponding CARO clause (ix) must be included.`,
      confidence: 0.9,
      annualReportFindings: {
        keywordsFound: annualReportResult.extractedData?.foundKeywords || [],
        contexts: annualReportResult.extractedData?.contexts || [],
        totalMatches: annualReportResult.extractedData?.totalMatches || 0
      },
      caroClauseAnalysis: {
        clausePresent: caroResult.isCompliant,
        clauseContent: caroResult.explanation,
        complianceStatus: caroResult.isCompliant ? 'Compliant' : 'Non-compliant',
        clauseNumber: '(ix)'
      },
      crossComplianceStatus: {
        keywordsFoundInAnnualReport: true,
        expectedCaroClause: '(ix)',
        caroClausePresent: caroResult.isCompliant,
        overallCompliant: isCompliant
      }
    };
    
  } catch (error) {
    console.error('Error processing Annual Report + CARO Defaults check:', error);
    return {
      isCompliant: false,
      explanation: `Technical error during Annual Report + CARO Defaults cross-compliance check: ${error instanceof Error ? error.message : String(error)}`,
      confidence: 0.5,
      crossComplianceStatus: {
        keywordsFoundInAnnualReport: false,
        expectedCaroClause: '(ix)',
        caroClausePresent: false,
        overallCompliant: false
      }
    };
  }
}

/**
 * Process Annual Report + CARO Rights Issue Check
 * Keywords: "Rights issue"
 * Expected CARO Clause: (x)(b)
 */
export async function processAnnualReportCaroRightsIssue(
  annualReportFile: File,
  caroFile: File,
  parameters: Record<string, any>
): Promise<AnnualReportCaroCheckResult> {
  try {
    console.log('Processing Annual Report + CARO Rights Issue check');
    
    const annualReportResult = await checkRightsIssueKeywords(annualReportFile);
    
    if (!annualReportResult.isCompliant) {
      return {
        isCompliant: true,
        explanation: 'No rights issue keywords found in annual report. CARO clause (x)(b) analysis not required for this cross-compliance check.',
        confidence: 0.9,
        annualReportFindings: {
          keywordsFound: [],
          contexts: [],
          totalMatches: 0
        },
        caroClauseAnalysis: {
          clausePresent: false,
          clauseContent: 'Not required - no triggering keywords found',
          complianceStatus: 'Not applicable',
          clauseNumber: '(x)(b)'
        },
        crossComplianceStatus: {
          keywordsFoundInAnnualReport: false,
          expectedCaroClause: '(x)(b)',
          caroClausePresent: false,
          overallCompliant: true
        }
      };
    }
    
    const caroResult = await processSingleDocumentCheck(
      caroFile,
      'caro_clause_x_b_check',
      'caro_clause_x_b_rights_issue',
      parameters
    );
    
    const isCompliant = annualReportResult.isCompliant && caroResult.isCompliant;
    
    return {
      isCompliant,
      explanation: isCompliant
        ? `✅ COMPLIANT: Rights issue keywords found in annual report, and CARO clause (x)(b) is properly present and addresses rights issue matters. Cross-document compliance maintained.`
        : `❌ NON-COMPLIANT: Rights issue keywords found in annual report, but CARO clause (x)(b) is ${caroResult.isCompliant ? 'present but inadequate' : 'missing'}. When annual report mentions rights issue of shares or convertible debentures, corresponding CARO clause (x)(b) must be included.`,
      confidence: 0.9,
      annualReportFindings: {
        keywordsFound: annualReportResult.extractedData?.foundKeywords || [],
        contexts: annualReportResult.extractedData?.contexts || [],
        totalMatches: annualReportResult.extractedData?.totalMatches || 0
      },
      caroClauseAnalysis: {
        clausePresent: caroResult.isCompliant,
        clauseContent: caroResult.explanation,
        complianceStatus: caroResult.isCompliant ? 'Compliant' : 'Non-compliant',
        clauseNumber: '(x)(b)'
      },
      crossComplianceStatus: {
        keywordsFoundInAnnualReport: true,
        expectedCaroClause: '(x)(b)',
        caroClausePresent: caroResult.isCompliant,
        overallCompliant: isCompliant
      }
    };
    
  } catch (error) {
    console.error('Error processing Annual Report + CARO Rights Issue check:', error);
    return {
      isCompliant: false,
      explanation: `Technical error during Annual Report + CARO Rights Issue cross-compliance check: ${error instanceof Error ? error.message : String(error)}`,
      confidence: 0.5,
      crossComplianceStatus: {
        keywordsFoundInAnnualReport: false,
        expectedCaroClause: '(x)(b)',
        caroClausePresent: false,
        overallCompliant: false
      }
    };
  }
}

/**
 * Process Annual Report + CARO Fraud Check
 * Keywords: "fraud"
 * Expected CARO Clause: (xi)(a)
 */
export async function processAnnualReportCaroFraud(
  annualReportFile: File,
  caroFile: File,
  parameters: Record<string, any>
): Promise<AnnualReportCaroCheckResult> {
  try {
    console.log('Processing Annual Report + CARO Fraud check');
    
    const annualReportResult = await checkFraudKeywords(annualReportFile);
    
    if (!annualReportResult.isCompliant) {
      return {
        isCompliant: true,
        explanation: 'No fraud keywords found in annual report. CARO clause (xi)(a) analysis not required for this cross-compliance check.',
        confidence: 0.9,
        annualReportFindings: {
          keywordsFound: [],
          contexts: [],
          totalMatches: 0
        },
        caroClauseAnalysis: {
          clausePresent: false,
          clauseContent: 'Not required - no triggering keywords found',
          complianceStatus: 'Not applicable',
          clauseNumber: '(xi)(a)'
        },
        crossComplianceStatus: {
          keywordsFoundInAnnualReport: false,
          expectedCaroClause: '(xi)(a)',
          caroClausePresent: false,
          overallCompliant: true
        }
      };
    }
    
    const caroResult = await processSingleDocumentCheck(
      caroFile,
      'caro_clause_xi_a_check',
      'caro_clause_xi_a_fraud',
      parameters
    );
    
    const isCompliant = annualReportResult.isCompliant && caroResult.isCompliant;
    
    return {
      isCompliant,
      explanation: isCompliant
        ? `✅ COMPLIANT: Fraud keywords found in annual report, and CARO clause (xi)(a) is properly present and addresses fraud matters. Cross-document compliance maintained.`
        : `❌ NON-COMPLIANT: Fraud keywords found in annual report, but CARO clause (xi)(a) is ${caroResult.isCompliant ? 'present but inadequate' : 'missing'}. When annual report mentions fraud, corresponding CARO clause (xi)(a) must be included.`,
      confidence: 0.9,
      annualReportFindings: {
        keywordsFound: annualReportResult.extractedData?.foundKeywords || [],
        contexts: annualReportResult.extractedData?.contexts || [],
        totalMatches: annualReportResult.extractedData?.totalMatches || 0
      },
      caroClauseAnalysis: {
        clausePresent: caroResult.isCompliant,
        clauseContent: caroResult.explanation,
        complianceStatus: caroResult.isCompliant ? 'Compliant' : 'Non-compliant',
        clauseNumber: '(xi)(a)'
      },
      crossComplianceStatus: {
        keywordsFoundInAnnualReport: true,
        expectedCaroClause: '(xi)(a)',
        caroClausePresent: caroResult.isCompliant,
        overallCompliant: isCompliant
      }
    };
    
  } catch (error) {
    console.error('Error processing Annual Report + CARO Fraud check:', error);
    return {
      isCompliant: false,
      explanation: `Technical error during Annual Report + CARO Fraud cross-compliance check: ${error instanceof Error ? error.message : String(error)}`,
      confidence: 0.5,
      crossComplianceStatus: {
        keywordsFoundInAnnualReport: false,
        expectedCaroClause: '(xi)(a)',
        caroClausePresent: false,
        overallCompliant: false
      }
    };
  }
}

/**
 * Process Annual Report + CARO Whistle-blower Check
 * Keywords: "whistle-blower", "whistleblower"
 * Expected CARO Clause: (xi)(c)
 */
export async function processAnnualReportCaroWhistleBlower(
  annualReportFile: File,
  caroFile: File,
  parameters: Record<string, any>
): Promise<AnnualReportCaroCheckResult> {
  try {
    console.log('Processing Annual Report + CARO Whistle-blower check');
    
    const annualReportResult = await checkWhistleBlowerKeywords(annualReportFile);
    
    if (!annualReportResult.isCompliant) {
      return {
        isCompliant: true,
        explanation: 'No whistle-blower keywords found in annual report. CARO clause (xi)(c) analysis not required for this cross-compliance check.',
        confidence: 0.9,
        annualReportFindings: {
          keywordsFound: [],
          contexts: [],
          totalMatches: 0
        },
        caroClauseAnalysis: {
          clausePresent: false,
          clauseContent: 'Not required - no triggering keywords found',
          complianceStatus: 'Not applicable',
          clauseNumber: '(xi)(c)'
        },
        crossComplianceStatus: {
          keywordsFoundInAnnualReport: false,
          expectedCaroClause: '(xi)(c)',
          caroClausePresent: false,
          overallCompliant: true
        }
      };
    }
    
    const caroResult = await processSingleDocumentCheck(
      caroFile,
      'caro_clause_xi_c_check',
      'caro_clause_xi_c_whistleblower',
      parameters
    );
    
    const isCompliant = annualReportResult.isCompliant && caroResult.isCompliant;
    
    return {
      isCompliant,
      explanation: isCompliant
        ? `✅ COMPLIANT: Whistle-blower keywords found in annual report, and CARO clause (xi)(c) is properly present and addresses whistle-blower complaint handling. Cross-document compliance maintained.`
        : `❌ NON-COMPLIANT: Whistle-blower keywords found in annual report, but CARO clause (xi)(c) is ${caroResult.isCompliant ? 'present but inadequate' : 'missing'}. When annual report mentions whistle-blower mechanisms or complaints, corresponding CARO clause (xi)(c) must be included.`,
      confidence: 0.9,
      annualReportFindings: {
        keywordsFound: annualReportResult.extractedData?.foundKeywords || [],
        contexts: annualReportResult.extractedData?.contexts || [],
        totalMatches: annualReportResult.extractedData?.totalMatches || 0
      },
      caroClauseAnalysis: {
        clausePresent: caroResult.isCompliant,
        clauseContent: caroResult.explanation,
        complianceStatus: caroResult.isCompliant ? 'Compliant' : 'Non-compliant',
        clauseNumber: '(xi)(c)'
      },
      crossComplianceStatus: {
        keywordsFoundInAnnualReport: true,
        expectedCaroClause: '(xi)(c)',
        caroClausePresent: caroResult.isCompliant,
        overallCompliant: isCompliant
      }
    };
    
  } catch (error) {
    console.error('Error processing Annual Report + CARO Whistle-blower check:', error);
    return {
      isCompliant: false,
      explanation: `Technical error during Annual Report + CARO Whistle-blower cross-compliance check: ${error instanceof Error ? error.message : String(error)}`,
      confidence: 0.5,
      crossComplianceStatus: {
        keywordsFoundInAnnualReport: false,
        expectedCaroClause: '(xi)(c)',
        caroClausePresent: false,
        overallCompliant: false
      }
    };
  }
}

/**
 * Process Annual Report + CARO Cost Records Check
 * Keywords: "Cost records", "Cost Auditor"
 * Expected CARO Clause: (vi)
 * Note: Typically applies when turnover from related products/services ≥ ₹35 crores
 */
export async function processAnnualReportCaroCostRecords(
  annualReportFile: File,
  caroFile: File,
  parameters: Record<string, any>
): Promise<AnnualReportCaroCheckResult> {
  try {
    console.log('Processing Annual Report + CARO Cost Records check');
    
    const annualReportResult = await checkCostRecordsKeywords(annualReportFile);
    
    if (!annualReportResult.isCompliant) {
      return {
        isCompliant: true,
        explanation: 'No cost records or cost auditor keywords found in annual report. CARO clause (vi) analysis not required for this cross-compliance check.',
        confidence: 0.9,
        annualReportFindings: {
          keywordsFound: [],
          contexts: [],
          totalMatches: 0
        },
        caroClauseAnalysis: {
          clausePresent: false,
          clauseContent: 'Not required - no triggering keywords found',
          complianceStatus: 'Not applicable',
          clauseNumber: '(vi)'
        },
        crossComplianceStatus: {
          keywordsFoundInAnnualReport: false,
          expectedCaroClause: '(vi)',
          caroClausePresent: false,
          overallCompliant: true
        }
      };
    }
    
    const caroResult = await processSingleDocumentCheck(
      caroFile,
      'caro_clause_vi_check',
      'caro_clause_vi_cost_records',
      parameters
    );
    
    const isCompliant = annualReportResult.isCompliant && caroResult.isCompliant;
    
    return {
      isCompliant,
      explanation: isCompliant
        ? `✅ COMPLIANT: Cost records keywords found in annual report, and CARO clause (vi) is properly present and addresses cost records maintenance requirements. Cross-document compliance maintained.`
        : `❌ NON-COMPLIANT: Cost records keywords found in annual report, but CARO clause (vi) is ${caroResult.isCompliant ? 'present but inadequate' : 'missing'}. When annual report mentions cost records or cost auditor, corresponding CARO clause (vi) must be included if turnover threshold (₹35 crores) is met.`,
      confidence: 0.9,
      annualReportFindings: {
        keywordsFound: annualReportResult.extractedData?.foundKeywords || [],
        contexts: annualReportResult.extractedData?.contexts || [],
        totalMatches: annualReportResult.extractedData?.totalMatches || 0
      },
      caroClauseAnalysis: {
        clausePresent: caroResult.isCompliant,
        clauseContent: caroResult.explanation,
        complianceStatus: caroResult.isCompliant ? 'Compliant' : 'Non-compliant',
        clauseNumber: '(vi)'
      },
      crossComplianceStatus: {
        keywordsFoundInAnnualReport: true,
        expectedCaroClause: '(vi)',
        caroClausePresent: caroResult.isCompliant,
        overallCompliant: isCompliant
      }
    };
    
  } catch (error) {
    console.error('Error processing Annual Report + CARO Cost Records check:', error);
    return {
      isCompliant: false,
      explanation: `Technical error during Annual Report + CARO Cost Records cross-compliance check: ${error instanceof Error ? error.message : String(error)}`,
      confidence: 0.5,
      crossComplianceStatus: {
        keywordsFoundInAnnualReport: false,
        expectedCaroClause: '(vi)',
        caroClausePresent: false,
        overallCompliant: false
      }
    };
  }
}

/**
 * Process all Annual Report + CARO checks in batch
 */
export async function processAllAnnualReportCaroChecks(
  annualReportFile: File,
  caroFile: File,
  parameters: Record<string, any>,
  onProgress?: (current: number, total: number, checkName: string) => void
): Promise<Record<string, AnnualReportCaroCheckResult>> {
  
  const checks = [
    { name: 'Income Tax Raids', processor: processAnnualReportCaroIncomeTax, id: 'annual_report_caro_income_tax' },
    { name: 'Defaults/Wilful Defaulter', processor: processAnnualReportCaroDefaults, id: 'annual_report_caro_defaults' },
    { name: 'Rights Issue', processor: processAnnualReportCaroRightsIssue, id: 'annual_report_caro_rights_issue' },
    { name: 'Fraud', processor: processAnnualReportCaroFraud, id: 'annual_report_caro_fraud' },
    { name: 'Whistle-blower', processor: processAnnualReportCaroWhistleBlower, id: 'annual_report_caro_whistleblower' },
    { name: 'Cost Records', processor: processAnnualReportCaroCostRecords, id: 'annual_report_caro_cost_records' }
  ];
  
  const results: Record<string, AnnualReportCaroCheckResult> = {};
  
  console.log(`🚀 Processing ${checks.length} Annual Report + CARO cross-compliance checks`);
  
  for (let i = 0; i < checks.length; i++) {
    const check = checks[i];
    
    if (onProgress) {
      onProgress(i, checks.length, `Annual Report + CARO: ${check.name}`);
    }
    
    console.log(`📋 Processing check ${i + 1}/${checks.length}: ${check.name}`);
    
    try {
      const startTime = Date.now();
      const result = await check.processor(annualReportFile, caroFile, parameters);
      const duration = Date.now() - startTime;
      
      results[check.id] = result;
      
      console.log(`${result.isCompliant ? '✅' : '❌'} ${check.name}: ${result.isCompliant ? 'COMPLIANT' : 'NON-COMPLIANT'} (${duration}ms)`);
      
      if (result.crossComplianceStatus?.keywordsFoundInAnnualReport) {
        console.log(`   📝 Keywords found: ${result.annualReportFindings?.keywordsFound.join(', ')}`);
        console.log(`   📋 Expected CARO clause: ${result.crossComplianceStatus.expectedCaroClause}`);
        console.log(`   ✅ CARO clause present: ${result.crossComplianceStatus.caroClausePresent ? 'Yes' : 'No'}`);
      } else {
        console.log(`   📝 No triggering keywords found in annual report`);
      }
      
      // Small delay between checks
      await new Promise(resolve => setTimeout(resolve, 300));
      
    } catch (error) {
      console.error(`❌ Error processing ${check.name}:`, error);
      results[check.id] = {
        isCompliant: false,
        explanation: `Error processing ${check.name}: ${error instanceof Error ? error.message : String(error)}`,
        confidence: 0.5,
        crossComplianceStatus: {
          keywordsFoundInAnnualReport: false,
          expectedCaroClause: 'unknown',
          caroClausePresent: false,
          overallCompliant: false
        }
      };
    }
  }
  
  // Final progress update
  if (onProgress) {
    onProgress(checks.length, checks.length, 'Annual Report + CARO checks complete');
  }
  
  console.log(`🎉 Completed ${Object.keys(results).length} Annual Report + CARO cross-compliance checks`);
  
  const compliantCount = Object.values(results).filter(r => r.isCompliant).length;
  console.log(`📊 Results: ${compliantCount}/${Object.keys(results).length} compliant`);
  
  return results;
}

/**
 * Get summary of Annual Report + CARO check results
 */
export function getAnnualReportCaroSummary(results: Record<string, AnnualReportCaroCheckResult>): {
  totalChecks: number;
  compliantChecks: number;
  checksWithKeywords: number;
  checksRequiringCaroClauses: number;
  compliancePercentage: number;
  keywordFindings: Array<{checkId: string, keywords: string[], expectedClause: string}>;
} {
  const totalChecks = Object.keys(results).length;
  const compliantChecks = Object.values(results).filter(r => r.isCompliant).length;
  const checksWithKeywords = Object.values(results).filter(r => 
    r.crossComplianceStatus?.keywordsFoundInAnnualReport === true
  ).length;
  const checksRequiringCaroClauses = checksWithKeywords; // Same as checks with keywords
  const compliancePercentage = totalChecks > 0 ? Math.round((compliantChecks / totalChecks) * 100) : 0;
  
  const keywordFindings = Object.entries(results)
    .filter(([_, result]) => result.crossComplianceStatus?.keywordsFoundInAnnualReport === true)
    .map(([checkId, result]) => ({
      checkId,
      keywords: result.annualReportFindings?.keywordsFound || [],
      expectedClause: result.crossComplianceStatus?.expectedCaroClause || 'unknown'
    }));
  
  return {
    totalChecks,
    compliantChecks,
    checksWithKeywords,
    checksRequiringCaroClauses,
    compliancePercentage,
    keywordFindings
  };
}

/**
 * Debug function for Annual Report + CARO checks
 */
export function debugAnnualReportCaroCheck(
  checkId: string,
  annualReportFile: File | undefined,
  caroFile: File | undefined,
  parameters: Record<string, any>
): {
  checkFound: boolean;
  hasRequiredDocuments: boolean;
  missingDocuments: string[];
  conditionsMet: boolean;
  checkDetails: {
    name: string;
    expectedKeywords: string[];
    expectedCaroClause: string;
    applicableReportTypes: string[];
  };
} {
  const checkRegistry = {
    'annual_report_caro_income_tax': {
      name: 'Income Tax Raids Check',
      expectedKeywords: ['Income tax raids', 'Raids by Income tax authorities', 'Unrecorded income'],
      expectedCaroClause: '(viii)',
      applicableReportTypes: ['Normal', 'Standalone']
    },
    'annual_report_caro_defaults': {
      name: 'Defaults/Wilful Defaulter Check',
      expectedKeywords: ['Defaults', 'Wilful Defaulter', 'Diversion'],
      expectedCaroClause: '(ix)',
      applicableReportTypes: ['Normal', 'Standalone']
    },
    'annual_report_caro_rights_issue': {
      name: 'Rights Issue Check',
      expectedKeywords: ['Rights issue'],
      expectedCaroClause: '(x)(b)',
      applicableReportTypes: ['Normal', 'Standalone']
    },
    'annual_report_caro_fraud': {
      name: 'Fraud Check',
      expectedKeywords: ['fraud'],
      expectedCaroClause: '(xi)(a)',
      applicableReportTypes: ['Normal', 'Standalone']
    },
    'annual_report_caro_whistleblower': {
      name: 'Whistle-blower Check',
      expectedKeywords: ['whistle-blower', 'whistleblower'],
      expectedCaroClause: '(xi)(c)',
      applicableReportTypes: ['Normal', 'Standalone']
    },
    'annual_report_caro_cost_records': {
      name: 'Cost Records Check',
      expectedKeywords: ['Cost records', 'Cost Auditor'],
      expectedCaroClause: '(vi)',
      applicableReportTypes: ['Normal', 'Standalone']
    }
  };
  
  const check = checkRegistry[checkId as keyof typeof checkRegistry];
  
  if (!check) {
    return {
      checkFound: false,
      hasRequiredDocuments: false,
      missingDocuments: ['annual_report', 'annexure_a'],
      conditionsMet: false,
      checkDetails: {
        name: 'Unknown Check',
        expectedKeywords: [],
        expectedCaroClause: 'unknown',
        applicableReportTypes: []
      }
    };
  }
  
  const missingDocuments: string[] = [];
  if (!annualReportFile) missingDocuments.push('annual_report');
  if (!caroFile) missingDocuments.push('annexure_a');
  
  const hasRequiredDocuments = missingDocuments.length === 0;
  
  // Check conditions (audit report type)
  const auditReportType = parameters.audit_report_type;
  const conditionsMet = check.applicableReportTypes.includes(auditReportType);
  
  return {
    checkFound: true,
    hasRequiredDocuments,
    missingDocuments,
    conditionsMet,
    checkDetails: check
  };
}

/**
 * Validate Annual Report + CARO check parameters
 */
export function validateAnnualReportCaroParameters(
  documents: { annual_report?: File; annexure_a?: File },
  parameters: Record<string, any>
): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  applicableChecks: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];
  const applicableChecks: string[] = [];
  
  // Document validation
  if (!documents.annual_report) {
    errors.push('Annual Report is required for Annual Report + CARO cross-compliance checks');
  } else {
    if (documents.annual_report.type !== 'application/pdf') {
      errors.push('Annual Report must be a PDF file');
    }
    if (documents.annual_report.size === 0) {
      errors.push('Annual Report file is empty');
    }
    if (documents.annual_report.size > 50 * 1024 * 1024) {
      warnings.push('Annual Report file is very large and may slow processing');
    }
  }
  
  if (!documents.annexure_a) {
    errors.push('CARO Annexure A is required for Annual Report + CARO cross-compliance checks');
  } else {
    if (documents.annexure_a.type !== 'application/pdf') {
      errors.push('CARO Annexure A must be a PDF file');
    }
    if (documents.annexure_a.size === 0) {
      errors.push('CARO Annexure A file is empty');
    }
  }
  
  // Parameter validation
  const auditReportType = parameters.audit_report_type;
  if (!auditReportType) {
    errors.push('Audit report type is required');
  } else if (!['Normal', 'Standalone', 'Consolidated'].includes(auditReportType)) {
    errors.push(`Invalid audit report type: ${auditReportType}`);
  } else if (auditReportType === 'Consolidated') {
    warnings.push('Annual Report + CARO checks are primarily designed for Normal/Standalone reports');
  } else {
    // Add applicable checks for Normal/Standalone
    applicableChecks.push(
      'annual_report_caro_income_tax',
      'annual_report_caro_defaults',
      'annual_report_caro_rights_issue',
      'annual_report_caro_fraud',
      'annual_report_caro_whistleblower',
      'annual_report_caro_cost_records'
    );
  }
  
  if (!parameters.company_name) {
    warnings.push('Company name not provided - may affect check descriptions');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    applicableChecks
  };
}

/**
 * Generate Annual Report + CARO check configuration
 */
export function getAnnualReportCaroCheckConfiguration(): {
  totalChecks: number;
  checkDetails: Array<{
    id: string;
    name: string;
    description: string;
    keywords: string[];
    expectedCaroClause: string;
    method: 'text_search' | 'ai_analysis';
    documentTypes: string[];
  }>;
  processingFlow: string[];
} {
  const checkDetails = [
    {
      id: 'annual_report_caro_income_tax',
      name: 'Income Tax Raids Cross-Compliance',
      description: 'Searches Annual Report for income tax raid keywords and verifies corresponding CARO clause (viii)',
      keywords: ['Income tax raids', 'Raids by Income tax authorities', 'Unrecorded income'],
      expectedCaroClause: '(viii)',
      method: 'text_search' as const,
      documentTypes: ['annual_report', 'annexure_a']
    },
    {
      id: 'annual_report_caro_defaults',
      name: 'Defaults/Wilful Defaulter Cross-Compliance',
      description: 'Searches Annual Report for default keywords and verifies corresponding CARO clause (ix)',
      keywords: ['Defaults', 'Wilful Defaulter', 'Diversion'],
      expectedCaroClause: '(ix)',
      method: 'text_search' as const,
      documentTypes: ['annual_report', 'annexure_a']
    },
    {
      id: 'annual_report_caro_rights_issue',
      name: 'Rights Issue Cross-Compliance',
      description: 'Searches Annual Report for rights issue keywords and verifies corresponding CARO clause (x)(b)',
      keywords: ['Rights issue'],
      expectedCaroClause: '(x)(b)',
      method: 'text_search' as const,
      documentTypes: ['annual_report', 'annexure_a']
    },
    {
      id: 'annual_report_caro_fraud',
      name: 'Fraud Cross-Compliance',
      description: 'Searches Annual Report for fraud keywords and verifies corresponding CARO clause (xi)(a)',
      keywords: ['fraud'],
      expectedCaroClause: '(xi)(a)',
      method: 'text_search' as const,
      documentTypes: ['annual_report', 'annexure_a']
    },
    {
      id: 'annual_report_caro_whistleblower',
      name: 'Whistle-blower Cross-Compliance',
      description: 'Searches Annual Report for whistle-blower keywords and verifies corresponding CARO clause (xi)(c)',
      keywords: ['whistle-blower', 'whistleblower'],
      expectedCaroClause: '(xi)(c)',
      method: 'text_search' as const,
      documentTypes: ['annual_report', 'annexure_a']
    },
    {
      id: 'annual_report_caro_cost_records',
      name: 'Cost Records Cross-Compliance',
      description: 'Searches Annual Report for cost records keywords and verifies corresponding CARO clause (vi)',
      keywords: ['Cost records', 'Cost Auditor'],
      expectedCaroClause: '(vi)',
      method: 'text_search' as const,
      documentTypes: ['annual_report', 'annexure_a']
    }
  ];
  
  const processingFlow = [
    '1. Extract text from Annual Report PDF',
    '2. Search for specific keywords using Ctrl+F logic',
    '3. If keywords found, analyze CARO Annexure A using Gemini AI',
    '4. Extract and verify expected CARO clause',
    '5. Cross-validate compliance between documents',
    '6. Generate detailed compliance report'
  ];
  
  return {
    totalChecks: checkDetails.length,
    checkDetails,
    processingFlow
  };
}

/**
 * Export main processing functions for use by other modules
 */
export const AnnualReportCaroProcessors = {
  processIncomeTax: processAnnualReportCaroIncomeTax,
  processDefaults: processAnnualReportCaroDefaults,
  processRightsIssue: processAnnualReportCaroRightsIssue,
  processFraud: processAnnualReportCaroFraud,
  processWhistleBlower: processAnnualReportCaroWhistleBlower,
  processCostRecords: processAnnualReportCaroCostRecords,
  processAll: processAllAnnualReportCaroChecks
};

/**
 * Export utility functions
 */
export const AnnualReportCaroUtils = {
  getSummary: getAnnualReportCaroSummary,
  debug: debugAnnualReportCaroCheck,
  validate: validateAnnualReportCaroParameters,
  getConfiguration: getAnnualReportCaroCheckConfiguration
};