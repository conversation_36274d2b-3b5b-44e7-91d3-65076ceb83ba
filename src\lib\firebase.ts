// firebase.ts
import { initializeApp } from "firebase/app";
import { getAuth } from "firebase/auth";
import { getStorage, ref as storageRef, getMetadata } from "firebase/storage";
import { getDatabase } from "firebase/database";

// Your web app's Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyC2tY3W2bet6tfCVlU_V77gYiP9jXf6nzY",
  authDomain: "checklistaicap.firebaseapp.com",
  databaseURL: "https://checklistaicap-default-rtdb.firebaseio.com",
  projectId: "checklistaicap",
  storageBucket: "checklistaicap.firebasestorage.app",
  messagingSenderId: "924373606572",
  appId: "1:924373606572:web:87684cb58403bc7a723f09"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const storage = getStorage(app);
const database = getDatabase(app);

// Create a cache for downloaded URLs to avoid repeated Firebase Storage calls
export const storageCache = new Map<string, {
  url: string;
  timestamp: number;
  size: number;
}>();

// Maximum cache size (100MB)
const MAX_CACHE_SIZE = 100 * 1024 * 1024;
let currentCacheSize = 0;

// Cache expiration time (30 minutes)
const CACHE_EXPIRATION = 30 * 60 * 1000;

/**
 * Add a URL to the storage cache
 */
export const addToStorageCache = (path: string, url: string, size: number = 0) => {
  // If adding this would exceed the cache size, clear old entries
  if (currentCacheSize + size > MAX_CACHE_SIZE) {
    // Sort by timestamp (oldest first)
    const entries = Array.from(storageCache.entries())
      .sort((a, b) => a[1].timestamp - b[1].timestamp);

    // Remove oldest entries until we have enough space
    while (currentCacheSize + size > MAX_CACHE_SIZE && entries.length > 0) {
      const [oldestKey, oldestValue] = entries.shift()!;
      storageCache.delete(oldestKey);
      currentCacheSize -= oldestValue.size;
      console.log(`Removed ${oldestKey} from storage cache to free up space`);
    }
  }

  // Add to cache
  storageCache.set(path, {
    url,
    timestamp: Date.now(),
    size
  });

  currentCacheSize += size;
  console.log(`Added ${path} to storage cache (${size} bytes)`);
};

/**
 * Get a URL from the storage cache if it exists and is not expired
 */
export const getFromStorageCache = (path: string): string | null => {
  const cached = storageCache.get(path);

  if (!cached) {
    return null;
  }

  // Check if expired
  if (Date.now() - cached.timestamp > CACHE_EXPIRATION) {
    storageCache.delete(path);
    currentCacheSize -= cached.size;
    console.log(`Cache entry for ${path} expired and was removed`);
    return null;
  }

  console.log(`Cache hit for ${path}`);
  return cached.url;
};

export { app, auth, storage, database };