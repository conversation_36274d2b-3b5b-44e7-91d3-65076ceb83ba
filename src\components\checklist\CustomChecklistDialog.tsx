import React, { useState } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { CustomChecklistItem } from '@/lib/types/checklist';
import { checkTypeDefinitions, CustomCheckType } from '@/lib/types/CustomCheckTypeSystem';

interface CustomChecklistDialogProps {
  open: boolean;
  onClose: () => void;
  onSave: (item: Omit<CustomChecklistItem, 'id' | 'createdAt' | 'updatedAt'>) => void;
  initialData?: CustomChecklistItem;
}

const documentTypes = [
  { id: 'auditReport', label: 'Audit Report' },
  { id: 'annexureA', label: 'Annexure A - CARO' },
  { id: 'annexureB', label: 'Annexure B - IFC' },
  { id: 'balanceSheet', label: 'Balance Sheet' }, 
  { id: 'notes', label: 'Notes to Accounts' },
  { id: 'plNotes', label: 'P&L Notes' },
  { id: 'annualReport', label: 'Annual Report' },
  { id: 'secretarialCompliance', label: 'Secretarial Compliance Report' }
];

export const CustomChecklistDialog = ({ open, onClose, onSave, initialData }: CustomChecklistDialogProps) => {
  const [formData, setFormData] = useState<Omit<CustomChecklistItem, 'id' | 'createdAt' | 'updatedAt'>>(
    initialData || {
      name: '',
      description: '',
      type: 'single',
      documentTypes: [],
      checkType: '',
      parameters: {}
    }
  );

  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!formData.name) {
      errors.name = 'Name is required';
    }
    if (!formData.description) {
      errors.description = 'Description is required';
    }
    if (!formData.documentTypes.length) {
      errors.documentTypes = 'At least one document type must be selected';
    }
    if (!formData.checkType) {
      errors.checkType = 'Check type must be selected';
    } else {
      const checkTypeDef = checkTypeDefinitions[formData.checkType as CustomCheckType];
      if (checkTypeDef) {
        const { isValid, error } = checkTypeDef.validate(formData.parameters || {});
        if (!isValid) {
          errors.parameters = error || 'Invalid parameters';
        }
      }
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      onSave(formData);
    }
  };

  const handleDocumentTypeToggle = (id: string) => {
    setFormData(prev => ({
      ...prev,
      documentTypes: prev.documentTypes.includes(id as any)
        ? prev.documentTypes.filter(t => t !== id)
        : [...prev.documentTypes, id] as any[]
    }));
  };

  const handleCheckTypeChange = (checkType: CustomCheckType) => {
    const checkTypeDef = checkTypeDefinitions[checkType];
    setFormData(prev => ({
      ...prev,
      checkType,
      type: checkTypeDef.supportsMultiDoc ? 'multi' : 'single',
      parameters: {}
    }));
  };

  const getParameterInput = (paramDef: any, paramName: string) => {
    const value = formData.parameters?.[paramName] || '';
    const onChange = (newValue: any) => {
      setFormData(prev => ({
        ...prev,
        parameters: {
          ...prev.parameters,
          [paramName]: newValue
        }
      }));
    };

    switch (paramDef.type) {
      case 'string':
      case 'regex':
        return (
          <Input 
            value={value} 
            onChange={e => onChange(e.target.value)}
            placeholder={paramDef.description}
          />
        );
      case 'number':
        return (
          <Input 
            type="number"
            value={value} 
            onChange={e => onChange(parseFloat(e.target.value))}
            placeholder={paramDef.description}
          />
        );
      case 'boolean':
        return (
          <Checkbox
            checked={value}
            onCheckedChange={checked => onChange(checked)}
          />
        );
      case 'array':
        return (
          <Input
            value={Array.isArray(value) ? value.join(', ') : value}
            onChange={e => onChange(e.target.value.split(',').map(v => v.trim()))}
            placeholder={paramDef.description}
          />
        );
      default:
        return null;
    }
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>{initialData ? 'Edit' : 'Add'} Custom Checklist Item</DialogTitle>
            <DialogDescription>
              Create a new checklist item to validate specific requirements in your documents.
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="name">Name</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Checklist item name"
                className={validationErrors.name ? 'border-red-500' : ''}
                required
              />
              {validationErrors.name && (
                <span className="text-sm text-red-500">{validationErrors.name}</span>
              )}
            </div>

            <div className="grid gap-2">
              <Label htmlFor="description">Description</Label>
              <Input
                id="description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Description of what this checklist item verifies"
                className={validationErrors.description ? 'border-red-500' : ''}
                required
              />
              {validationErrors.description && (
                <span className="text-sm text-red-500">{validationErrors.description}</span>
              )}
            </div>

            <div className="grid gap-2">
              <Label htmlFor="checkType">Check Type</Label>
              <Select
                value={formData.checkType}
                onValueChange={value => handleCheckTypeChange(value as CustomCheckType)}
              >
                <SelectTrigger id="checkType" className={validationErrors.checkType ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Select check type" />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(checkTypeDefinitions).map(([type, def]) => (
                    <SelectItem key={type} value={type}>
                      {def.description}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {validationErrors.checkType && (
                <span className="text-sm text-red-500">{validationErrors.checkType}</span>
              )}
            </div>

            <div className="grid gap-2">
              <Label>Required Documents</Label>
              <div className="grid grid-cols-2 gap-2">
                {documentTypes.map(type => {
                  // Only show document types that are applicable to the selected check type
                  const checkTypeDef = formData.checkType ? checkTypeDefinitions[formData.checkType as CustomCheckType] : null;
                  if (checkTypeDef && !checkTypeDef.applicableToDocTypes.includes(type.id)) {
                    return null;
                  }

                  return (
                    <div key={type.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={`dt-${type.id}`}
                        checked={formData.documentTypes.includes(type.id as any)}
                        onCheckedChange={() => handleDocumentTypeToggle(type.id)}
                      />
                      <Label htmlFor={`dt-${type.id}`}>{type.label}</Label>
                    </div>
                  );
                })}
              </div>
              {validationErrors.documentTypes && (
                <span className="text-sm text-red-500">{validationErrors.documentTypes}</span>
              )}
            </div>

            {/* Parameters section */}
            {formData.checkType && (
              <div className="grid gap-2">
                <Label>Parameters</Label>
                {checkTypeDefinitions[formData.checkType as CustomCheckType].parameters.map(param => (
                  <div key={param.name} className="grid gap-2">
                    <Label htmlFor={param.name}>
                      {param.name}
                      {param.required && <span className="text-red-500">*</span>}
                    </Label>
                    {getParameterInput(param, param.name)}
                  </div>
                ))}
                {validationErrors.parameters && (
                  <span className="text-sm text-red-500">{validationErrors.parameters}</span>
                )}
              </div>
            )}
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose}>Cancel</Button>
            <Button type="submit">Save</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};
