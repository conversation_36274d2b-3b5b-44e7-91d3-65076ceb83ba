import React, { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { getUserDocuments, deleteDocument, DocumentInfo, uploadFile } from "@/lib/storageService";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { FileText, Trash2, Eye, Upload, CheckCircle, Clock } from "lucide-react";
import { format } from "date-fns";
import { useToast } from "@/hooks/use-toast";
import PDFUploader from "@/components/analyzer/PDFUploader";
import { database } from "@/lib/firebase";
import { ref, push, set } from "firebase/database";
import { Progress } from "@/components/ui/progress";
import DocumentViewer from "@/components/common/DocumentViewer";

const Documents = () => {
  const { currentUser } = useAuth();
  const { toast } = useToast();
  const [documents, setDocuments] = useState<DocumentInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [uploadingDoc, setUploadingDoc] = useState<File | null>(null);
  const [documentType, setDocumentType] = useState<string>("audit_report");
  const [uploading, setUploading] = useState(false);
  const [viewerOpen, setViewerOpen] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState<DocumentInfo | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);

  // Fetch documents when component mounts or user changes
  useEffect(() => {
    if (currentUser) {
      fetchDocuments();
    }
  }, [currentUser]);

  // Function to fetch all user documents from Realtime Database
  const fetchDocuments = async () => {
    if (!currentUser) return;

    setLoading(true);
    try {
      const docs = await getUserDocuments(currentUser.uid);
      setDocuments(docs);
    } catch (error) {
      console.error("Error fetching documents:", error);
      toast({
        title: "Error",
        description: "Failed to load documents",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Function to delete a document
  const handleDeleteDocument = async (docId: string) => {
    if (!currentUser) return;

    try {
      await deleteDocument(currentUser.uid, docId);
      toast({
        title: "Success",
        description: "Document deleted successfully",
      });
      fetchDocuments(); // Refresh the document list
    } catch (error) {
      console.error("Error deleting document:", error);
      toast({
        title: "Error",
        description: "Failed to delete document",
        variant: "destructive",
      });
    }
  };

  // Function to open the document in a new tab
  const handleViewDocument = (document: DocumentInfo) => {
    setSelectedDocument(document);
    setViewerOpen(true);
  };

  // Function to handle document upload to Firebase Storage and Realtime Database
  const handleUploadDocument = async () => {
    if (!currentUser || !uploadingDoc) return;

    setUploading(true);
    setUploadProgress(0);

    try {
      // Upload file to Firebase Storage with progress tracking
      const { url, path } = await uploadFile(
        uploadingDoc,
        currentUser.uid,
        documentType as any,
        (progress) => {
          setUploadProgress(progress);
        }
      );

      // Create a new document reference in Realtime Database
      const documentsRef = ref(database, `documents/${currentUser.uid}`);
      const newDocRef = push(documentsRef);

      // Save document metadata
      const docData = {
        userId: currentUser.uid,
        name: uploadingDoc.name,
        type: documentType,
        url,
        path,
        size: uploadingDoc.size,
        uploadedAt: Date.now(),
        status: "uploaded"
      };

      await set(newDocRef, docData);

      toast({
        title: "Success",
        description: "Document uploaded successfully",
      });
      setUploadingDoc(null);
      setUploadProgress(0);
      fetchDocuments(); // Refresh the document list
    } catch (error) {
      console.error("Error uploading document:", error);
      let errorMessage = "Failed to upload document";

      if (error instanceof Error) {
        errorMessage = error.message;
      }

      toast({
        title: "Upload Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setUploading(false);
    }
  };

  // Helper function to get human-readable document type label
  const getDocumentTypeLabel = (type: string) => {
    switch (type) {
      case "audit_report":
        return "Audit Report";
      case "annexure_a":
        return "Annexure A (CARO)";
      case "annexure_b":
        return "Annexure B (IFC)";
      case "balance_sheet":
        return "Balance Sheet";
      case "notes":
        return "Notes to Accounts";
      case "pl_notes":
        return "Profit & Loss Notes";

      default:
        return type;
    }
  };

  // Helper function to display status badge with appropriate icon and color
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "uploaded":
        return (
          <div className="flex items-center text-yellow-600 bg-yellow-100 px-2 py-1 rounded-full text-xs">
            <Clock className="h-3 w-3 mr-1" />
            <span>Uploaded</span>
          </div>
        );
      case "analyzed":
        return (
          <div className="flex items-center text-green-600 bg-green-100 px-2 py-1 rounded-full text-xs">
            <CheckCircle className="h-3 w-3 mr-1" />
            <span>Analyzed</span>
          </div>
        );
      default:
        return (
          <div className="flex items-center text-gray-600 bg-gray-100 px-2 py-1 rounded-full text-xs">
            <span>{status}</span>
          </div>
        );
    }
  };

  // Format timestamp to readable date
  const formatDate = (timestamp: number) => {
    return format(new Date(timestamp), "MMM d, yyyy");
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Document Management</h1>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Upload Document Card */}
        <Card>
          <CardHeader>
            <CardTitle>Upload New Document</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Document Type Selector */}
              <div>
                <label className="block text-sm font-medium mb-1">Document Type</label>
                <select
                  className="w-full p-2 border rounded-md"
                  value={documentType}
                  onChange={(e) => setDocumentType(e.target.value)}
                  disabled={uploading}
                >
                  <option value="audit_report">Audit Report</option>
                  <option value="annexure_a">Annexure A (CARO)</option>
                  <option value="annexure_b">Annexure B (IFC)</option>
                  <option value="balance_sheet">Balance Sheet</option>
                  <option value="notes">Notes to Accounts</option>
                  <option value="pl_notes">Profit & Loss Notes</option>
                  <option value="annual_report">Annual Report</option>
                  <option value="secretarial_compliance">Secretarial Compliance Report</option>

                </select>
              </div>

              {/* PDF File Uploader Component */}
              <PDFUploader
                label="Select PDF Document"
                description="Upload a PDF file (max 10MB)"
                onFileChange={(file) => setUploadingDoc(file)}
                value={uploadingDoc}
              />

              {/* Upload Progress Indicator */}
              {uploading && (
                <div className="space-y-2">
                  <Progress value={uploadProgress} className="h-2" />
                  <p className="text-xs text-center text-gray-500">
                    {uploadProgress === 100 ? "Processing document..." : `Uploading: ${uploadProgress}%`}
                  </p>
                </div>
              )}

              {/* Upload Button */}
              <Button
                onClick={handleUploadDocument}
                disabled={!uploadingDoc || uploading}
                className="w-full"
              >
                {uploading ? (
                  <span className="flex items-center">
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Uploading...
                  </span>
                ) : (
                  <span className="flex items-center">
                    <Upload className="h-4 w-4 mr-2" />
                    Upload Document
                  </span>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Document List Card */}
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>Your Documents</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              // Loading spinner
              <div className="flex justify-center py-8">
                <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
              </div>
            ) : documents.length === 0 ? (
              // Empty state
              <div className="text-center py-8 text-gray-500">
                <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>No documents found. Upload your first document to get started.</p>
              </div>
            ) : (
              // Document table
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left py-3 px-4">Document</th>
                      <th className="text-left py-3 px-4">Type</th>
                      <th className="text-left py-3 px-4">Uploaded</th>
                      <th className="text-left py-3 px-4">Status</th>
                      <th className="text-left py-3 px-4">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {documents.map((doc) => (
                      <tr key={doc.id} className="border-b hover:bg-gray-50">
                        <td className="py-3 px-4">
                          <div className="flex items-center">
                            <FileText className="h-5 w-5 text-primary mr-2" />
                            <span className="font-medium truncate max-w-[150px]">{doc.name}</span>
                          </div>
                        </td>
                        <td className="py-3 px-4">{getDocumentTypeLabel(doc.type)}</td>
                        <td className="py-3 px-4">{formatDate(doc.uploadedAt)}</td>
                        <td className="py-3 px-4">{getStatusBadge(doc.status)}</td>
                        <td className="py-3 px-4">
                          <div className="flex space-x-2">
                            {/* View button */}
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleViewDocument(doc)}
                              title="View Document"
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            {/* Delete button */}
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleDeleteDocument(doc.id)}
                              title="Delete Document"
                              className="text-red-500 hover:text-red-700 hover:bg-red-50"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Document Viewer Dialog */}
      {selectedDocument && (
        <DocumentViewer
          url={selectedDocument.url}
          title={selectedDocument.name}
          isOpen={viewerOpen}
          onClose={() => setViewerOpen(false)}
        />
      )}
    </div>
  );
};

export default Documents;