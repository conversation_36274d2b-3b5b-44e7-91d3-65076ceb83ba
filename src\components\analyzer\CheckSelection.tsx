import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

interface CheckSelectionProps {
  documents: DocumentFiles;
  parameters: AnalysisParameters;
  onSelectionChange: (selectedChecks: string[]) => void;
  onStartAnalysis: () => void;
  isProcessing: boolean;
}

const CheckSelection: React.FC<CheckSelectionProps> = ({
  documents,
  parameters,
  onSelectionChange,
  onStartAnalysis,
  isProcessing
}) => {
  const [availableChecks, setAvailableChecks] = useState<CheckOption[]>([]);
  const [categories, setCategories] = useState<CheckCategory[]>([]);

  useEffect(() => {
    const checks = getAvailableChecksForSelection(documents, parameters);
    setAvailableChecks(checks);
    setCategories(groupChecksByCategory(checks));
  }, [documents, parameters]);

  const handleCheckToggle = (checkId: string, checked: boolean) => {
    const updatedChecks = availableChecks.map(check =>
      check.id === checkId ? { ...check, isSelected: checked } : check
    );
    setAvailableChecks(updatedChecks);
    setCategories(groupChecksByCategory(updatedChecks));
    
    const selectedIds = updatedChecks.filter(c => c.isSelected).map(c => c.id);
    onSelectionChange(selectedIds);
  };

  const handleCategoryToggle = (categoryName: string, checked: boolean) => {
    const updatedChecks = availableChecks.map(check =>
      check.category === categoryName ? { ...check, isSelected: checked } : check
    );
    setAvailableChecks(updatedChecks);
    setCategories(groupChecksByCategory(updatedChecks));
    
    const selectedIds = updatedChecks.filter(c => c.isSelected).map(c => c.id);
    onSelectionChange(selectedIds);
  };

  const totalSelected = availableChecks.filter(c => c.isSelected).length;
  const estimatedTime = availableChecks
    .filter(c => c.isSelected)
    .reduce((acc, check) => acc + parseInt(check.estimatedTime), 0);

  return (
    <div className="space-y-6">
      <Card className="border-blue-200 bg-blue-50">
        <CardHeader>
          <CardTitle className="text-blue-800">📋 Select Compliance Checks to Run</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{totalSelected}</div>
              <div className="text-sm text-gray-600">Checks Selected</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{availableChecks.length}</div>
              <div className="text-sm text-gray-600">Total Available</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{Math.ceil(estimatedTime / 60)}m</div>
              <div className="text-sm text-gray-600">Est. Time</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {categories.map(category => (
        <Card key={category.name}>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Checkbox
                  checked={category.totalSelected === category.totalAvailable}
                  onCheckedChange={(checked) => handleCategoryToggle(category.name, !!checked)}
                  disabled={isProcessing}
                />
                <CardTitle className="text-lg">{category.name}</CardTitle>
                <Badge variant="outline">
                  {category.totalSelected}/{category.totalAvailable}
                </Badge>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {category.checks.map(check => (
                <div key={check.id} className="flex items-start space-x-3 p-3 border rounded-lg hover:bg-gray-50">
                  <Checkbox
                    checked={check.isSelected}
                    onCheckedChange={(checked) => handleCheckToggle(check.id, !!checked)}
                    disabled={isProcessing}
                  />
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium">{check.name}</h4>
                      <Badge variant="secondary">{check.estimatedTime}</Badge>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">{check.description}</p>
                    <div className="flex items-center space-x-2 mt-2">
                      {check.documentTypes.map(docType => (
                        <Badge key={docType} variant="outline" className="text-xs">
                          {docType.replace('_', ' ')}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      ))}

      <div className="flex justify-end space-x-4">
        <Button
          onClick={onStartAnalysis}
          disabled={isProcessing || totalSelected === 0}
          size="lg"
          className="w-full md:w-auto"
        >
          {isProcessing ? (
            <span className="flex items-center">
              <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2"></div>
              Running {totalSelected} Checks...
            </span>
          ) : (
            `🚀 Run ${totalSelected} Selected Checks (≈${Math.ceil(estimatedTime / 60)}m)`
          )}
        </Button>
      </div>
    </div>
  );
};

export default CheckSelection;