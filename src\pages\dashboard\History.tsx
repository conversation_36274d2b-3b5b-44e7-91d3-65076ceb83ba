// History.tsx - Updated to work with new analysis structure

import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { useQuery } from "@tanstack/react-query";
import { getAnalysisHistory } from "@/lib/storageService";
import { format } from "date-fns";
import { Button } from "@/components/ui/button";
import { FileText, AlertCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

// Import new types
import { CheckResult } from "@/lib/checkDefinitions";
import { getAnalysisSummary } from "@/lib/mainDocumentProcessor";

interface HistoryAnalysis {
  id: string;
  timestamp: number;
  company_name?: string;
  parameters?: {
    company_name?: string;
    audit_report_type?: string;
    company_listing_status?: string;
    is_nbfc?: string;
  };
  results?: Record<string, CheckResult>;
  documents?: Record<string, any>;
  summary?: {
    total: number;
    compliant: number;
    nonCompliant: number;
    compliancePercentage: number;
  };
}

const History = () => {
  const { currentUser } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [error, setError] = useState<string | null>(null);

  const {
    data: analysisHistory,
    isLoading,
    error: queryError,
    refetch
  } = useQuery({
    queryKey: ["analysisHistory", currentUser?.uid],
    queryFn: async () => {
      if (!currentUser) return [];

      try {
        console.log("Fetching analysis history for user:", currentUser.uid);
        const history = await getAnalysisHistory(currentUser.uid);
        console.log("Fetched history:", history);
        
        // Process each analysis to ensure proper structure
        return history.map((analysis: any) => {
          const processedAnalysis: HistoryAnalysis = {
            id: analysis.id,
            timestamp: analysis.timestamp || Date.now(),
            company_name: analysis.company_name || analysis.parameters?.company_name,
            parameters: analysis.parameters || {},
            results: analysis.results || {},
            documents: analysis.documents || {},
            summary: analysis.summary
          };
          
          // Generate summary if not present
          if (!processedAnalysis.summary && processedAnalysis.results) {
            processedAnalysis.summary = getAnalysisSummary(processedAnalysis.results);
          }
          
          // Fallback summary
          if (!processedAnalysis.summary) {
            processedAnalysis.summary = {
              total: 0,
              compliant: 0,
              nonCompliant: 0,
              compliancePercentage: 0
            };
          }
          
          return processedAnalysis;
        });
      } catch (err) {
        console.error("Error fetching analysis history:", err);
        setError(`Error fetching analysis history: ${err instanceof Error ? err.message : String(err)}`);
        return [];
      }
    },
    enabled: !!currentUser,
    retry: 2,
    staleTime: 30000, // 30 seconds
  });

  useEffect(() => {
    if (queryError) {
      console.error("Query error:", queryError);
      setError(`Error loading history: ${queryError instanceof Error ? queryError.message : String(queryError)}`);
    }
  }, [queryError]);

  const handleViewDetails = (id: string) => {
    console.log("Navigating to analysis details:", id);
    navigate(`/dashboard/analysis/${id}`);
  };

  const getComplianceColor = (percentage: number) => {
    if (percentage >= 80) return "text-green-600";
    if (percentage >= 60) return "text-yellow-600";
    return "text-red-600";
  };

  const getComplianceBadgeColor = (percentage: number) => {
    if (percentage >= 80) return "bg-green-100 text-green-800";
    if (percentage >= 60) return "bg-yellow-100 text-yellow-800";
    return "bg-red-100 text-red-800";
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Analysis History</h1>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={() => {
              refetch();
              toast({
                title: "Refreshing",
                description: "Refreshing analysis history...",
              });
            }}
          >
            Refresh
          </Button>
          <Button onClick={() => navigate("/dashboard/analyzer")}>
            New Analysis
          </Button>
        </div>
      </div>

      {error && (
        <Alert variant="destructive" className="mb-4">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="bg-white rounded-lg shadow-sm">
        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-primary border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]"></div>
              <p className="mt-4 text-gray-500">Loading analysis history...</p>
            </div>
          </div>
        ) : analysisHistory && analysisHistory.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Company
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Report Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Documents
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Compliance
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Action
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {analysisHistory.map((analysis) => {
                  // Count documents
                  const documentCount = analysis.documents 
                    ? Object.values(analysis.documents).filter(doc => doc !== null && doc !== undefined).length
                    : 0;

                  const summary = analysis.summary!;

                  return (
                    <tr key={analysis.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="font-medium text-gray-900">
                          {analysis.company_name || "N/A"}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-gray-500">
                        {analysis.timestamp ? format(new Date(analysis.timestamp), "MMM d, yyyy") : "N/A"}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-gray-900">
                          {analysis.parameters?.audit_report_type || "Standard"}
                        </div>
                        <div className="text-xs text-gray-500">
                          {analysis.parameters?.company_listing_status || "Unlisted"}
                          {analysis.parameters?.is_nbfc === "Yes" && " • NBFC"}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <FileText className="h-4 w-4 mr-1 text-gray-500" />
                          <span className="text-gray-900">
                            {documentCount} document{documentCount !== 1 ? 's' : ''}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex flex-col">
                          <span className={`font-medium ${getComplianceColor(summary.compliancePercentage)}`}>
                            {summary.compliancePercentage}%
                          </span>
                          <span className="text-xs text-gray-500">
                            {summary.compliant}/{summary.total} checks
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getComplianceBadgeColor(summary.compliancePercentage)}`}>
                          {summary.compliancePercentage >= 80 ? 'Excellent' : 
                           summary.compliancePercentage >= 60 ? 'Good' : 'Needs Review'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleViewDetails(analysis.id)}
                        >
                          View Details
                        </Button>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="text-center py-12 text-gray-500">
            <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p className="mb-2">No analysis history found</p>
            <p className="text-sm">
              {error ?
                "There was an error loading your analysis history. Please try refreshing." :
                "Start by analyzing an audit report"}
            </p>
            <div className="flex justify-center mt-4 space-x-4">
              <Button onClick={() => navigate("/dashboard/analyzer")}>
                Start Your First Analysis
              </Button>
              {error && (
                <Button variant="outline" onClick={() => refetch()}>
                  Try Again
                </Button>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default History;