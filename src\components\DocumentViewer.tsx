import React, { useState, useEffect } from 'react';
import { storage, getFromStorageCache, addToStorageCache } from '@/lib/firebase';
import { ref, getDownloadURL, getMetadata, getBlob } from 'firebase/storage';
import { Loader2, Alert<PERSON>riangle, RefreshCw, ExternalLink, Download } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

// Helper function to get container-friendly path
const getContainerFriendlyPath = (path: string): string => {
  // If path starts with 'users/', it's already a relative path
  if (path.startsWith('users/')) {
    return path;
  }

  // Extract the relative path from the absolute path
  // Assuming the format is something like '/path/to/storage/users/userId/...'
  const userIndex = path.indexOf('users/');
  if (userIndex !== -1) {
    return path.substring(userIndex);
  }

  // If we can't find 'users/', return the original path
  return path;
};

// Export a utility function to fetch document URL that can be used by other components
export const fetchDocumentUrlFromViewer = async (documentPath: string): Promise<string> => {
  try {
    console.log(`Utility: Fetching document URL for ${documentPath}`);

    // First, check if the URL is in our cache
    const cachedUrl = getFromStorageCache(documentPath);
    if (cachedUrl) {
      console.log(`Utility: Using cached URL for ${documentPath}`);
      return cachedUrl;
    }

    // Create a reference to the file in Firebase Storage
    const fileRef = ref(storage, documentPath);

    // Get the download URL
    const downloadUrl = await getDownloadURL(fileRef);
    console.log(`Utility: Got download URL: ${downloadUrl}`);

    if (!downloadUrl) {
      throw new Error("Failed to get download URL");
    }

    // Add to cache for future use
    try {
      const metadata = await getMetadata(fileRef);
      const fileSize = metadata.size || 0;
      addToStorageCache(documentPath, downloadUrl, fileSize);
    } catch (metadataError) {
      console.warn(`Utility: Could not get metadata for ${documentPath}:`, metadataError);
      addToStorageCache(documentPath, downloadUrl);
    }

    // Return the URL with a timestamp to prevent browser caching
    return `${downloadUrl}&t=${Date.now()}`;
  } catch (err) {
    console.error('Utility: Error fetching document URL:', err);
    throw err;
  }
};

interface DocumentViewerProps {
  documentPath: string;
  documentName: string;
}

const DocumentViewer: React.FC<DocumentViewerProps> = ({ documentPath, documentName }) => {
  const [url, setUrl] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [viewerType, setViewerType] = useState<'google' | 'direct'>('direct');

  const fetchDocumentUrl = async (retry = false) => {
    try {
      setLoading(true);
      setError(null);

      if (retry) {
        setRetryCount(prev => prev + 1);
      }

      console.log(`Fetching document URL for ${documentPath} (Attempt: ${retryCount + 1})`);

      // First, check if the URL is in our cache
      const cachedUrl = getFromStorageCache(documentPath);
      if (cachedUrl) {
        console.log(`Using cached URL for ${documentPath}`);
        setUrl(cachedUrl);
        setLoading(false);
        return;
      }

      // Create a reference to the file in Firebase Storage
      const fileRef = ref(storage, documentPath);

      // Simplified approach - only try once with no retries
      let downloadUrl: string | null = null;
      let fileSize = 0;

      // First try to get metadata to check file size
      try {
        const metadata = await getMetadata(fileRef);
        fileSize = metadata.size || 0;
        console.log(`File size for ${documentPath}: ${fileSize} bytes`);

        // Check if file is too large (over 10MB) and warn
        if (fileSize > 10 * 1024 * 1024) {
          console.warn(`Large file detected (${Math.round(fileSize / (1024 * 1024))}MB). This may cause loading issues.`);
        }
      } catch (metadataError) {
        console.warn(`Could not get metadata for ${documentPath}:`, metadataError);
      }

      // Single attempt to get the download URL
      try {
        downloadUrl = await getDownloadURL(fileRef);
        console.log(`Got download URL: ${downloadUrl}`);
      } catch (error) {
        console.error(`Error getting download URL: ${error}`);
        throw error; // Let the outer catch block handle this
      }

      if (!downloadUrl) {
        throw new Error("Failed to get download URL");
      }

      // Add to cache for future use
      addToStorageCache(documentPath, downloadUrl, fileSize);

      // Set the URL with a timestamp to prevent browser caching
      // Make sure we're adding the timestamp parameter correctly
      const separator = downloadUrl.includes('?') ? '&' : '?';
      const finalUrl = `${downloadUrl}${separator}t=${Date.now()}`;

      // Log the final URL for debugging
      console.log(`Final document URL with cache busting: ${finalUrl}`);

      // Check if the URL is accessible
      const isAccessible = await checkUrlAccessibility(finalUrl);

      if (!isAccessible) {
        console.warn(`URL is not accessible: ${finalUrl}`);
        // Try with Google Docs Viewer as a fallback
        setViewerType('google');
      }

      setUrl(finalUrl);
      setLoading(false);
    } catch (err) {
      console.error('Error fetching document URL:', err);

      // Create a more user-friendly error message with detailed information
      let errorMessage = "Could not load the document. Please try again later.";
      let errorDetails = "";

      if (err instanceof Error) {
        // Handle specific Firebase Storage errors
        if (err.message.includes("storage/retry-limit-exceeded")) {
          errorMessage = "Firebase Storage: Max retry time exceeded.";
          errorDetails = "The document may be too large or the connection is unstable.";
        } else if (err.message.includes("storage/object-not-found")) {
          errorMessage = "Document not found in Firebase Storage.";
          errorDetails = `Path: ${getContainerFriendlyPath(documentPath)}. The file may have been deleted or moved.`;
        } else if (err.message.includes("storage/unauthorized")) {
          errorMessage = "Access denied to document.";
          errorDetails = `You don't have permission to access this document at path: ${getContainerFriendlyPath(documentPath)}`;
        } else if (err.message.includes("storage/canceled")) {
          errorMessage = "Document download was canceled.";
          errorDetails = "The operation was canceled, possibly due to a timeout or user action.";
        } else if (err.message.includes("storage/server-file-wrong-size")) {
          errorMessage = "Document size mismatch.";
          errorDetails = "The file may be corrupted or incompletely uploaded.";
        } else if (err.message.includes("storage/quota-exceeded")) {
          errorMessage = "Storage quota exceeded.";
          errorDetails = "Please contact support to increase your storage quota.";
        } else if (err.message.includes("storage/invalid-argument")) {
          errorMessage = "Invalid document path.";
          errorDetails = `The path '${getContainerFriendlyPath(documentPath)}' is not valid or does not exist.`;
        } else if (err.message.includes("storage/unknown")) {
          errorMessage = "Unknown Firebase Storage error.";
          errorDetails = `Error details: ${err.message}`;
        } else {
          errorMessage = `Error: ${err.message}`;
          errorDetails = `Document path: ${getContainerFriendlyPath(documentPath)}`;
        }
      }

      // Store both the main error message and details
      setError(`${errorMessage}\n${errorDetails}`);
      setLoading(false);
    }
  };

  // Function to check if a URL is accessible
  const checkUrlAccessibility = async (urlToCheck: string): Promise<boolean> => {
    try {
      console.log(`Checking URL accessibility: ${urlToCheck}`);

      // Try to fetch the URL with HEAD request to check if it's accessible
      const response = await fetch(urlToCheck, {
        method: 'HEAD',
        mode: 'no-cors',
        cache: 'no-store',
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      });

      console.log(`URL accessibility check result: ${response.status}`);
      return true; // If we get here, the URL is accessible
    } catch (error) {
      console.error(`URL accessibility check failed: ${error}`);
      return false; // URL is not accessible
    }
  };

  useEffect(() => {
    if (documentPath) {
      fetchDocumentUrl();
    } else {
      setError('No document path provided');
      setLoading(false);
    }
  }, [documentPath]);

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary mb-2" />
        <span className="text-gray-600">Loading document...</span>
        <span className="text-xs text-gray-500 mt-1">Attempt {retryCount + 1}</span>
      </div>
    );
  }

  if (error) {
    // Split error message into main error and details
    const [errorMain, errorDetails] = error.split('\n');

    return (
      <div className="space-y-4">
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Error Loading Document</AlertTitle>
          <AlertDescription className="font-medium">{errorMain}</AlertDescription>
          {errorDetails && (
            <AlertDescription className="mt-2 text-sm opacity-80 whitespace-pre-wrap">
              {errorDetails}
            </AlertDescription>
          )}
        </Alert>

        <div className="bg-gray-50 p-4 rounded-md">
          <h3 className="text-sm font-medium mb-2">Document Information</h3>
          <div className="text-xs space-y-1 mb-3">
            <div><span className="font-medium">Path:</span> {getContainerFriendlyPath(documentPath)}</div>
            <div><span className="font-medium">Name:</span> {documentName}</div>
          </div>

          <h3 className="text-sm font-medium mb-2">Troubleshooting</h3>
          <ul className="text-sm space-y-1 list-disc pl-5">
            <li>Check if the document exists in Firebase Storage</li>
            <li>Verify that the document path is correct</li>
            <li>Ensure you have permission to access this document</li>
            <li>Try using a different viewer or browser</li>
          </ul>

          <div className="flex flex-wrap gap-2 mt-4">
            <Button
              size="sm"
              onClick={() => fetchDocumentUrl(true)}
              disabled={retryCount >= 3}
              className="flex items-center"
            >
              <RefreshCw className="h-4 w-4 mr-1" />
              Retry {retryCount > 0 ? `(${retryCount}/3)` : ''}
            </Button>

            <Button
              size="sm"
              variant="outline"
              onClick={() => setViewerType(viewerType === 'google' ? 'direct' : 'google')}
              className="flex items-center"
            >
              <ExternalLink className="h-4 w-4 mr-1" />
              Try {viewerType === 'google' ? 'Direct' : 'Google'} Viewer
            </Button>
          </div>
        </div>
      </div>
    );
  }

  if (!url) {
    return (
      <div className="bg-yellow-50 p-4 rounded-md text-yellow-800">
        <p className="font-medium">No document URL available</p>
        <p className="text-sm mt-1">The document may not exist or you may not have permission to access it.</p>
        <Button
          size="sm"
          variant="outline"
          className="mt-3"
          onClick={() => fetchDocumentUrl(true)}
        >
          Try Again
        </Button>
      </div>
    );
  }

  // Add cache busting parameter to prevent caching issues
  const cacheBustedUrl = `${url}${url.includes('?') ? '&' : '?'}_cb=${Date.now()}`;

  // Use Google Docs Viewer or direct PDF viewing based on user preference
  const googleDocsViewerUrl = `https://docs.google.com/viewer?url=${encodeURIComponent(cacheBustedUrl)}&embedded=true`;

  // Track iframe load state
  const [iframeLoaded, setIframeLoaded] = useState(false);
  const [iframeError, setIframeError] = useState(false);

  // Handle iframe load event
  const handleIframeLoad = () => {
    console.log(`${viewerType} viewer loaded successfully`);
    setIframeLoaded(true);
    setIframeError(false);
  };

  // Handle iframe error
  const handleIframeError = () => {
    console.error(`${viewerType} viewer failed to load`);
    setIframeError(true);

    // If using direct view and it fails, switch to Google viewer
    if (viewerType === 'direct') {
      console.log("Switching to Google Docs viewer as fallback");
      setViewerType('google');
    }
  };

  return (
    <div className="w-full h-full">
      <div className="flex justify-between items-center mb-3">
        <div className="text-sm text-gray-500">
          Viewing: {documentName}
        </div>
        <div className="flex space-x-2">
          <Button
            size="sm"
            variant="outline"
            onClick={() => {
              setIframeLoaded(false);
              setIframeError(false);
              setViewerType(viewerType === 'google' ? 'direct' : 'google');
            }}
          >
            Switch to {viewerType === 'google' ? 'Direct' : 'Google'} View
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={() => {
              // Refresh the document by fetching a new URL
              setIframeLoaded(false);
              setIframeError(false);
              fetchDocumentUrl(true);
            }}
          >
            <RefreshCw className="h-4 w-4 mr-1" />
            Refresh
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={() => window.open(url, '_blank')}
          >
            <ExternalLink className="h-4 w-4 mr-1" />
            Open in New Tab
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={async () => {
              try {
                // Create a reference to the file in Firebase Storage
                const fileRef = ref(storage, documentPath);
                // Get the blob
                const blob = await getBlob(fileRef);
                // Create a URL for the blob
                const blobUrl = URL.createObjectURL(blob);
                // Create a temporary anchor element
                const a = document.createElement('a');
                a.href = blobUrl;
                a.download = documentName || 'document.pdf';
                // Trigger the download
                document.body.appendChild(a);
                a.click();
                // Clean up
                document.body.removeChild(a);
                URL.revokeObjectURL(blobUrl);
              } catch (error) {
                console.error('Error downloading document:', error);
                // If direct download fails, just open in a new tab
                window.open(url, '_blank');
              }
            }}
          >
            <Download className="h-4 w-4 mr-1" />
            Download
          </Button>
        </div>
      </div>

      {/* Show loading indicator while iframe is loading */}
      {!iframeLoaded && !iframeError && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-50 z-10">
          <div className="flex flex-col items-center">
            <Loader2 className="h-8 w-8 animate-spin text-primary mb-2" />
            <span className="text-gray-600">Loading document with {viewerType} viewer...</span>
          </div>
        </div>
      )}

      {/* Show error message if both viewers fail */}
      {iframeError && viewerType === 'google' && (
        <div className="bg-red-50 p-4 rounded-md text-red-800 mb-4">
          <p className="font-medium">Failed to load document with both viewers</p>
          <p className="text-sm mt-1">Try opening the document in a new tab or downloading it.</p>
          <div className="mt-3">
            <Button
              size="sm"
              variant="outline"
              onClick={() => window.open(url, '_blank')}
            >
              <ExternalLink className="h-4 w-4 mr-1" />
              Open in New Tab
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={async () => {
                try {
                  // Create a reference to the file in Firebase Storage
                  const fileRef = ref(storage, documentPath);
                  // Get the blob
                  const blob = await getBlob(fileRef);
                  // Create a URL for the blob
                  const blobUrl = URL.createObjectURL(blob);
                  // Create a temporary anchor element
                  const a = document.createElement('a');
                  a.href = blobUrl;
                  a.download = documentName || 'document.pdf';
                  // Trigger the download
                  document.body.appendChild(a);
                  a.click();
                  // Clean up
                  document.body.removeChild(a);
                  URL.revokeObjectURL(blobUrl);
                } catch (error) {
                  console.error('Error downloading document:', error);
                  // If direct download fails, just open in a new tab
                  window.open(url, '_blank');
                }
              }}
            >
              <Download className="h-4 w-4 mr-1" />
              Download
            </Button>
          </div>
        </div>
      )}

      <iframe
        src={viewerType === 'google' ? googleDocsViewerUrl : cacheBustedUrl}
        className="w-full h-[600px] border border-gray-200 rounded-md"
        title={documentName}
        onLoad={handleIframeLoad}
        onError={handleIframeError}
      />
    </div>
  );
};

export default DocumentViewer;
