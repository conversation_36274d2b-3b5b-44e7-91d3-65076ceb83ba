// AnalysisDetail.tsx - Updated to work with new analysis structure

import { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { getAnalysisById } from "@/lib/storageService";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Download, FileText, Eye } from "lucide-react";
import AnalysisResults from "@/components/analyzer/AnalysisResults";
import { format } from "date-fns";
import { useToast } from "@/hooks/use-toast";
import SimpleDocumentViewer from "@/components/common/SimpleDocumentViewer";

// Import new types
import { CheckResult } from "@/lib/checkDefinitions";
import { AnalysisParameters, DocumentFiles, getAnalysisSummary } from "@/lib/mainDocumentProcessor";

interface AnalysisData {
  id: string;
  userId: string;
  timestamp: number;
  company_name: string;
  parameters: AnalysisParameters;
  results: Record<string, CheckResult>;
  documents: DocumentFiles;
  summary?: {
    total: number;
    compliant: number;
    nonCompliant: number;
    compliancePercentage: number;
  };
}

const AnalysisDetail = () => {
  const { id } = useParams<{ id: string }>();
  const { currentUser } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();

  const [analysisData, setAnalysisData] = useState<AnalysisData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [viewerOpen, setViewerOpen] = useState(false);
  const [selectedDocumentUrl, setSelectedDocumentUrl] = useState<string | null>(null);
  const [selectedDocumentName, setSelectedDocumentName] = useState<string>("");

  useEffect(() => {
    const fetchAnalysis = async () => {
      if (!id || !currentUser) {
        setError("Invalid analysis ID or not authenticated");
        setLoading(false);
        return;
      }

      try {
        console.log(`Fetching analysis with ID: ${id} for user: ${currentUser.uid}`);

        const data = await getAnalysisById(id, currentUser.uid);
        console.log("Fetched analysis data:", data);

        if (!data) {
          console.error("Analysis not found");
          setError("Analysis not found");
        } else if (data.userId !== currentUser.uid) {
          console.error("Permission error: User ID mismatch");
          setError("You don't have permission to view this analysis");
        } else {
          // Process the analysis data to ensure compatibility
          const processedData: AnalysisData = {
            id: data.id,
            userId: data.userId,
            timestamp: data.timestamp || Date.now(),
            company_name: data.company_name || data.parameters?.company_name || "Unknown Company",
            parameters: data.parameters || {} as AnalysisParameters,
            results: data.results || {},
            documents: data.documents || {},
            summary: data.summary
          };

          // Generate summary if not present
          if (!processedData.summary && processedData.results) {
            processedData.summary = getAnalysisSummary(processedData.results);
          }

          console.log("Processed analysis data:", processedData);
          setAnalysisData(processedData);
        }
      } catch (err) {
        console.error("Error fetching analysis:", err);
        setError(`Failed to fetch analysis details: ${err instanceof Error ? err.message : String(err)}`);
      } finally {
        setLoading(false);
      }
    };

    fetchAnalysis();
  }, [id, currentUser]);

  const handleBackClick = () => {
    navigate("/dashboard/history");
  };

  const handleExportPDF = () => {
    toast({
      title: "Feature Coming Soon",
      description: "PDF export functionality will be available in a future update.",
    });
  };

  const handleViewDocument = (file: File, documentType: string) => {
    console.log(`Opening document viewer for ${documentType}`);

    try {
      // Create object URL for the file
      const documentUrl = URL.createObjectURL(file);
      
      // Set the document URL and name, then open the viewer
      setSelectedDocumentUrl(documentUrl);
      setSelectedDocumentName(documentType);
      setViewerOpen(true);
      
    } catch (err) {
      console.error("Error in handleViewDocument:", err);
      toast({
        title: "Error",
        description: `Failed to open document viewer: ${err instanceof Error ? err.message : "Unknown error"}`,
        variant: "destructive"
      });
    }
  };

  const handleCloseViewer = () => {
    if (selectedDocumentUrl) {
      URL.revokeObjectURL(selectedDocumentUrl); // Cleanup object URL
    }
    setViewerOpen(false);
    setSelectedDocumentUrl(null);
    setSelectedDocumentName("");
  };

  // Document type to label mapping
  const documentLabels: Record<keyof DocumentFiles, string> = {
    audit_report: "Audit Report",
    annexure_a: "Annexure A (CARO)",
    annexure_b: "Annexure B (IFC)",
    balance_sheet: "Balance Sheet",
    notes: "Notes to Accounts",
    pl_notes: "Profit & Loss Notes",
    annual_report: "Annual Report",
    secretarial_compliance: "Secretarial Compliance"
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[500px]">
        <div className="text-center">
          <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-primary border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]"></div>
          <p className="mt-4 text-gray-500">Loading analysis details...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[500px]">
        <div className="text-center max-w-md">
          <FileText className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h2 className="text-2xl font-bold mb-2">Analysis Not Available</h2>
          <p className="text-gray-600 mb-6">{error}</p>
          <Button onClick={handleBackClick}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to History
          </Button>
        </div>
      </div>
    );
  }

  if (!analysisData) {
    return null;
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <Button variant="outline" size="sm" className="mr-4" onClick={handleBackClick}>
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back
          </Button>
          <h1 className="text-3xl font-bold">Analysis Details</h1>
        </div>
        <Button onClick={handleExportPDF}>
          <Download className="mr-2 h-4 w-4" />
          Export Report
        </Button>
      </div>

      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="mb-6">
          <div className="text-sm text-gray-500 mb-1">Analyzed on</div>
          <div className="text-lg font-semibold">
            {format(new Date(analysisData.timestamp), "PPPP 'at' p")}
          </div>
          
          {/* Summary Statistics */}
          {analysisData.summary && (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
              <div className="bg-blue-50 p-3 rounded-lg text-center">
                <div className="text-2xl font-bold text-blue-600">{analysisData.summary.total}</div>
                <div className="text-sm text-blue-600">Total Checks</div>
              </div>
              <div className="bg-green-50 p-3 rounded-lg text-center">
                <div className="text-2xl font-bold text-green-600">{analysisData.summary.compliant}</div>
                <div className="text-sm text-green-600">Compliant</div>
              </div>
              <div className="bg-red-50 p-3 rounded-lg text-center">
                <div className="text-2xl font-bold text-red-600">{analysisData.summary.nonCompliant}</div>
                <div className="text-sm text-red-600">Non-Compliant</div>
              </div>
              <div className="bg-purple-50 p-3 rounded-lg text-center">
                <div className="text-2xl font-bold text-purple-600">{analysisData.summary.compliancePercentage}%</div>
                <div className="text-sm text-purple-600">Compliance Rate</div>
              </div>
            </div>
          )}
        </div>

        {/* Document links */}
        {analysisData.documents && Object.keys(analysisData.documents).some(key => analysisData.documents[key as keyof DocumentFiles]) && (
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <h3 className="text-sm font-medium mb-3">Analyzed Documents</h3>
            <div className="space-y-2">
              {Object.entries(analysisData.documents).map(([docType, file]) => {
                if (!file) return null;
                
                const label = documentLabels[docType as keyof DocumentFiles] || docType;
                
                return (
                  <div key={docType} className="flex items-center justify-between">
                    <div className="flex items-center">
                      <FileText className="h-4 w-4 mr-2 text-primary" />
                      <span>{label}</span>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleViewDocument(file, label)}
                    >
                      <Eye className="h-4 w-4 mr-1" />
                      View
                    </Button>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Analysis Results */}
        <AnalysisResults
          results={analysisData.results}
          parameters={analysisData.parameters}
          documents={analysisData.documents}
        />
      </div>

      {/* Simple Document Viewer Dialog */}
      <SimpleDocumentViewer
        url={selectedDocumentUrl || ''}
        title={selectedDocumentName}
        isOpen={viewerOpen && !!selectedDocumentUrl}
        onClose={handleCloseViewer}
      />
    </div>
  );
};

export default AnalysisDetail;