import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Award } from "lucide-react";
import { <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";

const CaseStudies = () => {
  return (
    <div className="min-h-screen bg-slate-900 text-white">
      {/* Header */}
      <header className="bg-slate-900/95 backdrop-blur-lg border-b border-white/10">
        <div className="w-full px-6 md:px-12 lg:px-20 py-6 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="h-10 w-10 rounded-xl bg-gradient-to-br from-blue-600 to-purple-600 flex items-center justify-center text-white font-bold">
              P
            </div>
            <span className="text-xl font-bold text-white">PKF Audit AI</span>
          </div>

          <Link to="/">
            <Button variant="ghost" className="text-white">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Home
            </Button>
          </Link>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-24 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-indigo-900 via-blue-900 to-slate-900"></div>
        <div className="w-full px-6 md:px-12 lg:px-20 relative z-10">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Real Results with <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">PKF Audit AI</span>
            </h1>
            <p className="text-xl md:text-2xl text-white/80 mb-8">
              Discover how organizations across industries have transformed their audit processes with our AI-powered solutions.
            </p>
            <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-full px-8 py-6 text-xl font-semibold shadow-2xl">
              Get Started Today
            </Button>
          </div>
        </div>
      </section>

      {/* Case Studies Grid */}
      <section className="py-16 w-full px-6 md:px-12 lg:px-20">
        <h2 className="text-3xl font-bold mb-12 text-center">Featured Success Stories</h2>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
          {/* Case Study 1 */}
          <div className="bg-white/5 backdrop-blur-xl border border-white/20 rounded-3xl overflow-hidden">
            <div className="h-64 bg-gradient-to-br from-blue-900/40 to-indigo-900/40 relative">
              <div className="absolute inset-0 flex items-center justify-center">
                <Building className="h-24 w-24 text-blue-300/30" />
              </div>
              <div className="absolute bottom-4 left-4">
                <div className="h-16 w-16 rounded-xl bg-white/10 backdrop-blur-sm border border-white/20 flex items-center justify-center">
                  <div className="h-10 w-10 rounded-md bg-gradient-to-br from-blue-600 to-purple-600 flex items-center justify-center text-white font-bold">
                    F
                  </div>
                </div>
              </div>
            </div>
            
            <div className="p-8">
              <div className="flex items-center gap-2 mb-4">
                <div className="px-3 py-1 bg-blue-500/20 rounded-full text-blue-400 text-sm">Finance</div>
                <div className="px-3 py-1 bg-purple-500/20 rounded-full text-purple-400 text-sm">Fortune 500</div>
              </div>
              
              <h3 className="text-2xl font-bold mb-3">Financial Services Leader Achieves 90% Faster Audit Times</h3>
              
              <p className="text-white/70 mb-6">
                A leading global financial services firm implemented PKF Audit AI to streamline their audit processes and achieved dramatic time savings while improving accuracy.
              </p>
              
              <div className="grid grid-cols-2 gap-4 mb-6">
                <div className="bg-white/5 rounded-xl p-4">
                  <div className="flex items-center gap-2 mb-1">
                    <Clock className="h-4 w-4 text-blue-400" />
                    <span className="text-white/70 text-sm">Time Saved</span>
                  </div>
                  <div className="text-xl font-bold text-white">90%</div>
                </div>
                
                <div className="bg-white/5 rounded-xl p-4">
                  <div className="flex items-center gap-2 mb-1">
                    <BarChart className="h-4 w-4 text-green-400" />
                    <span className="text-white/70 text-sm">Accuracy</span>
                  </div>
                  <div className="text-xl font-bold text-white">99.8%</div>
                </div>
              </div>
              
              <Button className="w-full bg-gradient-to-r from-blue-600 to-purple-600">
                Read Full Case Study
              </Button>
            </div>
          </div>
          
          {/* Case Study 2 */}
          <div className="bg-white/5 backdrop-blur-xl border border-white/20 rounded-3xl overflow-hidden">
            <div className="h-64 bg-gradient-to-br from-green-900/40 to-teal-900/40 relative">
              <div className="absolute inset-0 flex items-center justify-center">
                <Building className="h-24 w-24 text-green-300/30" />
              </div>
              <div className="absolute bottom-4 left-4">
                <div className="h-16 w-16 rounded-xl bg-white/10 backdrop-blur-sm border border-white/20 flex items-center justify-center">
                  <div className="h-10 w-10 rounded-md bg-gradient-to-br from-green-600 to-teal-600 flex items-center justify-center text-white font-bold">
                    H
                  </div>
                </div>
              </div>
            </div>
            
            <div className="p-8">
              <div className="flex items-center gap-2 mb-4">
                <div className="px-3 py-1 bg-green-500/20 rounded-full text-green-400 text-sm">Healthcare</div>
                <div className="px-3 py-1 bg-teal-500/20 rounded-full text-teal-400 text-sm">Enterprise</div>
              </div>
              
              <h3 className="text-2xl font-bold mb-3">Healthcare Provider Reduces Audit Costs by 60%</h3>
              
              <p className="text-white/70 mb-6">
                A national healthcare network implemented our AI solution to handle complex compliance requirements and significantly reduced their audit costs while improving regulatory compliance.
              </p>
              
              <div className="grid grid-cols-2 gap-4 mb-6">
                <div className="bg-white/5 rounded-xl p-4">
                  <div className="flex items-center gap-2 mb-1">
                    <PieChart className="h-4 w-4 text-green-400" />
                    <span className="text-white/70 text-sm">Cost Reduction</span>
                  </div>
                  <div className="text-xl font-bold text-white">60%</div>
                </div>
                
                <div className="bg-white/5 rounded-xl p-4">
                  <div className="flex items-center gap-2 mb-1">
                    <Users className="h-4 w-4 text-blue-400" />
                    <span className="text-white/70 text-sm">Staff Efficiency</span>
                  </div>
                  <div className="text-xl font-bold text-white">+75%</div>
                </div>
              </div>
              
              <Button className="w-full bg-gradient-to-r from-green-600 to-teal-600">
                Read Full Case Study
              </Button>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Mini Case Study 1 */}
          <div className="bg-white/5 backdrop-blur-xl border border-white/20 rounded-2xl p-6">
            <div className="flex items-center gap-2 mb-4">
              <div className="px-3 py-1 bg-purple-500/20 rounded-full text-purple-400 text-sm">Retail</div>
            </div>
            
            <h3 className="text-xl font-bold mb-3">Global Retail Chain Improves Audit Speed by 85%</h3>
            
            <p className="text-white/70 mb-4">
              With operations in 35 countries, this retailer needed a solution to standardize and accelerate their financial auditing.
            </p>
            
            <div className="mb-4 flex items-center gap-3">
              <Award className="h-5 w-5 text-yellow-400" />
              <span className="text-white/70">85% faster audits</span>
            </div>
            
            <Button variant="link" className="text-blue-400 p-0">
              Read Case Study →
            </Button>
          </div>
          
          {/* Mini Case Study 2 */}
          <div className="bg-white/5 backdrop-blur-xl border border-white/20 rounded-2xl p-6">
            <div className="flex items-center gap-2 mb-4">
              <div className="px-3 py-1 bg-blue-500/20 rounded-full text-blue-400 text-sm">Manufacturing</div>
            </div>
            
            <h3 className="text-xl font-bold mb-3">Manufacturer Detects $1.2M in Accounting Errors</h3>
            
            <p className="text-white/70 mb-4">
              Our AI analysis uncovered significant accounting inconsistencies that would have been missed in traditional audits.
            </p>
            
            <div className="mb-4 flex items-center gap-3">
              <Award className="h-5 w-5 text-yellow-400" />
              <span className="text-white/70">$1.2M saved</span>
            </div>
            
            <Button variant="link" className="text-blue-400 p-0">
              Read Case Study →
            </Button>
          </div>
          
          {/* Mini Case Study 3 */}
          <div className="bg-white/5 backdrop-blur-xl border border-white/20 rounded-2xl p-6">
            <div className="flex items-center gap-2 mb-4">
              <div className="px-3 py-1 bg-green-500/20 rounded-full text-green-400 text-sm">Tech Startup</div>
            </div>
            
            <h3 className="text-xl font-bold mb-3">Tech Startup Scales Financial Operations</h3>
            
            <p className="text-white/70 mb-4">
              A rapidly growing tech company used our solution to scale their financial operations without expanding their finance team.
            </p>
            
            <div className="mb-4 flex items-center gap-3">
              <Award className="h-5 w-5 text-yellow-400" />
              <span className="text-white/70">40% cost reduction</span>
            </div>
            
            <Button variant="link" className="text-blue-400 p-0">
              Read Case Study →
            </Button>
          </div>
        </div>
      </section>
      
      {/* Industries Section */}
      <section className="py-16 bg-white/5 backdrop-blur-sm">
        <div className="w-full px-6 md:px-12 lg:px-20">
          <h2 className="text-3xl font-bold mb-12 text-center">Industries We Serve</h2>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {[
              { icon: Building, name: "Financial Services", count: 150 },
              { icon: Users, name: "Healthcare", count: 87 },
              { icon: Building, name: "Manufacturing", count: 65 },
              { icon: Building, name: "Retail", count: 48 },
              { icon: Building, name: "Technology", count: 92 },
              { icon: Building, name: "Education", count: 31 },
              { icon: Building, name: "Non-profit", count: 29 },
              { icon: Building, name: "Government", count: 26 }
            ].map((industry, index) => (
              <div key={index} className="text-center p-4">
                <div className="h-16 w-16 mx-auto bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-full flex items-center justify-center mb-4">
                  <industry.icon className="h-8 w-8 text-white/70" />
                </div>
                <h3 className="font-bold mb-1">{industry.name}</h3>
                <p className="text-white/60 text-sm">{industry.count}+ clients</p>
              </div>
            ))}
          </div>
        </div>
      </section>
      
      {/* CTA Section */}
      <section className="py-16 w-full px-6 md:px-12 lg:px-20">
        <div className="max-w-4xl mx-auto bg-gradient-to-br from-blue-900/50 to-purple-900/50 backdrop-blur-xl border border-white/10 rounded-3xl p-12 text-center">
          <h2 className="text-3xl font-bold mb-6">Ready to Transform Your Financial Audit Process?</h2>
          <p className="text-xl text-white/70 mb-8">
            Join these successful organizations and see how PKF Audit AI can revolutionize your financial operations.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Button className="bg-gradient-to-r from-blue-600 to-purple-600 text-lg py-6 px-8">
              Request Demo
            </Button>
            <Button variant="outline" className="border-white/50 text-white text-lg py-6 px-8">
              Contact Sales
            </Button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-slate-900 border-t border-white/10 py-8">
        <div className="w-full px-6 md:px-12 lg:px-20 text-center">
          <p className="text-white/60">© {new Date().getFullYear()} PKF Audit AI. All rights reserved.</p>
        </div>
      </footer>
    </div>
  );
};

export default CaseStudies;
