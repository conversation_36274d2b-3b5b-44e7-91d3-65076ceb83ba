declare module 'firebase/database' {
  import { Database } from '@firebase/database-types';
  
  export function ref(database: Database, path?: string): any;
  export function push(ref: any): any;
  export function set(ref: any, value: any): Promise<void>;
  export function get(ref: any): Promise<any>;
  export function remove(ref: any): Promise<void>;
  export function update(ref: any, value: any): Promise<void>;
  export function query(ref: any, ...queryConstraints: any[]): any;
  export function orderByChild(path: string): any;
  export function equalTo(value: any): any;
}



//See in the Signature i want to get information not an hardcoded get the information from the Based on the firm name and partener name and UID number from the cloud for that see the settings Tab to get the firm name and registeration number and keep the Drop down of the partener name in the Analysis Tab please when i clcik the Partner name keep UID number auto take for the checking. please make required adjustments 