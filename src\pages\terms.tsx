import React from "react";
import { <PERSON><PERSON>ef<PERSON> } from "lucide-react";
import { <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";

const Terms = () => {
  return (
    <div className="min-h-screen bg-slate-900 text-white">
      {/* Header */}
      <header className="bg-slate-900/95 backdrop-blur-lg border-b border-white/10">
        <div className="w-full px-6 md:px-12 lg:px-20 py-6 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="h-10 w-10 rounded-xl bg-gradient-to-br from-blue-600 to-purple-600 flex items-center justify-center text-white font-bold">
              P
            </div>
            <span className="text-xl font-bold text-white">PKF Audit AI</span>
          </div>

          <Link to="/">
            <Button variant="ghost" className="text-white">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Home
            </Button>
          </Link>
        </div>
      </header>

      {/* Page Content */}
      <div className="w-full px-6 md:px-12 lg:px-20 py-16">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl md:text-5xl font-bold mb-8">Terms of Service</h1>
          <p className="text-white/60 mb-8">Last updated: May 15, 2025</p>

          <div className="prose prose-invert max-w-none">
            <p className="text-white/80 mb-6">
              Please read these Terms of Service ("Terms", "Terms of Service") carefully before using the PKF Audit AI platform operated by PKF Audit AI ("us", "we", "our").
            </p>

            <p className="text-white/80 mb-6">
              Your access to and use of the Service is conditioned on your acceptance of and compliance with these Terms. These Terms apply to all visitors, users, and others who access or use the Service.
            </p>

            <p className="text-white/80 mb-6">
              By accessing or using the Service, you agree to be bound by these Terms. If you disagree with any part of the terms, then you may not access the Service.
            </p>

            <h2 className="text-2xl font-bold mt-10 mb-6">1. Use of Service</h2>
            <p className="text-white/80 mb-4">
              PKF Audit AI provides an AI-powered financial auditing platform ("Service") designed to assist with financial document analysis, compliance checking, and audit workflows.
            </p>
            <p className="text-white/80 mb-4">
              You are responsible for maintaining the confidentiality of your account and password and for restricting access to your account. You agree to accept responsibility for all activities that occur under your account.
            </p>

            <h2 className="text-2xl font-bold mt-10 mb-6">2. Subscription and Payments</h2>
            <p className="text-white/80 mb-4">
              Some features of the Service require a subscription. You agree to pay the fees associated with your chosen subscription plan. Subscription fees are billed in advance on a monthly or annual basis depending on your selected billing cycle.
            </p>
            <p className="text-white/80 mb-4">
              You may cancel your subscription at any time, but no refunds will be provided for partial subscription periods unless required by law.
            </p>

            <h2 className="text-2xl font-bold mt-10 mb-6">3. Data and Privacy</h2>
            <p className="text-white/80 mb-4">
              You retain all rights to your data uploaded to the Service. By uploading data, you grant PKF Audit AI a license to use, process, and store this data for the purpose of providing the Service to you.
            </p>
            <p className="text-white/80 mb-4">
              We take data privacy seriously and handle your information in accordance with our Privacy Policy. By using our Service, you consent to our collection and use of data as outlined in the Privacy Policy.
            </p>
            <p className="text-white/80 mb-6">
              PKF Audit AI will not share your financial data with third parties except as necessary to provide the Service or as required by law.
            </p>

            <h2 className="text-2xl font-bold mt-10 mb-6">4. Intellectual Property</h2>
            <p className="text-white/80 mb-4">
              The Service and its original content, features, and functionality are owned by PKF Audit AI and are protected by international copyright, trademark, patent, trade secret, and other intellectual property or proprietary rights laws.
            </p>
            <p className="text-white/80 mb-4">
              You may not duplicate, copy, or reuse any portion of the HTML, CSS, JavaScript, or visual design elements of the Service without express written permission from PKF Audit AI.
            </p>

            <h2 className="text-2xl font-bold mt-10 mb-6">5. Limitations of Liability</h2>
            <p className="text-white/80 mb-4">
              While we strive for accuracy, PKF Audit AI makes no warranties regarding the completeness, reliability, or accuracy of the analysis provided by the Service. The Service is designed to assist with auditing processes but is not a replacement for professional financial expertise.
            </p>
            <p className="text-white/80 mb-4">
              In no event shall PKF Audit AI be liable for any indirect, incidental, special, consequential or punitive damages, including without limitation, loss of profits, data, use, goodwill, or other intangible losses, resulting from your access to or use of or inability to access or use the Service.
            </p>

            <h2 className="text-2xl font-bold mt-10 mb-6">6. Termination</h2>
            <p className="text-white/80 mb-4">
              We may terminate or suspend access to our Service immediately, without prior notice or liability, for any reason whatsoever, including without limitation if you breach the Terms.
            </p>
            <p className="text-white/80 mb-4">
              All provisions of the Terms which by their nature should survive termination shall survive termination, including, without limitation, ownership provisions, warranty disclaimers, indemnity, and limitations of liability.
            </p>

            <h2 className="text-2xl font-bold mt-10 mb-6">7. Changes</h2>
            <p className="text-white/80 mb-4">
              We reserve the right, at our sole discretion, to modify or replace these Terms at any time. If a revision is material, we will try to provide at least 30 days' notice prior to any new terms taking effect.
            </p>
            <p className="text-white/80 mb-4">
              By continuing to access or use our Service after those revisions become effective, you agree to be bound by the revised terms. If you do not agree to the new terms, please stop using the Service.
            </p>

            <h2 className="text-2xl font-bold mt-10 mb-6">8. Contact Us</h2>
            <p className="text-white/80 mb-4">
              If you have any questions about these Terms, please contact us at:
            </p>
            <p className="text-white/80 mb-8">
              <strong>Email:</strong> <EMAIL><br />
              <strong>Address:</strong> PKF Audit AI, Chennai, Tamil Nadu, India
            </p>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-slate-900 border-t border-white/10 py-8">
        <div className="w-full px-6 md:px-12 lg:px-20 text-center">
          <p className="text-white/60">© {new Date().getFullYear()} PKF Audit AI. All rights reserved.</p>
        </div>
      </footer>
    </div>
  );
};

export default Terms;
