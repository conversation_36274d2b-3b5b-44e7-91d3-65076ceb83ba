// AnalysisResults.tsx - Updated to work with new processing logic

import React, { useState } from "react";
import { format } from "date-fns";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { FileText } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import StreamlitStyleChecklist from "./StreamlitStyleChecklist";
import SimpleDocumentViewer from "@/components/common/SimpleDocumentViewer";

// Import new types
import { CheckResult } from "@/lib/checkDefinitions";
import { AnalysisParameters, DocumentFiles } from "@/lib/mainDocumentProcessor";

interface AnalysisResultsProps {
  results: Record<string, CheckResult>;
  parameters: AnalysisParameters;
  documents?: DocumentFiles;
}

const AnalysisResults: React.FC<AnalysisResultsProps> = ({ 
  results, 
  parameters, 
  documents = {} 
}) => {
  // State for document viewer
  const [viewerOpen, setViewerOpen] = useState(false);
  const [selectedDocumentUrl, setSelectedDocumentUrl] = useState<string>("");
  const [selectedDocumentName, setSelectedDocumentName] = useState<string>("");
  const [error, setError] = useState<string | null>(null);

  // Check for common errors
  React.useEffect(() => {
    try {
      // Check if parameters and audit_date exist and are valid
      if (!parameters) {
        setError("Analysis parameters are missing");
        return;
      }

      // Ensure audit_date is properly handled
      if (parameters.audit_date) {
        // If it's already a Date object
        if (parameters.audit_date instanceof Date) {
          parameters.audit_date.getTime(); // This will throw if invalid
        }
        // If it's a number (timestamp)
        else if (typeof parameters.audit_date === 'number') {
          new Date(parameters.audit_date).getTime();
        }
        // If it's a string (ISO date)
        else if (typeof parameters.audit_date === 'string') {
          new Date(parameters.audit_date).getTime();
        }
        // If it's something else, convert to string first
        else {
          new Date(String(parameters.audit_date)).getTime();
        }
      }
      setError(null);
    } catch (err) {
      console.error("Date parsing error:", err);
      setError("Cannot read properties of undefined (reading 'getTime')");
    }
  }, [parameters]);

  // Handle document viewing
  const handleViewDocument = (file: File, documentType: string) => {
    try {
      console.log(`Opening document viewer for ${documentType}`);
      
      // Create object URL for the file
      const documentUrl = URL.createObjectURL(file);
      
      // Set document info and open viewer
      setSelectedDocumentUrl(documentUrl);
      setSelectedDocumentName(documentType);
      setViewerOpen(true);
      
    } catch (err) {
      console.error("Error in handleViewDocument:", err);
      setError(`Failed to open document viewer: ${err instanceof Error ? err.message : "Unknown error"}`);
    }
  };

  // Close document viewer and cleanup
  const handleCloseViewer = () => {
    if (selectedDocumentUrl) {
      URL.revokeObjectURL(selectedDocumentUrl); // Cleanup object URL
    }
    setViewerOpen(false);
    setSelectedDocumentUrl("");
    setSelectedDocumentName("");
  };

  // Document type to label mapping
  const documentLabels: Record<keyof DocumentFiles, string> = {
    audit_report: "Audit Report",
    annexure_a: "Annexure A (CARO)",
    annexure_b: "Annexure B (IFC)", 
    balance_sheet: "Balance Sheet",
    notes: "Notes to Accounts",
    pl_notes: "Profit & Loss Notes",
    annual_report: "Annual Report",
    secretarial_compliance: "Secretarial Compliance"
  };

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="text-sm text-gray-500">Company Name</div>
            <div className="font-semibold text-lg">{parameters.company_name}</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="text-sm text-gray-500">Audit Date</div>
            <div className="font-semibold text-lg">
              {(() => {
                try {
                  if (!parameters.audit_date) return "Not specified";

                  // Handle different types of audit_date
                  if (parameters.audit_date instanceof Date) {
                    return format(parameters.audit_date, "PPP");
                  } else if (typeof parameters.audit_date === 'number') {
                    return format(new Date(parameters.audit_date), "PPP");
                  } else if (typeof parameters.audit_date === 'string') {
                    return format(new Date(parameters.audit_date), "PPP");
                  } else {
                    return format(new Date(String(parameters.audit_date)), "PPP");
                  }
                } catch (err) {
                  console.error("Error formatting date:", err);
                  return "Invalid date";
                }
              })()}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="text-sm text-gray-500">Report Type</div>
            <div className="font-semibold text-lg">
              {parameters.audit_report_type}
              {parameters.profit_or_loss && ` (${parameters.profit_or_loss})`}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Document Viewer Section */}
      {documents && Object.keys(documents).some(key => documents[key as keyof DocumentFiles]) && (
        <Card className="mt-4">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg font-semibold">Analyzed Documents</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-4">
              {Object.entries(documents).map(([docType, file]) => {
                if (!file) return null;
                
                const label = documentLabels[docType as keyof DocumentFiles] || docType;
                
                return (
                  <Button
                    key={docType}
                    variant="outline"
                    className="flex items-center gap-2"
                    onClick={() => handleViewDocument(file, label)}
                  >
                    <FileText className="h-4 w-4" />
                    View {label}
                  </Button>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Document Viewer Dialog */}
      <SimpleDocumentViewer
        url={selectedDocumentUrl}
        title={selectedDocumentName}
        isOpen={viewerOpen}
        onClose={handleCloseViewer}
      />

      {/* Error Display */}
      {error ? (
        <div className="space-y-6 mt-6 p-6 border rounded-lg bg-white shadow-sm">
          <div className="bg-red-500 text-white p-4 rounded-md">
            <h2 className="text-xl font-bold mb-2">Analysis Failed</h2>
            <p>An error occurred during analysis: {error}</p>
          </div>
        </div>
      ) : (
        /* Main Results Display */
        <StreamlitStyleChecklist
          results={results}
          parameters={parameters}
          documents={documents}
        />
      )}
    </div>
  );
};

export default AnalysisResults;