// This script can be loaded in your browser console to clear any auth-related storage
console.log("Clearing localStorage...");
const keysToRemove = [];
for (let i = 0; i < localStorage.length; i++) {
  const key = localStorage.key(i);
  if (key.includes('firebase') || key.includes('auth') || key.includes('token')) {
    keysToRemove.push(key);
  }
}

keysToRemove.forEach(key => {
  console.log(`Removing key: ${key}`);
  localStorage.removeItem(key);
});

console.log("Clearing sessionStorage...");
sessionStorage.clear();

console.log("Storage cleared. Please refresh the page.");
