
import React, { useEffect } from "react";
import { Link, useLocation } from "react-router-dom";
import { BarChart3, FileText, Home, Settings, History, ChevronLeft, ChevronRight, Files, FileSearch, FileCheck } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { useState } from "react";

const DashboardSidebar: React.FC = () => {
  const location = useLocation();
  const { currentUser } = useAuth();
  const [collapsed, setCollapsed] = useState(false);

  // Update the sidebar placeholder width when collapsed state changes
  useEffect(() => {
    const placeholder = document.getElementById('sidebar-placeholder');
    if (placeholder) {
      placeholder.style.width = collapsed ? '4rem' : '16rem';
    }
  }, [collapsed]);

  const isActive = (path: string) => {
    return location.pathname === path;
  };
  const menuItems = [
    {
      path: "/dashboard",
      name: "Dashboard",
      icon: <Home className="h-5 w-5" />
    },
    {
      path: "/dashboard/documents",
      name: "Documents",
      icon: <Files className="h-5 w-5" />
    },
    {
      path: "/dashboard/analyzer",
      name: "Analyzer",
      icon: <BarChart3 className="h-5 w-5" />
    },
    {
      path: "/dashboard/history",
      name: "History",
      icon: <History className="h-5 w-5" />
    },
    {
      path: "/dashboard/checklists",
      name: "Checklists",
      icon: <FileText className="h-5 w-5" />
    },
    {
      path: "/dashboard/document-diagnostics",
      name: "Document Diagnostics",
      icon: <FileSearch className="h-5 w-5" />
    },
    {
      path: "/dashboard/settings",
      name: "Settings",
      icon: <Settings className="h-5 w-5" />
    },
  ];

  if (!currentUser) {
    return null;
  }

  return (
    <aside
      className={cn(
        "bg-white border-r border-gray-200 transition-all duration-300 h-[calc(100vh-64px)] fixed top-16 left-0 z-30",
        collapsed ? "w-16" : "w-64"
      )}
    >
      <div className="p-3 flex flex-col h-full overflow-y-auto scrollbar-hide">
        <div className="mb-6 flex justify-end">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setCollapsed(!collapsed)}
            className="h-8 w-8 p-0"
          >
            {collapsed ? <ChevronRight size={16} /> : <ChevronLeft size={16} />}
          </Button>
        </div>

        <nav>
          <ul className="space-y-1">
            {menuItems.map((item) => (
              <li key={item.path}>
                <Link
                  to={item.path}
                  className={cn(
                    "flex items-center gap-3 px-3 py-2 rounded-md text-gray-700 hover:bg-gray-100 transition-colors",
                    isActive(item.path) && "bg-primary text-white hover:bg-primary/90"
                  )}
                >
                  {item.icon}
                  {!collapsed && <span className="font-medium">{item.name}</span>}
                </Link>
              </li>
            ))}
          </ul>
        </nav>

        <div className="mt-auto">
          {!collapsed && (
            <div className="border-t border-gray-200 pt-4 mt-4">
              <div className="text-xs text-gray-500 mb-2">
                Logged in as
              </div>
              <div className="text-sm font-medium truncate">
                {currentUser.displayName || currentUser.email}
              </div>
            </div>
          )}
        </div>
      </div>
    </aside>
  );
};

export default DashboardSidebar;
