// modelRouter.ts - Simplified routing (only configure what you want to change)

import { CheckResult } from './checkDefinitions';
import { processSingleDocumentCheck } from './geminiProcessor';
import { processSingleDocumentCheckWithOpenAI } from './openaiProcessor';

// Configuration for which checks should use OpenAI (everything else uses Gemini by default)
export interface ModelConfig {
  checkId: string;
  openaiModel?: string; // Which OpenAI model to use (defaults to gpt-4o)
  reason?: string; // Optional reason for using OpenAI
}

// Simple configuration - ONLY list checks that should use OpenAI
// Everything else automatically uses Gemini
export const OPENAI_CHECKS: ModelConfig[] = [
  // Removed clause_20 - it will now use Gemini which handles PDFs natively
  // Add other checks here that work well with OpenAI's text-based approach
  
  // Example for future text-based checks:
  // {
  //   checkId: 'company_name_consistency',
  //   openaiModel: 'gpt-4o',
  //   reason: 'Better text pattern matching for company name variations'
  // }
];

/**
 * Check if a specific check should use OpenAI
 */
export function shouldUseOpenAI(checkId: string): boolean {
  return OPENAI_CHECKS.some(config => config.checkId === checkId);
}

/**
 * Get the OpenAI model to use for a specific check
 */
export function getOpenAIModelForCheck(checkId: string): string {
  const config = OPENAI_CHECKS.find(config => config.checkId === checkId);
  return config?.openaiModel || 'gpt-4o';
}

/**
 * Process a single document check using the configured AI model
 * This is the main function that routes to the appropriate processor
 */
export async function processDocumentCheckWithRouting(
  pdfFile: File,
  checkType: string,
  promptKey: string,
  parameters: Record<string, any>
): Promise<CheckResult> {
  
  if (shouldUseOpenAI(checkType)) {
    const model = getOpenAIModelForCheck(checkType);
    const config = OPENAI_CHECKS.find(c => c.checkId === checkType);
    
    console.log(`🤖 Routing ${checkType} to OpenAI (${model})`);
    if (config?.reason) {
      console.log(`📝 Reason: ${config.reason}`);
    }
    
    return await processSingleDocumentCheckWithOpenAI(
      pdfFile,
      checkType,
      promptKey,
      parameters,
      model
    );
  } else {
    console.log(`🧠 Routing ${checkType} to Gemini (default)`);
    
    return await processSingleDocumentCheck(
      pdfFile,
      checkType,
      promptKey,
      parameters
    );
  }
}

/**
 * Add a new check to use OpenAI (runtime configuration)
 */
export function useOpenAIForCheck(
  checkId: string, 
  openaiModel: string = 'gpt-4o', 
  reason?: string
): void {
  // Remove existing if already configured
  const existingIndex = OPENAI_CHECKS.findIndex(config => config.checkId === checkId);
  if (existingIndex !== -1) {
    OPENAI_CHECKS.splice(existingIndex, 1);
  }
  
  // Add new configuration
  OPENAI_CHECKS.push({ checkId, openaiModel, reason });
  console.log(`✅ ${checkId} will now use OpenAI (${openaiModel})`);
}

/**
 * Remove OpenAI routing for a check (will fall back to Gemini)
 */
export function useGeminiForCheck(checkId: string): void {
  const existingIndex = OPENAI_CHECKS.findIndex(config => config.checkId === checkId);
  if (existingIndex !== -1) {
    OPENAI_CHECKS.splice(existingIndex, 1);
    console.log(`✅ ${checkId} will now use Gemini (default)`);
  } else {
    console.log(`ℹ️ ${checkId} already uses Gemini (default)`);
  }
}

/**
 * Log current configuration
 */
export function logRoutingConfiguration(): void {
  console.log('\n🔀 AI MODEL ROUTING 🔀');
  console.log('======================');
  
  if (OPENAI_CHECKS.length === 0) {
    console.log('🧠 All checks use Gemini (default)');
  } else {
    console.log(`🤖 OpenAI checks: ${OPENAI_CHECKS.length}`);
    OPENAI_CHECKS.forEach(config => {
      console.log(`  ✅ ${config.checkId} → ${config.openaiModel || 'gpt-4o'}`);
      if (config.reason) {
        console.log(`     Reason: ${config.reason}`);
      }
    });
    console.log(`🧠 All other checks use Gemini (default)`);
  }
  console.log('======================\n');
}