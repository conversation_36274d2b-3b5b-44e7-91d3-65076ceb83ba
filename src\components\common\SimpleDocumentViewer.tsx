import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>alogContent, <PERSON><PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { X, ExternalLink, RefreshCw, Download, AlertTriangle } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface SimpleDocumentViewerProps {
  url: string;
  title: string;
  isOpen: boolean;
  onClose: () => void;
}

const SimpleDocumentViewer: React.FC<SimpleDocumentViewerProps> = ({
  url,
  title,
  isOpen,
  onClose
}) => {
  const [viewerType, setViewerType] = useState<'google' | 'direct'>('direct');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [loadAttempts, setLoadAttempts] = useState(0);

  // Validate URL
  const isValidUrl = url && (url.startsWith('http://') || url.startsWith('https://'));

  // Add cache busting parameter - recalculate on each render to ensure freshness
  const cacheBustedUrl = React.useMemo(() => {
    if (!isValidUrl) return '';
    return `${url}${url.includes('?') ? '&' : '?'}_cb=${Date.now()}`;
  }, [url, isOpen, isValidUrl]); // Recalculate when URL or isOpen changes

  // Google Docs viewer URL
  const googleDocsViewerUrl = React.useMemo(() => {
    if (!isValidUrl) return '';
    return `https://docs.google.com/viewer?url=${encodeURIComponent(cacheBustedUrl)}&embedded=true`;
  }, [cacheBustedUrl, isValidUrl]);

  // Reset states when the document changes or dialog opens
  useEffect(() => {
    if (isOpen) {
      console.log("SimpleDocumentViewer rendered with isOpen:", isOpen);
      console.log("URL:", url);

      setIsLoading(true);
      setError(null);
      setLoadAttempts(0);

      if (!isValidUrl) {
        console.error("SimpleDocumentViewer: Invalid URL format:", url);
        setIsLoading(false);
        setError("Invalid document URL. The document may have been deleted or the URL is malformed.");
      }
    }
  }, [isOpen, url, isValidUrl]);

  // Handle iframe load
  const handleIframeLoad = () => {
    console.log("Document loaded successfully");
    setIsLoading(false);
    setError(null);
  };

  // Handle iframe error
  const handleIframeError = () => {
    console.error(`Error loading document (attempt ${loadAttempts + 1})`);
    setLoadAttempts(prev => prev + 1);

    if (loadAttempts >= 2) {
      setIsLoading(false);
      setError(`Failed to load the document. Please try a different viewer or download the document.`);
    } else {
      // Try again with a different viewer
      console.log("Switching viewer type");
      setViewerType(viewerType === 'google' ? 'direct' : 'google');
    }
  };

  // Function to refresh the document with a new cache-busting parameter
  const refreshDocument = () => {
    console.log("Refreshing document");
    setIsLoading(true);
    setError(null);
    setLoadAttempts(0);
    // Force a re-render by updating state
    setViewerType(viewerType); // This will trigger the useMemo hooks to recalculate
  };

  // Force the dialog to be visible if isOpen is true
  useEffect(() => {
    if (isOpen) {
      console.log("Forcing dialog to be visible");
      // This is a hack to force the dialog to be visible
      const dialogElement = document.querySelector('[role="dialog"]');
      if (dialogElement) {
        dialogElement.setAttribute('style', 'display: block !important');
      }
    }
  }, [isOpen]);

  if (!isOpen) {
    return null; // Don't render anything if not open
  }

  return (
    <Dialog open={true} onOpenChange={(open) => {
      console.log("Dialog open state changed to:", open);
      if (!open) onClose();
    }}>
      <DialogContent className="max-w-5xl w-[95vw] h-[85vh]" hideCloseButton={true}>
        <DialogHeader className="flex flex-row items-center justify-between">
          <DialogTitle>{title}</DialogTitle>
          <div className="flex items-center space-x-2">
            {isValidUrl && (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setViewerType(viewerType === 'google' ? 'direct' : 'google');
                    setIsLoading(true);
                    setError(null);
                  }}
                  title={viewerType === 'google' ? 'Switch to direct PDF view' : 'Switch to Google Docs viewer'}
                >
                  Switch to {viewerType === 'google' ? 'Direct' : 'Google'} View
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={refreshDocument}
                  title="Refresh document"
                >
                  <RefreshCw className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => window.open(url, '_blank')}
                  title="Open in new tab"
                >
                  <ExternalLink className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    try {
                      const link = document.createElement('a');
                      link.href = url;
                      link.download = title || 'document.pdf';
                      document.body.appendChild(link);
                      link.click();
                      document.body.removeChild(link);
                    } catch (err) {
                      console.error("Error downloading document:", err);
                      setError("Failed to download the document. The URL may be invalid.");
                    }
                  }}
                  title="Download document"
                >
                  <Download className="h-4 w-4" />
                </Button>
              </>
            )}
            <Button variant="ghost" size="icon" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </DialogHeader>

        {error && (
          <div className="flex items-center justify-center p-4">
            <Alert variant="destructive" className="mb-4">
              <AlertTriangle className="h-4 w-4 mr-2" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          </div>
        )}

        {isLoading && !error && (
          <div className="flex items-center justify-center h-16">
            <div className="animate-spin h-6 w-6 border-4 border-primary border-t-transparent rounded-full"></div>
            <span className="ml-2">Loading document...</span>
          </div>
        )}

        {isValidUrl && !error && (
          <div className="mt-2">
            <iframe
              src={viewerType === 'google' ? googleDocsViewerUrl : cacheBustedUrl}
              className="w-full h-[calc(85vh-120px)] border border-gray-200 rounded"
              onLoad={handleIframeLoad}
              onError={handleIframeError}
            />
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default SimpleDocumentViewer;
