
import React, { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { updateProfile } from "firebase/auth";
import { useToast } from "@/hooks/use-toast";
import {
  Company,
  Partner,
  FirmSettings,
  getCompanies,
  getPartners,
  getFirmSettings,
  addCompany,
  addPartner,
  saveFirmSettings,
  updateCompany,
  updatePartner,
  deleteCompany,
  deletePartner
} from "@/lib/storageService";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@/components/ui/dialog";
import { Plus, Pencil, Trash2, Loader2 } from "lucide-react";
import { format } from "date-fns";

const Settings = () => {
  const { currentUser, resetPassword } = useAuth();
  const { toast } = useToast();

  const [name, setName] = useState(currentUser?.displayName || "");
  const [email, setEmail] = useState(currentUser?.email || "");
  const [loading, setLoading] = useState(false);

  // Entity management states
  const [companies, setCompanies] = useState<Company[]>([]);
  const [partners, setPartners] = useState<Partner[]>([]);
  const [firmSettings, setFirmSettings] = useState<FirmSettings | null>(null);
  const [loadingEntities, setLoadingEntities] = useState({
    companies: false,
    partners: false,
    firmSettings: false
  });

  // Dialog states
  const [companyDialogOpen, setCompanyDialogOpen] = useState(false);
  const [partnerDialogOpen, setPartnerDialogOpen] = useState(false);

  // Form states
  const [newCompanyName, setNewCompanyName] = useState("");
  const [newPartnerName, setNewPartnerName] = useState("");
  const [newPartnerRegNumber, setNewPartnerRegNumber] = useState("");
  const [firmName, setFirmName] = useState("");
  const [firmRegNumber, setFirmRegNumber] = useState("");

  // Edit states
  const [editingPartner, setEditingPartner] = useState<Partner | null>(null);
  const [editingCompany, setEditingCompany] = useState<Company | null>(null);
  const [savingFirmSettings, setSavingFirmSettings] = useState(false);

  const handleUpdateProfile = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!currentUser) {
      return;
    }

    setLoading(true);
    try {
      await updateProfile(currentUser, {
        displayName: name
      });

      toast({
        title: "Profile Updated",
        description: "Your profile has been successfully updated."
      });
    } catch (error) {
      toast({
        title: "Update Failed",
        description: "Failed to update profile. Please try again.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleResetPassword = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!currentUser?.email) {
      return;
    }

    try {
      await resetPassword(currentUser.email);
    } catch (error) {
      toast({
        title: "Password Reset Failed",
        description: "Failed to send password reset email. Please try again.",
        variant: "destructive"
      });
    }
  };

  // Load entities on component mount
  useEffect(() => {
    if (currentUser) {
      fetchCompanies();
      fetchPartners();
      fetchFirmSettings();
    }
  }, [currentUser]);

  // Fetch firm settings
  const fetchFirmSettings = async () => {
    if (!currentUser) return;

    setLoadingEntities(prev => ({ ...prev, firmSettings: true }));
    try {
      const settings = await getFirmSettings(currentUser.uid);
      if (settings) {
        setFirmSettings(settings);
        setFirmName(settings.name);
        setFirmRegNumber(settings.registrationNumber);
      }
    } catch (error) {
      console.error("Error fetching firm settings:", error);
      toast({
        title: "Error",
        description: "Failed to load firm settings",
        variant: "destructive"
      });
    } finally {
      setLoadingEntities(prev => ({ ...prev, firmSettings: false }));
    }
  };

  // Fetch partners
  const fetchPartners = async () => {
    if (!currentUser) return;

    setLoadingEntities(prev => ({ ...prev, partners: true }));
    try {
      const fetchedPartners = await getPartners(currentUser.uid);
      setPartners(fetchedPartners);
    } catch (error) {
      console.error("Error fetching partners:", error);
      toast({
        title: "Error",
        description: "Failed to load partners",
        variant: "destructive"
      });
    } finally {
      setLoadingEntities(prev => ({ ...prev, partners: false }));
    }
  };

  // Fetch companies
  const fetchCompanies = async () => {
    if (!currentUser) return;

    setLoadingEntities(prev => ({ ...prev, companies: true }));
    try {
      const fetchedCompanies = await getCompanies(currentUser.uid);
      setCompanies(fetchedCompanies);
    } catch (error) {
      console.error("Error fetching companies:", error);
      toast({
        title: "Error",
        description: "Failed to load companies",
        variant: "destructive"
      });
    } finally {
      setLoadingEntities(prev => ({ ...prev, companies: false }));
    }
  };

  // Handle firm settings
  const handleSaveFirmSettings = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentUser || !firmName.trim()) return;

    setSavingFirmSettings(true);
    try {
      await saveFirmSettings(currentUser.uid, firmName, firmRegNumber);
      toast({
        title: "Success",
        description: "Firm settings saved successfully"
      });
      fetchFirmSettings();
    } catch (error) {
      console.error("Error saving firm settings:", error);
      toast({
        title: "Error",
        description: "Failed to save firm settings",
        variant: "destructive"
      });
    } finally {
      setSavingFirmSettings(false);
    }
  };

  // Handle partner actions
  const handleAddPartner = async () => {
    if (!currentUser || !newPartnerName.trim()) return;

    try {
      if (editingPartner) {
        // Update existing partner
        await updatePartner(currentUser.uid, editingPartner.id, newPartnerName, newPartnerRegNumber);
        toast({
          title: "Success",
          description: "Partner updated successfully"
        });
      } else {
        // Add new partner
        await addPartner(currentUser.uid, newPartnerName, newPartnerRegNumber);
        toast({
          title: "Success",
          description: "Partner added successfully"
        });
      }

      setNewPartnerName("");
      setNewPartnerRegNumber("");
      setEditingPartner(null);
      setPartnerDialogOpen(false);
      fetchPartners();
    } catch (error) {
      console.error("Error with partner:", error);
      toast({
        title: "Error",
        description: editingPartner ? "Failed to update partner" : "Failed to add partner",
        variant: "destructive"
      });
    }
  };

  const handleDeletePartner = async (partnerId: string) => {
    if (!currentUser) return;

    if (!confirm("Are you sure you want to delete this partner?")) {
      return;
    }

    try {
      await deletePartner(currentUser.uid, partnerId);
      toast({
        title: "Success",
        description: "Partner deleted successfully"
      });
      fetchPartners();
    } catch (error) {
      console.error("Error deleting partner:", error);
      toast({
        title: "Error",
        description: "Failed to delete partner",
        variant: "destructive"
      });
    }
  };

  // Handle company actions
  const handleAddCompany = async () => {
    if (!currentUser || !newCompanyName.trim()) return;

    try {
      if (editingCompany) {
        // Update existing company
        await updateCompany(currentUser.uid, editingCompany.id, newCompanyName);
        toast({
          title: "Success",
          description: "Company updated successfully"
        });
      } else {
        // Add new company
        await addCompany(currentUser.uid, newCompanyName);
        toast({
          title: "Success",
          description: "Company added successfully"
        });
      }

      setNewCompanyName("");
      setEditingCompany(null);
      setCompanyDialogOpen(false);
      fetchCompanies();
    } catch (error) {
      console.error("Error with company:", error);
      toast({
        title: "Error",
        description: editingCompany ? "Failed to update company" : "Failed to add company",
        variant: "destructive"
      });
    }
  };

  const handleDeleteCompany = async (companyId: string) => {
    if (!currentUser) return;

    if (!confirm("Are you sure you want to delete this company?")) {
      return;
    }

    try {
      await deleteCompany(currentUser.uid, companyId);
      toast({
        title: "Success",
        description: "Company deleted successfully"
      });
      fetchCompanies();
    } catch (error) {
      console.error("Error deleting company:", error);
      toast({
        title: "Error",
        description: "Failed to delete company",
        variant: "destructive"
      });
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Settings</h1>
      </div>

      <Tabs defaultValue="account" className="w-full">
        <TabsList className="mb-6">
          <TabsTrigger value="account">Account</TabsTrigger>
          <TabsTrigger value="companies">Companies</TabsTrigger>
          <TabsTrigger value="partners">Partners</TabsTrigger>
          <TabsTrigger value="firm">Firm Name</TabsTrigger>
          
        </TabsList>

        <TabsContent value="account">
          <div className="grid gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Profile Information</CardTitle>
                <CardDescription>
                  Update your personal information
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleUpdateProfile} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Name</Label>
                    <Input
                      id="name"
                      value={name}
                      onChange={(e) => setName(e.target.value)}
                      placeholder="Your name"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      value={email}
                      disabled
                      placeholder="Your email"
                    />
                    <p className="text-sm text-gray-500">
                      Your email cannot be changed.
                    </p>
                  </div>
                  <Button type="submit" disabled={loading || !name.trim()}>
                    {loading ? "Updating..." : "Update Profile"}
                  </Button>
                </form>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Password</CardTitle>
                <CardDescription>
                  Change your password
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleResetPassword} className="space-y-4">
                  <p className="text-sm text-gray-600 mb-4">
                    We'll send a password reset link to your email address.
                  </p>
                  <Button type="submit">
                    Send Password Reset Email
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>
        </TabsContent>



        {/* Partners Tab */}
        <TabsContent value="partners">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Partners</CardTitle>
                <CardDescription>
                  Manage your partner list
                </CardDescription>
              </div>
              <Dialog open={partnerDialogOpen} onOpenChange={setPartnerDialogOpen}>
                <DialogTrigger asChild>
                  <Button
                    onClick={() => {
                      setEditingPartner(null);
                      setNewPartnerName("");
                    }}
                  >
                    <Plus className="h-4 w-4 mr-2" /> Add Partner
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>{editingPartner ? "Edit Partner" : "Add New Partner"}</DialogTitle>
                    <DialogDescription>
                      {editingPartner ? "Update partner details" : "Enter partner details below"}
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4 py-4">
                    <div className="space-y-2">
                      <Label htmlFor="partnerName">Partner Name</Label>
                      <Input
                        id="partnerName"
                        value={newPartnerName}
                        onChange={(e) => setNewPartnerName(e.target.value)}
                        placeholder="Enter partner name"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="partnerRegNumber">Registration Number</Label>
                      <Input
                        id="partnerRegNumber"
                        value={newPartnerRegNumber}
                        onChange={(e) => setNewPartnerRegNumber(e.target.value)}
                        placeholder="Enter registration number"
                      />
                    </div>
                  </div>
                  <DialogFooter>
                    <Button variant="outline" onClick={() => setPartnerDialogOpen(false)}>Cancel</Button>
                    <Button onClick={handleAddPartner} disabled={!newPartnerName.trim()}>
                      {editingPartner ? "Update Partner" : "Add Partner"}
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </CardHeader>
            <CardContent>
              {loadingEntities.partners ? (
                <div className="flex justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                </div>
              ) : partners.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <p>No partners added yet.</p>
                  <p>Click "Add Partner" to create your first partner.</p>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Partner Name</TableHead>
                      <TableHead>Registration Number</TableHead>
                      <TableHead>Added On</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {partners.map((partner) => (
                      <TableRow key={partner.id}>
                        <TableCell className="font-medium">{partner.name}</TableCell>
                        <TableCell>{partner.registrationNumber}</TableCell>
                        <TableCell>{format(partner.createdAt, "PPP")}</TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end space-x-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                setEditingPartner(partner);
                                setNewPartnerName(partner.name);
                                setNewPartnerRegNumber(partner.registrationNumber);
                                setPartnerDialogOpen(true);
                              }}
                            >
                              <Pencil className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDeletePartner(partner.id)}
                            >
                              <Trash2 className="h-4 w-4 text-red-500" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Companies Tab (renamed from Clients) */}
        <TabsContent value="companies">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Companies</CardTitle>
                <CardDescription>
                  Manage your company list (formerly clients)
                </CardDescription>
              </div>
              <Dialog open={companyDialogOpen} onOpenChange={setCompanyDialogOpen}>
                <DialogTrigger asChild>
                  <Button
                    onClick={() => {
                      setEditingCompany(null);
                      setNewCompanyName("");
                    }}
                  >
                    <Plus className="h-4 w-4 mr-2" /> Add Company
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>{editingCompany ? "Edit Company" : "Add New Company"}</DialogTitle>
                    <DialogDescription>
                      {editingCompany ? "Update company details" : "Enter company details below"}
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4 py-4">
                    <div className="space-y-2">
                      <Label htmlFor="companyName">Company Name</Label>
                      <Input
                        id="companyName"
                        value={newCompanyName}
                        onChange={(e) => setNewCompanyName(e.target.value)}
                        placeholder="Enter company name"
                      />
                    </div>
                  </div>
                  <DialogFooter>
                    <Button variant="outline" onClick={() => setCompanyDialogOpen(false)}>Cancel</Button>
                    <Button onClick={handleAddCompany} disabled={!newCompanyName.trim()}>
                      {editingCompany ? "Update Company" : "Add Company"}
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </CardHeader>
            <CardContent>
              {loadingEntities.companies ? (
                <div className="flex justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                </div>
              ) : companies.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <p>No companies added yet.</p>
                  <p>Click "Add Company" to create your first company.</p>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Company Name</TableHead>
                      <TableHead>Added On</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {companies.map((company) => (
                      <TableRow key={company.id}>
                        <TableCell className="font-medium">{company.name}</TableCell>
                        <TableCell>{format(company.createdAt, "PPP")}</TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end space-x-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                setEditingCompany(company);
                                setNewCompanyName(company.name);
                                setCompanyDialogOpen(true);
                              }}
                            >
                              <Pencil className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDeleteCompany(company.id)}
                            >
                              <Trash2 className="h-4 w-4 text-red-500" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="notifications">
          <Card>
            <CardHeader>
              <CardTitle>Notifications</CardTitle>
              <CardDescription>
                Configure your notification preferences
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-gray-500">
                Notification settings will be available in a future update.
              </p>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Firm Name Tab (formerly Company) */}
        <TabsContent value="firm">
          <Card>
            <CardHeader>
              <CardTitle>Firm Settings</CardTitle>
              <CardDescription>
                Configure your firm details
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSaveFirmSettings} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="firmName">Firm Name</Label>
                  <Input
                    id="firmName"
                    value={firmName}
                    onChange={(e) => setFirmName(e.target.value)}
                    placeholder="Enter your firm name"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="firmRegNumber">Registration Number</Label>
                  <Input
                    id="firmRegNumber"
                    value={firmRegNumber}
                    onChange={(e) => setFirmRegNumber(e.target.value)}
                    placeholder="Enter firm registration number"
                  />
                </div>
                <Button type="submit" disabled={savingFirmSettings || !firmName.trim()}>
                  {savingFirmSettings ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : "Save Firm Settings"}
                </Button>
              </form>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="preferences">
          <Card>
            <CardHeader>
              <CardTitle>Preferences</CardTitle>
              <CardDescription>
                Customize your application experience
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-gray-500">
                Preference settings will be available in a future update.
              </p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Settings;
