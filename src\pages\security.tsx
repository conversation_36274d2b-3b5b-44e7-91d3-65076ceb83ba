import React from "react";
import { ArrowLeft, Shield, Lock, Server, Database, CheckSquare } from "lucide-react";
import { Link } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";

const Security = () => {
  return (
    <div className="min-h-screen bg-slate-900 text-white">
      {/* Header */}
      <header className="bg-slate-900/95 backdrop-blur-lg border-b border-white/10">
        <div className="w-full px-6 md:px-12 lg:px-20 py-6 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="h-10 w-10 rounded-xl bg-gradient-to-br from-blue-600 to-purple-600 flex items-center justify-center text-white font-bold">
              P
            </div>
            <span className="text-xl font-bold text-white">PKF Audit AI</span>
          </div>

          <Link to="/">
            <Button variant="ghost" className="text-white">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Home
            </Button>
          </Link>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-24 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-indigo-900 via-blue-900 to-slate-900"></div>
        <div className="w-full px-6 md:px-12 lg:px-20 relative z-10">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Our <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">Security</span> Commitment
            </h1>
            <p className="text-xl md:text-2xl text-white/80 mb-8">
              We prioritize protecting your data with industry-leading security measures and protocols.
            </p>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-16 w-full px-6 md:px-12 lg:px-20">
        <div className="max-w-4xl mx-auto space-y-12">
          <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-3xl p-8">
            <div className="flex items-start gap-4 mb-6">
              <div className="h-12 w-12 rounded-xl bg-blue-600/20 flex items-center justify-center">
                <Shield className="h-6 w-6 text-blue-400" />
              </div>
              <div>
                <h2 className="text-2xl font-bold mb-2">Data Protection</h2>
                <p className="text-white/70 leading-relaxed">
                  At PKF Audit AI, we implement multiple layers of security to protect your sensitive financial data. All data is encrypted both in transit and at rest using industry-standard AES-256 encryption. Our system architecture includes role-based access controls, ensuring that only authorized personnel can access your information.
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-3xl p-8">
            <div className="flex items-start gap-4 mb-6">
              <div className="h-12 w-12 rounded-xl bg-purple-600/20 flex items-center justify-center">
                <Lock className="h-6 w-6 text-purple-400" />
              </div>
              <div>
                <h2 className="text-2xl font-bold mb-2">Authentication & Access Controls</h2>
                <p className="text-white/70 leading-relaxed">
                  We employ multi-factor authentication (MFA) for all user accounts, requiring additional verification beyond just passwords. Our access controls follow the principle of least privilege, meaning users are granted only the permissions necessary for their specific role. Regular access reviews ensure that all permissions remain appropriate.
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-3xl p-8">
            <div className="flex items-start gap-4 mb-6">
              <div className="h-12 w-12 rounded-xl bg-green-600/20 flex items-center justify-center">
                <Server className="h-6 w-6 text-green-400" />
              </div>
              <div>
                <h2 className="text-2xl font-bold mb-2">Infrastructure Security</h2>
                <p className="text-white/70 leading-relaxed">
                  Our application infrastructure is hosted in ISO-certified data centers with 24/7 physical security, redundant power systems, and environmental controls. We implement network security measures including firewalls, intrusion detection and prevention systems, and regular vulnerability scanning. All systems are patched promptly to protect against known vulnerabilities.
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-3xl p-8">
            <div className="flex items-start gap-4 mb-6">
              <div className="h-12 w-12 rounded-xl bg-orange-600/20 flex items-center justify-center">
                <Database className="h-6 w-6 text-orange-400" />
              </div>
              <div>
                <h2 className="text-2xl font-bold mb-2">Data Backup & Disaster Recovery</h2>
                <p className="text-white/70 leading-relaxed">
                  We maintain comprehensive backup systems with automated daily backups and periodic testing of restore procedures. Our disaster recovery plan ensures business continuity with minimal downtime in the event of a system failure. Backups are encrypted and stored securely in geographically diverse locations.
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-3xl p-8">
            <div className="flex items-start gap-4 mb-6">
              <div className="h-12 w-12 rounded-xl bg-red-600/20 flex items-center justify-center">
                <CheckSquare className="h-6 w-6 text-red-400" />
              </div>
              <div>
                <h2 className="text-2xl font-bold mb-2">Compliance & Certifications</h2>
                <p className="text-white/70 leading-relaxed">
                  PKF Audit AI maintains compliance with multiple international security standards including SOC 2 Type II and ISO 27001. We undergo regular third-party security audits and penetration testing to validate our security controls. Our security practices are designed to meet or exceed industry standards for financial data protection.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16 w-full px-6 md:px-12 lg:px-20 border-t border-white/10">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl font-bold mb-6">Have Security Questions?</h2>
          <p className="text-xl text-white/70 mb-8">
            Our security team is available to address any concerns about our security practices or policies.
          </p>
          <Link to="/contact">
            <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-full px-8 py-6 text-xl font-semibold">
              Contact Our Security Team
            </Button>
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t border-white/10 py-8">
        <div className="w-full px-6 md:px-12 lg:px-20 text-center">
          <p className="text-white/60">
            © {new Date().getFullYear()} PKF Audit AI. All rights reserved.
          </p>
        </div>
      </footer>
    </div>
  );
};

export default Security;
