// geminiProcessor.ts - Clean Gemini AI integration

import { CheckResult } from './checkDefinitions';
import { renderPrompt } from './promptDefinitions';

// Gemini API configuration
const API_KEY = "AIzaSyA2-3gPSsIMLH3ZVqQ6vmLl5STSYRrmAT0";
const API_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent";

// Cache for PDF base64 data to avoid repeated conversions
const pdfCache = new Map<string, string>();

/**
 * Convert ArrayBuffer to Base64 string (browser-compatible)
 */
function arrayBufferToBase64(buffer: ArrayBuffer): string {
  const bytes = new Uint8Array(buffer);
  let binary = '';
  const len = bytes.byteLength;
  for (let i = 0; i < len; i++) {
    binary += String.fromCharCode(bytes[i]);
  }
  return window.btoa(binary);
}

/**
 * Get PDF as Base64 string with caching
 */
async function getPdfAsBase64(pdfFile: File): Promise<string> {
  const cacheKey = `${pdfFile.name}_${pdfFile.size}`;
  
  if (pdfCache.has(cacheKey)) {
    console.log(`Using cached PDF data for ${pdfFile.name}`);
    return pdfCache.get(cacheKey)!;
  }
  
  const pdfArrayBuffer = await pdfFile.arrayBuffer();
  const pdfBase64 = arrayBufferToBase64(pdfArrayBuffer);
  
  pdfCache.set(cacheKey, pdfBase64);
  console.log(`Cached PDF data for ${pdfFile.name}`);
  
  return pdfBase64;
}

/**
 * Call Gemini API with retry logic
 */
async function callGeminiAPI(prompt: string, pdfBase64: string, maxRetries: number = 3): Promise<any> {
  let retryCount = 0;
  
  while (retryCount < maxRetries) {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

      const response = await fetch(`${API_URL}?key=${API_KEY}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{
            parts: [
              { text: prompt },
              {
                inline_data: {
                  mime_type: "application/pdf",
                  data: pdfBase64
                }
              }
            ]
          }],
          generation_config: {
            temperature: 0.1,
            top_k: 32,
            top_p: 0.95,
            max_output_tokens: 8192,
          }
        }),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        const data = await response.json();
        
        // Validate response structure
        if (!data.candidates || !data.candidates[0] || 
            !data.candidates[0].content || !data.candidates[0].content.parts || 
            !data.candidates[0].content.parts[0].text) {
          throw new Error("Unexpected response format from Gemini API");
        }
        
        return data;
      } else {
        throw new Error(`Gemini API error: ${response.status} ${response.statusText}`);
      }
    } catch (error) {
      retryCount++;
      console.warn(`Retry ${retryCount}/${maxRetries} due to: ${error}`);

      if (retryCount >= maxRetries) {
        throw error;
      }

      // Exponential backoff
      await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, retryCount)));
    }
  }
  
  throw new Error(`Failed to get response from Gemini API after ${maxRetries} attempts`);
}

/**
 * Parse Gemini API response into CheckResult
 */
function parseGeminiResponse(responseText: string, checkType: string): CheckResult {
  const lines = responseText.split('\n');
  const firstLine = lines[0].trim().toLowerCase();
  const explanation = lines.slice(1).join('\n').trim();

  // Determine compliance
  let isCompliant = firstLine === 'yes';
  
  // Special handling for specific check types
  if (checkType === "profit_loss") {
    isCompliant = firstLine === 'compliant';
  }

  // Extract additional data for specific checks
  let extractedData = null;
  
  switch (checkType) {
    case 'signature_date':
      if (lines.length > 1) {
        extractedData = { signatureBlock: lines.slice(1).join('\n') };
      }
      break;
      
    case 'company_name_consistency':
      if (lines.length >= 3) {
        extractedData = {
          totalMentions: lines[1].trim(),
          variations: lines[2].trim(),
          details: lines.slice(3).join('\n').trim()
        };
      }
      break;
      
    case 'caro_interlink':
    case 'ifc_interlink':
      if (lines.length >= 2) {
        extractedData = { paragraphComparison: lines[1].trim() };
      }
      break;
  }

  return {
    isCompliant,
    explanation: explanation || `${checkType} check completed`,
    confidence: 0.9,
    extractedData,
    detail: lines.length > 2 ? lines.slice(2).join('\n').trim() : ""
  };
}

/**
 * Process a single document check using Gemini AI
 */
export async function processSingleDocumentCheck(
  pdfFile: File,
  checkType: string,
  promptKey: string,
  parameters: Record<string, any>
): Promise<CheckResult> {
  try {
    console.log(`Processing ${checkType} check with Gemini AI`);

    // Get PDF as Base64
    const pdfBase64 = await getPdfAsBase64(pdfFile);

    // Render prompt with parameters
    const prompt = renderPrompt(promptKey, parameters);

    // Call Gemini API
    const data = await callGeminiAPI(prompt, pdfBase64);

    // Parse response
    const responseText = data.candidates[0].content.parts[0].text;
    const result = parseGeminiResponse(responseText, checkType);

    console.log(`Successfully processed ${checkType} check:`, result);
    return result;

  } catch (error) {
    console.error(`Error processing ${checkType} check:`, error);

    return {
      isCompliant: false,
      explanation: `Error processing check: ${error instanceof Error ? error.message : String(error)}`,
      confidence: 0.5,
      extractedData: null,
      detail: ""
    };
  }
}

/**
 * Clear PDF cache for memory management
 */
export function clearPdfCache(): void {
  pdfCache.clear();
  console.log('PDF cache cleared');
}

/**
 * Get cache statistics
 */
export function getCacheStats(): { size: number; keys: string[] } {
  return {
    size: pdfCache.size,
    keys: Array.from(pdfCache.keys())
  };
}