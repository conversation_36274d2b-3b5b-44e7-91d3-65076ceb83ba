import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { X, ExternalLink, Download, AlertTriangle, RefreshCw } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { storage } from "@/lib/firebase";
import { ref, getDownloadURL } from "firebase/storage";

/**
 * Utility function to get a document URL directly from Firebase Storage
 * This is exported for use by other components that need to access documents
 */
export const getDocumentDirectUrl = async (documentPath: string): Promise<string> => {
  try {
    console.log(`CommonDocumentViewer: Getting direct URL for ${documentPath}`);

    // Create a reference to the file in Firebase Storage
    const fileRef = ref(storage, documentPath);

    // Get the download URL
    const downloadUrl = await getDownloadURL(fileRef);
    console.log(`CommonDocumentViewer: Got direct URL: ${downloadUrl}`);

    if (!downloadUrl) {
      throw new Error(`Failed to get download URL for ${documentPath}`);
    }

    // Return the URL with a timestamp to prevent browser caching
    return downloadUrl;
  } catch (err) {
    console.error(`CommonDocumentViewer: Error getting direct URL for ${documentPath}:`, err);
    throw err;
  }
};

interface DocumentViewerProps {
  url: string;
  title?: string;
  isOpen: boolean;
  onClose: () => void;
}

const DocumentViewer: React.FC<DocumentViewerProps> = ({
  url,
  title = "Document Viewer",
  isOpen,
  onClose
}) => {
  const [viewerType, setViewerType] = useState<'google' | 'direct'>('direct');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [loadAttempts, setLoadAttempts] = useState(0);
  const [documentUrl, setDocumentUrl] = useState<string>(url);

  // Validate URL
  const isValidUrl = documentUrl && (documentUrl.startsWith('http://') || documentUrl.startsWith('https://'));

  // Add cache busting parameter to prevent caching issues
  const cacheBustedUrl = isValidUrl
    ? `${documentUrl}${documentUrl.includes('?') ? '&' : '?'}_cb=${Date.now()}`
    : '';

  // Encode the URL for Google Docs Viewer
  const googleDocsViewerUrl = isValidUrl
    ? `https://docs.google.com/viewer?url=${encodeURIComponent(cacheBustedUrl)}&embedded=true`
    : '';

  // Reset states when the document changes or dialog opens
  useEffect(() => {
    if (isOpen) {
      console.log("DocumentViewer: Opening document with URL:", url);
      setDocumentUrl(url); // Update the document URL
      setIsLoading(true);
      setError(null);
      setLoadAttempts(0);

      if (!url || !(url.startsWith('http://') || url.startsWith('https://'))) {
        console.error("DocumentViewer: Invalid URL format:", url);
        setIsLoading(false);
        setError("Invalid document URL. The document may have been deleted or the URL is malformed.");
      }
    }
  }, [url, isOpen]);

  // Log when dialog open state changes
  useEffect(() => {
    console.log("DocumentViewer: Dialog open state changed to:", isOpen);
  }, [isOpen]);

  // Handle iframe load event
  const handleIframeLoad = () => {
    console.log("DocumentViewer: Document loaded successfully");
    setIsLoading(false);
  };

  // Handle iframe error
  const handleIframeError = () => {
    console.error(`DocumentViewer: Error loading document (attempt ${loadAttempts + 1})`);
    setLoadAttempts(prev => prev + 1);

    if (loadAttempts >= 2) {
      setIsLoading(false);
      setError(`Failed to load the document. Please try a different viewer or download the document. URL: ${documentUrl.substring(0, 100)}...`);
    } else {
      // Try again with a different viewer
      console.log("DocumentViewer: Switching viewer type");
      setViewerType(viewerType === 'google' ? 'direct' : 'google');
    }
  };

  // Function to refresh the document with a new cache-busting parameter
  const refreshDocument = () => {
    console.log("DocumentViewer: Refreshing document");
    setIsLoading(true);
    setError(null);
    setLoadAttempts(0);
    // Force a new cache-busting parameter by updating the URL
    setDocumentUrl(`${url}${url.includes('?') ? '&' : '?'}_cb=${Date.now()}`);
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-w-5xl w-[95vw] h-[85vh] p-0">
        <DialogHeader className="p-4 flex flex-row items-center justify-between border-b">
          <DialogTitle>{title}</DialogTitle>
          <div className="flex items-center space-x-2">
            {isValidUrl && (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setViewerType(viewerType === 'google' ? 'direct' : 'google');
                    setIsLoading(true);
                    setError(null);
                  }}
                  title={viewerType === 'google' ? 'Switch to direct PDF view' : 'Switch to Google Docs viewer'}
                  disabled={!isValidUrl}
                >
                  {viewerType === 'google' ? 'Direct View' : 'Google View'}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={refreshDocument}
                  title="Refresh document"
                  disabled={!isValidUrl}
                >
                  <RefreshCw className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => window.open(documentUrl, '_blank')}
                  title="Open in new tab"
                  disabled={!isValidUrl}
                >
                  <ExternalLink className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    try {
                      const link = document.createElement('a');
                      link.href = documentUrl;
                      link.download = title || 'document.pdf';
                      document.body.appendChild(link);
                      link.click();
                      document.body.removeChild(link);
                      console.log("DocumentViewer: Download initiated for:", documentUrl);
                    } catch (err) {
                      console.error("Error downloading document:", err);
                      setError("Failed to download the document. The URL may be invalid.");
                    }
                  }}
                  title="Download document"
                  disabled={!isValidUrl}
                >
                  <Download className="h-4 w-4" />
                </Button>
              </>
            )}
            <Button variant="ghost" size="icon" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </DialogHeader>
        <div className="w-full h-full relative">
          {error && (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-50 z-10 p-6">
              <div className="max-w-md">
                <Alert variant="destructive" className="mb-4">
                  <AlertTriangle className="h-4 w-4 mr-2" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
                <div className="flex justify-center space-x-2 mt-4">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setError(null);
                      setIsLoading(true);
                      setViewerType(viewerType === 'google' ? 'direct' : 'google');
                    }}
                    disabled={!isValidUrl}
                  >
                    Try {viewerType === 'google' ? 'Direct' : 'Google'} Viewer
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={refreshDocument}
                    disabled={!isValidUrl}
                  >
                    Refresh Document
                  </Button>
                </div>
              </div>
            </div>
          )}

          {isLoading && !error && (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-50 bg-opacity-75 z-10">
              <div className="flex flex-col items-center">
                <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
                <p className="mt-2 text-gray-600">Loading document...</p>
              </div>
            </div>
          )}

          {isValidUrl && !error && (
            viewerType === 'google' ? (
              <iframe
                src={googleDocsViewerUrl}
                title="Document Viewer"
                className="w-full h-[calc(85vh-60px)] border-0"
                onLoad={handleIframeLoad}
                onError={handleIframeError}
              />
            ) : (
              <iframe
                src={cacheBustedUrl}
                title="Document Viewer"
                className="w-full h-[calc(85vh-60px)] border-0"
                onLoad={handleIframeLoad}
                onError={handleIframeError}
              />
            )
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default DocumentViewer;
