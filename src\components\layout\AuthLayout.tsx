import React, { useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";

interface AuthLayoutProps {
  children: React.ReactNode;
}

const AuthLayout: React.FC<AuthLayoutProps> = ({ children }) => {
  const { currentUser } = useAuth();
  const navigate = useNavigate();
  
  // Debug information
  useEffect(() => {
    console.log("AuthLayout rendered with currentUser:", currentUser ? "Logged in" : "Not logged in");
  }, [currentUser]);
  
  // Redirect to dashboard if user is already logged in
  useEffect(() => {
    if (currentUser) {
      console.log("User is already logged in, redirecting to dashboard");
      navigate('/dashboard');
    }
  }, [currentUser, navigate]);

  return (
    <div className="min-h-screen w-full">
      {children}
    </div>
  );
};

export default AuthLayout;