import { db } from '@/lib/firebase';
import { collection, addDoc, getDocs, doc, updateDoc, deleteDoc, query, where } from 'firebase/firestore';
import { CustomChecklistItem } from '@/lib/types/checklist';

const COLLECTION_NAME = 'customChecklist';

export const addCustomChecklistItem = async (item: Omit<CustomChecklistItem, 'id' | 'createdAt' | 'updatedAt'>) => {
  try {
    const docRef = await addDoc(collection(db, COLLECTION_NAME), {
      ...item,
      createdAt: new Date(),
      updatedAt: new Date()
    });
    return docRef.id;
  } catch (error) {
    console.error('Error adding custom checklist item:', error);
    throw error;
  }
};

export const getCustomChecklistItems = async () => {
  try {
    const querySnapshot = await getDocs(collection(db, COLLECTION_NAME));
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as CustomChecklistItem[];
  } catch (error) {
    console.error('Error getting custom checklist items:', error);
    throw error;
  }
};

export const updateCustomChecklistItem = async (id: string, updates: Partial<CustomChecklistItem>) => {
  try {
    const docRef = doc(db, COLLECTION_NAME, id);
    await updateDoc(docRef, {
      ...updates,
      updatedAt: new Date()
    });
  } catch (error) {
    console.error('Error updating custom checklist item:', error);
    throw error;
  }
};

export const deleteCustomChecklistItem = async (id: string) => {
  try {
    const docRef = doc(db, COLLECTION_NAME, id);
    await deleteDoc(docRef);
  } catch (error) {
    console.error('Error deleting custom checklist item:', error);
    throw error;
  }
};

// Get custom checklist items for specific document types
export const getCustomChecklistItemsByDocumentTypes = async (documentTypes: string[]) => {
  try {
    const q = query(collection(db, COLLECTION_NAME), 
      where('documentTypes', 'array-contains-any', documentTypes));
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as CustomChecklistItem[];
  } catch (error) {
    console.error('Error getting custom checklist items by document types:', error);
    throw error;
  }
};
