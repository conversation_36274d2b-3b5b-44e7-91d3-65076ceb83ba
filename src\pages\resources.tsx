import React from "react";
import { <PERSON><PERSON>eft, FileText, Download, BookOpen, Video, FileCheck } from "lucide-react";
import { <PERSON> } from "react-router-dom";
import { Button } from "@/components/ui/button";

const ResourceCenter = () => {
  return (
    <div className="min-h-screen bg-slate-900 text-white">
      {/* Header */}
      <header className="bg-slate-900/95 backdrop-blur-lg border-b border-white/10">
        <div className="w-full px-6 md:px-12 lg:px-20 py-6 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="h-10 w-10 rounded-xl bg-gradient-to-br from-blue-600 to-purple-600 flex items-center justify-center text-white font-bold">
              P
            </div>
            <span className="text-xl font-bold text-white">PKF Audit AI</span>
          </div>

          <Link to="/">
            <Button variant="ghost" className="text-white">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Home
            </Button>
          </Link>
        </div>
      </header>

      {/* Page Content */}
      <div className="w-full px-6 md:px-12 lg:px-20 py-16">
        <h1 className="text-4xl md:text-5xl font-bold mb-6">Resource Center</h1>
        <p className="text-xl text-white/80 mb-12 max-w-3xl">
          Access our comprehensive collection of guides, white papers, and videos to help you get the most out of our AI audit solutions.
        </p>

        {/* Resource Categories */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          {/* Guides */}
          <div className="bg-white/5 backdrop-blur-xl border border-white/20 rounded-3xl p-8 text-center hover:bg-white/10 transition-colors">
            <div className="h-16 w-16 mx-auto mb-4 bg-gradient-to-br from-blue-600 to-purple-600 rounded-full flex items-center justify-center">
              <BookOpen className="h-8 w-8 text-white" />
            </div>
            <h3 className="text-2xl font-bold mb-2">User Guides</h3>
            <p className="text-white/70 mb-4">
              Step-by-step guides to help you navigate our platform and leverage all features.
            </p>
            <Button className="bg-gradient-to-r from-blue-600 to-purple-600">
              Browse Guides
            </Button>
          </div>

          {/* White Papers */}
          <div className="bg-white/5 backdrop-blur-xl border border-white/20 rounded-3xl p-8 text-center hover:bg-white/10 transition-colors">
            <div className="h-16 w-16 mx-auto mb-4 bg-gradient-to-br from-green-600 to-teal-600 rounded-full flex items-center justify-center">
              <FileText className="h-8 w-8 text-white" />
            </div>
            <h3 className="text-2xl font-bold mb-2">White Papers</h3>
            <p className="text-white/70 mb-4">
              In-depth research and thought leadership on AI in financial auditing.
            </p>
            <Button className="bg-gradient-to-r from-green-600 to-teal-600">
              Download Papers
            </Button>
          </div>

          {/* Video Tutorials */}
          <div className="bg-white/5 backdrop-blur-xl border border-white/20 rounded-3xl p-8 text-center hover:bg-white/10 transition-colors">
            <div className="h-16 w-16 mx-auto mb-4 bg-gradient-to-br from-purple-600 to-pink-600 rounded-full flex items-center justify-center">
              <Video className="h-8 w-8 text-white" />
            </div>
            <h3 className="text-2xl font-bold mb-2">Video Tutorials</h3>
            <p className="text-white/70 mb-4">
              Visual walkthroughs and demonstrations of key features and processes.
            </p>
            <Button className="bg-gradient-to-r from-purple-600 to-pink-600">
              Watch Videos
            </Button>
          </div>
        </div>

        {/* Featured Resources */}
        <h2 className="text-3xl font-bold mb-8">Featured Resources</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {/* Resource 1 */}
          <div className="bg-white/5 backdrop-blur-sm border border-white/20 rounded-2xl overflow-hidden">
            <div className="p-6">
              <div className="flex justify-between items-start mb-4">
                <div className="p-2 bg-blue-500/20 rounded-lg">
                  <FileText className="h-6 w-6 text-blue-400" />
                </div>
                <span className="text-xs bg-blue-500/20 text-blue-400 py-1 px-2 rounded-full">Guide</span>
              </div>
              <h3 className="text-xl font-bold mb-3">Getting Started with PKF Audit AI</h3>
              <p className="text-white/70 mb-4">
                Your complete introduction to the platform. Learn how to set up your account, navigate the interface, and start your first audit.
              </p>
              <div className="flex justify-between items-center">
                <span className="text-white/50 text-sm">10 min read</span>
                <Button variant="outline" size="sm" className="border-blue-500/50 text-blue-400">
                  <Download className="h-4 w-4 mr-2" />
                  Download
                </Button>
              </div>
            </div>
          </div>

          {/* Resource 2 */}
          <div className="bg-white/5 backdrop-blur-sm border border-white/20 rounded-2xl overflow-hidden">
            <div className="p-6">
              <div className="flex justify-between items-start mb-4">
                <div className="p-2 bg-green-500/20 rounded-lg">
                  <FileText className="h-6 w-6 text-green-400" />
                </div>
                <span className="text-xs bg-green-500/20 text-green-400 py-1 px-2 rounded-full">White Paper</span>
              </div>
              <h3 className="text-xl font-bold mb-3">The Impact of AI on Financial Audit Accuracy</h3>
              <p className="text-white/70 mb-4">
                Research study examining how AI technologies improve audit accuracy rates across different industries and scenarios.
              </p>
              <div className="flex justify-between items-center">
                <span className="text-white/50 text-sm">25 pages</span>
                <Button variant="outline" size="sm" className="border-green-500/50 text-green-400">
                  <Download className="h-4 w-4 mr-2" />
                  Download
                </Button>
              </div>
            </div>
          </div>

          {/* Resource 3 */}
          <div className="bg-white/5 backdrop-blur-sm border border-white/20 rounded-2xl overflow-hidden">
            <div className="p-6">
              <div className="flex justify-between items-start mb-4">
                <div className="p-2 bg-purple-500/20 rounded-lg">
                  <Video className="h-6 w-6 text-purple-400" />
                </div>
                <span className="text-xs bg-purple-500/20 text-purple-400 py-1 px-2 rounded-full">Video</span>
              </div>
              <h3 className="text-xl font-bold mb-3">Advanced Document Analysis Tutorial</h3>
              <p className="text-white/70 mb-4">
                Learn how to use our advanced document analysis tools to automatically extract and verify financial information.
              </p>
              <div className="flex justify-between items-center">
                <span className="text-white/50 text-sm">15 min video</span>
                <Button variant="outline" size="sm" className="border-purple-500/50 text-purple-400">
                  <Video className="h-4 w-4 mr-2" />
                  Watch Now
                </Button>
              </div>
            </div>
          </div>

          {/* Resource 4 */}
          <div className="bg-white/5 backdrop-blur-sm border border-white/20 rounded-2xl overflow-hidden">
            <div className="p-6">
              <div className="flex justify-between items-start mb-4">
                <div className="p-2 bg-orange-500/20 rounded-lg">
                  <FileCheck className="h-6 w-6 text-orange-400" />
                </div>
                <span className="text-xs bg-orange-500/20 text-orange-400 py-1 px-2 rounded-full">Checklist</span>
              </div>
              <h3 className="text-xl font-bold mb-3">Audit Preparation Checklist</h3>
              <p className="text-white/70 mb-4">
                A comprehensive checklist to ensure you have all the necessary documents and data ready for AI-powered audit analysis.
              </p>
              <div className="flex justify-between items-center">
                <span className="text-white/50 text-sm">5 min read</span>
                <Button variant="outline" size="sm" className="border-orange-500/50 text-orange-400">
                  <Download className="h-4 w-4 mr-2" />
                  Download
                </Button>
              </div>
            </div>
          </div>

          {/* Resource 5 */}
          <div className="bg-white/5 backdrop-blur-sm border border-white/20 rounded-2xl overflow-hidden">
            <div className="p-6">
              <div className="flex justify-between items-start mb-4">
                <div className="p-2 bg-blue-500/20 rounded-lg">
                  <FileText className="h-6 w-6 text-blue-400" />
                </div>
                <span className="text-xs bg-blue-500/20 text-blue-400 py-1 px-2 rounded-full">Guide</span>
              </div>
              <h3 className="text-xl font-bold mb-3">Compliance and Regulatory Features</h3>
              <p className="text-white/70 mb-4">
                Detailed guide on using our platform's compliance features to ensure adherence to various financial regulations.
              </p>
              <div className="flex justify-between items-center">
                <span className="text-white/50 text-sm">12 min read</span>
                <Button variant="outline" size="sm" className="border-blue-500/50 text-blue-400">
                  <Download className="h-4 w-4 mr-2" />
                  Download
                </Button>
              </div>
            </div>
          </div>

          {/* Resource 6 */}
          <div className="bg-white/5 backdrop-blur-sm border border-white/20 rounded-2xl overflow-hidden">
            <div className="p-6">
              <div className="flex justify-between items-start mb-4">
                <div className="p-2 bg-purple-500/20 rounded-lg">
                  <Video className="h-6 w-6 text-purple-400" />
                </div>
                <span className="text-xs bg-purple-500/20 text-purple-400 py-1 px-2 rounded-full">Video</span>
              </div>
              <h3 className="text-xl font-bold mb-3">Multi-Document Analysis Walkthrough</h3>
              <p className="text-white/70 mb-4">
                Watch how to cross-reference multiple financial documents simultaneously using our AI-powered analysis tools.
              </p>
              <div className="flex justify-between items-center">
                <span className="text-white/50 text-sm">20 min video</span>
                <Button variant="outline" size="sm" className="border-purple-500/50 text-purple-400">
                  <Video className="h-4 w-4 mr-2" />
                  Watch Now
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Request Resources */}
        <div className="mt-20 bg-white/5 backdrop-blur-xl border border-white/20 rounded-3xl p-8 text-center">
          <h2 className="text-2xl font-bold mb-4">Need a Specific Resource?</h2>
          <p className="text-white/70 mb-8 max-w-2xl mx-auto">
            Can't find what you're looking for? Let us know and our team will help you find or create the resources you need.
          </p>
          <Button className="bg-gradient-to-r from-blue-600 to-purple-600">
            Request a Resource
          </Button>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-slate-900 border-t border-white/10 py-8">
        <div className="w-full px-6 md:px-12 lg:px-20 text-center">
          <p className="text-white/60">© {new Date().getFullYear()} PKF Audit AI. All rights reserved.</p>
        </div>
      </footer>
    </div>
  );
};

export default ResourceCenter;
