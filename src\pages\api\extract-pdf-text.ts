import { NextApiRequest, NextApiResponse } from 'next';
import formidable from 'formidable';
import fs from 'fs';
import * as pdfjs from 'pdfjs-dist';

// Disable the default body parser to handle file uploads
export const config = {
  api: {
    bodyParser: false,
  },
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Parse the form data
    const form = formidable({ multiples: false });
    
    const parseForm = async (): Promise<{ fields: formidable.Fields; files: formidable.Files }> => {
      return new Promise((resolve, reject) => {
        form.parse(req, (err, fields, files) => {
          if (err) reject(err);
          resolve({ fields, files });
        });
      });
    };

    const { files } = await parseForm();
    
    // Get the uploaded file
    const fileField = files.pdfFile;
    if (!fileField || Array.isArray(fileField)) {
      return res.status(400).json({ error: 'No PDF file uploaded' });
    }

    // Read the file
    const fileData = fs.readFileSync(fileField.filepath);
    
    // Extract text using PDF.js
    const loadingTask = pdfjs.getDocument({ data: fileData });
    const pdf = await loadingTask.promise;
    
    let extractedText = '';
    
    // Extract text from each page
    for (let i = 1; i <= pdf.numPages; i++) {
      const page = await pdf.getPage(i);
      const content = await page.getTextContent();
      const strings = content.items.map((item: any) => item.str);
      extractedText += strings.join(' ') + '\\n';
    }
    
    // Return the extracted text
    return res.status(200).json({ text: extractedText });
  } catch (error) {
    console.error('Error extracting PDF text:', error);
    return res.status(500).json({ 
      error: 'Error extracting PDF text', 
      message: error instanceof Error ? error.message : String(error) 
    });
  }
}
