// Firebase Storage Rules - Updated for better access
// Copy and paste these rules in your Firebase Console > Storage > Rules

rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Allow read access to all files for authenticated users
    match /{allPaths=**} {
      // Allow read access to any authenticated user - more permissive for PDF access
      allow read: if request.auth != null;

      // Allow write access only to user's own directory
      allow write: if request.auth != null &&
                    (request.resource.size < 10 * 1024 * 1024 && // 10MB file size limit
                     request.resource.contentType.matches('application/pdf') && // Only allow PDFs
                     request.path.matches('/users/' + request.auth.uid + '/.*')); // Only in user's directory

      // Allow users to delete their own files
      allow delete: if request.auth != null &&
                     request.path.matches('/users/' + request.auth.uid + '/.*');
    }
  }
}

// CORS Configuration for Firebase Storage
// You need to set up CORS for your Firebase Storage bucket
// Run the following command using gsutil (Google Cloud SDK):

/*
gsutil cors set cors-config.json gs://YOUR-BUCKET-NAME
*/

// Create a file named cors-config.json with the following content:
/*
[
  {
    "origin": ["*"],
    "method": ["GET", "HEAD", "OPTIONS"],
    "responseHeader": [
      "Content-Type",
      "Access-Control-Allow-Origin",
      "Content-Length",
      "User-Agent",
      "X-Requested-With"
    ],
    "maxAgeSeconds": 3600
  }
]
*/

// For production, replace "*" with your actual domain(s)
