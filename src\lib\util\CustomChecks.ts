import { CheckResult } from '../types/checklist';

// Custom check implementations
export const executeCustomCheck = async (
  type: string,
  text: string,
  params: Record<string, any>
): Promise<CheckResult> => {
  switch (type) {
    case 'text_search':
      return executeTextSearch(text, params);
    case 'regex_match':
      return executeRegexMatch(text, params);
    case 'number_comparison':
      return executeNumberComparison(text, params);
    case 'section_exists':
      return executeSectionExists(text, params);
    case 'value_extraction':
      return executeValueExtraction(text, params);
    default:
      throw new Error(`Unsupported check type: ${type}`);
  }
};

const executeTextSearch = (text: string, params: Record<string, any>): CheckResult => {
  const searchText = params.searchText;
  const caseSensitive = params.caseSensitive || false;

  if (!searchText) {
    return {
      isCompliant: false,
      explanation: 'No search text provided',
      confidence: 0
    };
  }

  const textToSearch = caseSensitive ? text : text.toLowerCase();
  const searchFor = caseSensitive ? searchText : searchText.toLowerCase();

  const found = textToSearch.includes(searchFor);
  return {
    isCompliant: found,
    explanation: found ? `Text "${searchText}" found` : `Text "${searchText}" not found`,
    confidence: found ? 1 : 0
  };
};

const executeRegexMatch = (text: string, params: Record<string, any>): CheckResult => {
  const pattern = params.pattern;
  
  if (!pattern) {
    return {
      isCompliant: false,
      explanation: 'No pattern provided',
      confidence: 0
    };
  }

  try {
    const regex = new RegExp(pattern);
    const matches = text.match(regex);
    const found = matches !== null;

    return {
      isCompliant: found,
      explanation: found ? `Pattern matched ${matches.length} times` : 'Pattern not found',
      confidence: found ? 1 : 0,
      extractedData: matches ? matches.join(', ') : undefined
    };
  } catch (error) {
    return {
      isCompliant: false,
      explanation: `Invalid regex pattern: ${error.message}`,
      confidence: 0
    };
  }
};

const executeNumberComparison = (text: string, params: Record<string, any>): CheckResult => {
  const { extractionPattern, operator, compareValue } = params;

  if (!extractionPattern || !operator || compareValue === undefined) {
    return {
      isCompliant: false,
      explanation: 'Missing required parameters',
      confidence: 0
    };
  }

  try {
    const regex = new RegExp(extractionPattern);
    const matches = text.match(regex);
    if (!matches) {
      return {
        isCompliant: false,
        explanation: 'Could not extract number from text',
        confidence: 0
      };
    }

    const extractedNumber = parseFloat(matches[0]);
    if (isNaN(extractedNumber)) {
      return {
        isCompliant: false,
        explanation: 'Extracted value is not a valid number',
        confidence: 0
      };
    }

    let isCompliant = false;
    switch (operator) {
      case '>':
        isCompliant = extractedNumber > compareValue;
        break;
      case '<':
        isCompliant = extractedNumber < compareValue;
        break;
      case '=':
        isCompliant = extractedNumber === compareValue;
        break;
      case '>=':
        isCompliant = extractedNumber >= compareValue;
        break;
      case '<=':
        isCompliant = extractedNumber <= compareValue;
        break;
      default:
        return {
          isCompliant: false,
          explanation: `Invalid operator: ${operator}`,
          confidence: 0
        };
    }

    return {
      isCompliant,
      explanation: `Extracted value ${extractedNumber} ${operator} ${compareValue}: ${isCompliant ? 'Pass' : 'Fail'}`,
      confidence: 1,
      extractedData: extractedNumber.toString()
    };
  } catch (error) {
    return {
      isCompliant: false,
      explanation: `Error extracting number: ${error.message}`,
      confidence: 0
    };
  }
};

const executeSectionExists = (text: string, params: Record<string, any>): CheckResult => {
  const { sectionIdentifier } = params;

  if (!sectionIdentifier) {
    return {
      isCompliant: false,
      explanation: 'No section identifier provided',
      confidence: 0
    };
  }

  // First try exact match
  const exactMatch = text.includes(sectionIdentifier);

  // If no exact match, try with flexible white space
  const flexibleMatch = !exactMatch && text.replace(/\s+/g, ' ')
    .toLowerCase()
    .includes(sectionIdentifier.replace(/\s+/g, ' ').toLowerCase());

  return {
    isCompliant: exactMatch || flexibleMatch,
    explanation: exactMatch 
      ? `Section "${sectionIdentifier}" found (exact match)`
      : flexibleMatch 
        ? `Section "${sectionIdentifier}" found (flexible match)` 
        : `Section "${sectionIdentifier}" not found`,
    confidence: exactMatch ? 1 : flexibleMatch ? 0.8 : 0
  };
};

const executeValueExtraction = (text: string, params: Record<string, any>): CheckResult => {
  const { extractionPattern } = params;

  if (!extractionPattern) {
    return {
      isCompliant: false,
      explanation: 'No extraction pattern provided',
      confidence: 0
    };
  }

  try {
    const regex = new RegExp(extractionPattern);
    const matches = text.match(regex);

    if (!matches) {
      return {
        isCompliant: false,
        explanation: 'No value found matching the extraction pattern',
        confidence: 0
      };
    }

    return {
      isCompliant: true,
      explanation: `Successfully extracted ${matches.length} value(s)`,
      confidence: 1,
      extractedData: matches.length === 1 ? matches[0] : matches
    };
  } catch (error) {
    return {
      isCompliant: false,
      explanation: `Invalid extraction pattern: ${error.message}`,
      confidence: 0
    };
  }
};
