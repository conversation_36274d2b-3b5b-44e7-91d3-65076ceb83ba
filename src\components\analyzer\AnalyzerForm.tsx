// AnalyzerForm.tsx - COPY THIS ENTIRE CODE TO YOUR ACTUAL FILE

import React, { useState, useEffect } from "react";
import { Calendar } from "@/components/ui/calendar";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon, FileText, AlertCircle, Upload, X } from "lucide-react";
import { cn } from "@/lib/utils";
import { useToast } from "@/hooks/use-toast";
import { Card, CardContent } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { format } from "date-fns";

// Import new types and functions
import { 
  DocumentFiles, 
  AnalysisParameters
} from "@/lib/mainDocumentProcessor";

interface AnalyzerFormProps {
  onStartAnalysis: (documents: DocumentFiles, parameters: AnalysisParameters) => void;
  isProcessing: boolean;
}

const AnalyzerForm: React.FC<AnalyzerFormProps> = ({ onStartAnalysis, isProcessing }) => {
  const { toast } = useToast();

  // DEBUG: Check if onStartAnalysis prop is properly passed
  useEffect(() => {
    console.log('AnalyzerForm mounted with props:');
    console.log('onStartAnalysis type:', typeof onStartAnalysis);
    console.log('onStartAnalysis value:', onStartAnalysis);
    console.log('isProcessing:', isProcessing);
  }, [onStartAnalysis, isProcessing]);

  // Form state
  const [parameters, setParameters] = useState<AnalysisParameters>({
    company_name: "",
    audit_date: new Date(),
    profit_or_loss: "Profit",
    company_listing_status: "Unlisted",
    top_1000_or_500: "No",
    audit_report_type: "Normal",
    audit_opinion_type: "Unmodified",
    is_nbfc: "No",
    has_internal_auditor: "No",
    has_cost_auditor: "No",
    related_party_note_number: "",
  });

  // Document files state
  const [documents, setDocuments] = useState<DocumentFiles>({});
  
  // Debug state
  const [debugInfo, setDebugInfo] = useState({
    companyNameValid: false,
    auditReportUploaded: false,
    formValid: false
  });


  // Update the debug info to include the new field:
useEffect(() => {
  const companyNameValid = parameters.company_name.trim().length > 0;
  const uploadedDocuments = Object.values(documents).filter(file => file !== undefined);
  const documentsUploaded = uploadedDocuments.length > 0;
  const formValid = companyNameValid && documentsUploaded;
  
  setDebugInfo({
    companyNameValid,
    auditReportUploaded: !!documents.audit_report,
    documentsUploaded,
    formValid,
    hasInternalAuditor: parameters.has_internal_auditor // Add this for debugging
  });
}, [parameters.company_name, parameters.has_internal_auditor, documents]);
  
  // Handle parameter changes
  const handleParameterChange = (name: keyof AnalysisParameters, value: any) => {
    setParameters(prev => {
      const updated = { ...prev, [name]: value };
      
      // Auto-reset dependent fields
      if (name === "company_listing_status" && value === "Unlisted") {
        updated.top_1000_or_500 = "No";
      }
      
      return updated;
    });
  };

  // Handle file uploads
  const handleFileUpload = (documentType: keyof DocumentFiles, event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    
    if (file) {
      // Basic validation
      if (file.type !== 'application/pdf') {
        toast({
          title: "Invalid File Type",
          description: `Please upload a PDF file for ${documentType}`,
          variant: "destructive",
        });
        event.target.value = '';
        return;
      }
      
      if (file.size > 50 * 1024 * 1024) { // 50MB limit
        toast({
          title: "File Too Large",
          description: `File size should be less than 50MB for ${documentType}`,
          variant: "destructive",
        });
        event.target.value = '';
        return;
      }
    }
    
    setDocuments(prev => ({
      ...prev,
      [documentType]: file || undefined
    }));

    event.target.value = '';
  };

  // Remove uploaded file
  const removeFile = (documentType: keyof DocumentFiles) => {
    setDocuments(prev => {
      const updated = { ...prev };
      delete updated[documentType];
      return updated;
    });
  };

  // File upload component
  const FileUploader = ({ 
    documentType, 
    label, 
    required = false 
  }: { 
    documentType: keyof DocumentFiles; 
    label: string; 
    required?: boolean;
  }) => {
    const file = documents[documentType];
    
    return (
      <div className="space-y-2">
        <Label className="flex items-center gap-2">
          {label}
          {required && <span className="text-red-500">*</span>}
        </Label>
        
        {file ? (
          <div className="border-2 border-green-300 bg-green-50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <FileText className="h-8 w-8 text-green-500" />
                <div>
                  <p className="text-sm font-medium text-green-700">{file.name}</p>
                  <p className="text-xs text-green-600">
                    {(file.size / 1024 / 1024).toFixed(2)} MB
                  </p>
                </div>
              </div>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => removeFile(documentType)}
                disabled={isProcessing}
                className="text-red-500 hover:text-red-700"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        ) : (
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-gray-400 transition-colors">
            <input
              type="file"
              accept=".pdf"
              onChange={(e) => handleFileUpload(documentType, e)}
              className="hidden"
              id={`file-${documentType}`}
              disabled={isProcessing}
            />
            <label htmlFor={`file-${documentType}`} className="cursor-pointer">
              <div className="space-y-2">
                <Upload className="h-8 w-8 mx-auto text-gray-400" />
                <p className="text-sm text-gray-600">Click to upload PDF</p>
                <p className="text-xs text-gray-400">Maximum 50MB</p>
              </div>
            </label>
          </div>
        )}
      </div>
    );
  };

  // MAIN BUTTON CLICK HANDLER
  const handleStartAnalysisClick = () => {
    console.log('=== BUTTON CLICK DEBUG ===');
    console.log('onStartAnalysis function check:');
    console.log('- Type:', typeof onStartAnalysis);
    console.log('- Is function?', typeof onStartAnalysis === 'function');
    console.log('- Value:', onStartAnalysis);
    
    // Check if the function exists
    if (typeof onStartAnalysis !== 'function') {
      console.error('❌ onStartAnalysis is not a function!');
      console.error('Props received:', { onStartAnalysis, isProcessing });
      
      toast({
        title: "Component Error",
        description: "onStartAnalysis prop is not a function. Check parent component.",
        variant: "destructive",
      });
      return;
    }

    // Prevent double-submission
    if (isProcessing) {
      console.log('❌ Already processing, ignoring click');
      return;
    }

    // Basic validation
    if (!parameters.company_name.trim()) {
      console.log('❌ Validation failed: Company name empty');
      toast({
        title: "Company Name Required",
        description: "Please enter the company name",
        variant: "destructive",
      });
      return;
    }

    const uploadedDocuments = Object.values(documents).filter(file => file !== undefined);
if (uploadedDocuments.length === 0) {
  console.log('❌ Validation failed: No documents uploaded');
  toast({
    title: "Document Required",
    description: "Please upload at least one document PDF",
    variant: "destructive",
  });
  return;
}

    console.log('✅ Validation passed, calling onStartAnalysis...');
    console.log('Documents:', Object.keys(documents).filter(k => documents[k as keyof DocumentFiles]));
    console.log('Parameters:', parameters);
    
    try {
      // Call the function
      onStartAnalysis(documents, parameters);
      console.log('✅ onStartAnalysis called successfully');
    } catch (error) {
      console.error('❌ Error calling onStartAnalysis:', error);
      toast({
        title: "Function Call Error",
        description: `Error: ${error instanceof Error ? error.message : String(error)}`,
        variant: "destructive",
      });
    }
  };

  return (
    <div className="space-y-6">
      {/* Debug Panel - Shows current form state */}
      <Card className="border-blue-200 bg-blue-50">
        <CardContent className="pt-4">
          <h4 className="font-semibold text-blue-800 mb-2">Debug Information</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <strong>onStartAnalysis Type:</strong> {typeof onStartAnalysis}
            </div>
            <div>
              <strong>Is Function:</strong> {typeof onStartAnalysis === 'function' ? '✅ Yes' : '❌ No'}
            </div>
            <div>
              <strong>Company Name:</strong> {debugInfo.companyNameValid ? '✅ Valid' : '❌ Required'}
            </div>
            <div>
              <strong>Audit Report:</strong> {debugInfo.auditReportUploaded ? '✅ Uploaded' : '❌ Required'}
            </div>
            <div>
              <strong>Form Valid:</strong> {debugInfo.formValid ? '✅ Ready' : '❌ Incomplete'}
            </div>
            <div>
              <strong>Processing:</strong> {isProcessing ? '⏳ Running' : '✅ Ready'}
            </div>
          </div>
          {documents.audit_report && (
            <div className="mt-2 text-xs text-blue-700">
              Audit Report: {documents.audit_report.name} ({(documents.audit_report.size / 1024).toFixed(1)}KB)
            </div>
          )}
        </CardContent>
      </Card>

      {/* Company Information */}
      <Card>
        <CardContent className="pt-6">
          <h3 className="text-lg font-semibold mb-4">Company Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="companyName">Company Name *</Label>
              <Input
                id="companyName"
                type="text"
                placeholder="Enter Company Name"
                value={parameters.company_name}
                onChange={(e) => handleParameterChange("company_name", e.target.value)}
                required
                disabled={isProcessing}
                className={parameters.company_name.trim() ? "border-green-300" : "border-red-300"}
              />
            </div>

            <div>
              <Label htmlFor="auditDate">Audit Report Date *</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    id="auditDate"
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !parameters.audit_date && "text-muted-foreground"
                    )}
                    disabled={isProcessing}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {parameters.audit_date ? (
                      format(parameters.audit_date, "PPP")
                    ) : (
                      <span>Pick a date</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={parameters.audit_date}
                    onSelect={(date) => handleParameterChange("audit_date", date || new Date())}
                    initialFocus
                    disabled={isProcessing}
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Analysis Parameters */}
      <Card>
        <CardContent className="pt-6">
          <h3 className="text-lg font-semibold mb-4">Analysis Parameters</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
              <Label>📈 Outcome for the year</Label>
              <Select
                value={parameters.profit_or_loss}
                onValueChange={(value) => handleParameterChange("profit_or_loss", value)}
                disabled={isProcessing}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Profit">Profit</SelectItem>
                  <SelectItem value="Loss">Loss</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label>🏷️ Company status</Label>
              <Select
                value={parameters.company_listing_status}
                onValueChange={(value) => handleParameterChange("company_listing_status", value)}
                disabled={isProcessing}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Unlisted">Unlisted</SelectItem>
                  <SelectItem value="Listed">Listed</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label>🖋️ Opinion type</Label>
              <Select
                value={parameters.audit_opinion_type}
                onValueChange={(value) => handleParameterChange("audit_opinion_type", value)}
                disabled={isProcessing}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Unmodified">Unmodified</SelectItem>
                  <SelectItem value="Qualified">Qualified</SelectItem>
                  <SelectItem value="Adverse">Adverse</SelectItem>
                  <SelectItem value="Disclaimer">Disclaimer</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label>📑 Report variant</Label>
              <Select
                value={parameters.audit_report_type}
                onValueChange={(value) => handleParameterChange("audit_report_type", value)}
                disabled={isProcessing}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Normal">Normal</SelectItem>
                  <SelectItem value="Consolidated">Consolidated</SelectItem>
                  <SelectItem value="Standalone">Standalone</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {parameters.company_listing_status === "Listed" && (
              <div>
                <Label>🏆 In Top-1000 / Top-500?</Label>
                <Select
                  value={parameters.top_1000_or_500}
                  onValueChange={(value) => handleParameterChange("top_1000_or_500", value)}
                  disabled={isProcessing}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Yes">Yes</SelectItem>
                    <SelectItem value="No">No</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}

            <div>
              <Label>🏢 Is the entity an NBFC?</Label>
              <Select
                value={parameters.is_nbfc}
                onValueChange={(value) => handleParameterChange("is_nbfc", value)}
                disabled={isProcessing}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="No">No</SelectItem>
                  <SelectItem value="Yes">Yes</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
  <Label>👥 Is Company appointed Internal Auditor? (Mandatory)</Label>
  <Select
    value={parameters.has_internal_auditor}
    onValueChange={(value) => handleParameterChange("has_internal_auditor", value)}
    disabled={isProcessing}
  >
    <SelectTrigger>
      <SelectValue />
    </SelectTrigger>
    <SelectContent>
      <SelectItem value="No">No</SelectItem>
      <SelectItem value="Yes">Yes</SelectItem>
    </SelectContent>
  </Select>
</div>
          </div>
        </CardContent>
      </Card>

      {/* Document Upload */}
      <Card>
        <CardContent className="pt-6">
          <h3 className="text-lg font-semibold mb-4">Document Upload</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <FileUploader 
              documentType="audit_report" 
              label="1. Audit Report" 
              required 
            />
            <FileUploader 
              documentType="annexure_a" 
              label="2. Annexure A (CARO)" 
            />
            <FileUploader 
              documentType="annexure_b" 
              label="3. Annexure B (IFC)" 
            />
            <FileUploader 
              documentType="balance_sheet" 
              label="4. Balance Sheet" 
            />
            <FileUploader 
              documentType="notes" 
              label="5. Notes to Accounts" 
            />
            <FileUploader 
              documentType="pl_notes" 
              label="6. P&L Notes" 
            />
            <FileUploader 
              documentType="sec_report" 
              label="7. Secretarial Audit Report"
            />
            <FileUploader 
              documentType="annual_report" 
              label="8. Annual Report"
            />
            

          </div>
        </CardContent>
      </Card>

      {/* Submit Button */}
      <div className="flex justify-end space-x-4">
        <Button
          type="button"
          onClick={handleStartAnalysisClick}
          disabled={isProcessing || !debugInfo.formValid}
          className="w-full md:w-auto"
          size="lg"
        >
          {isProcessing ? (
            <span className="flex items-center">
              <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2"></div>
              Processing Analysis...
            </span>
          ) : (
            `Start Analysis ${debugInfo.formValid ? '✅' : '❌'}`
          )}
        </Button>
      </div>

      {/* Help Text */}
      {!debugInfo.formValid && (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            To start analysis: 
            {!debugInfo.companyNameValid && " Enter company name."}
            {!debugInfo.auditReportUploaded && " Upload audit report PDF."}
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
};

export default AnalyzerForm;