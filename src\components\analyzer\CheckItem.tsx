import React from 'react';
import { CheckResult } from '@/lib/checkDefinitions'; // Updated import
import { CheckCircle, XCircle } from 'lucide-react';

// Helper function to format JSON data as readable text
const formatJsonAsText = (data: any): string => {
  if (!data) return '';

  try {
    let parsedData = data;

    // If it's a string, try to parse it as JSON
    if (typeof data === 'string') {
      // Check if it looks like JSON (starts with { or [)
      const trimmed = data.trim();
      if (trimmed.startsWith('{') || trimmed.startsWith('[')) {
        try {
          parsedData = JSON.parse(data);
        } catch (parseError) {
          // If JSON parsing fails, return the original string
          return data;
        }
      } else {
        // If it doesn't look like JSON, return as is
        return data;
      }
    }

    // If it's an object, format it nicely
    if (typeof parsedData === 'object' && parsedData !== null) {
      let formatted = '';

      for (const [key, value] of Object.entries(parsedData)) {
        // Convert camelCase and snake_case to readable format
        const readableKey = key
          .replace(/([A-Z])/g, ' $1')
          .replace(/_/g, ' ')
          .replace(/^./, str => str.toUpperCase())
          .trim();

        if (typeof value === 'object' && value !== null) {
          formatted += `${readableKey}:\n`;
          for (const [subKey, subValue] of Object.entries(value)) {
            const readableSubKey = subKey
              .replace(/([A-Z])/g, ' $1')
              .replace(/_/g, ' ')
              .replace(/^./, str => str.toUpperCase())
              .trim();

            if (typeof subValue === 'boolean') {
              formatted += `  ${readableSubKey}: ${subValue ? 'Yes' : 'No'}\n`;
            } else if (subValue === null || subValue === undefined) {
              formatted += `  ${readableSubKey}: N/A\n`;
            } else {
              formatted += `  ${readableSubKey}: ${subValue}\n`;
            }
          }
          formatted += '\n';
        } else {
          if (typeof value === 'boolean') {
            formatted += `${readableKey}: ${value ? 'Yes' : 'No'}\n`;
          } else if (value === null || value === undefined) {
            formatted += `${readableKey}: N/A\n`;
          } else {
            // Handle special formatting for currency values
            if (typeof value === 'string' && value.includes('₹')) {
              formatted += `${readableKey}: ${value}\n`;
            } else if (typeof value === 'number' && key.toLowerCase().includes('amount')) {
              formatted += `${readableKey}: ₹${value} crores\n`;
            } else {
              // Handle long text values by truncating if needed
              const stringValue = String(value);
              if (stringValue.length > 200) {
                formatted += `${readableKey}: ${stringValue.substring(0, 200)}...\n`;
              } else {
                formatted += `${readableKey}: ${stringValue}\n`;
              }
            }
          }
        }
      }

      return formatted.trim();
    }

    return String(parsedData);
  } catch (error) {
    console.warn('Error formatting JSON data:', error);
    return String(data);
  }
};

// Define DocumentDetail if it's not globally available or imported
// This should match the interface in StreamlitStyleChecklist.tsx
interface DocumentDetail {
  name: string;
  id: string;
  url: string;
  path: string;
}

interface CheckItemProps {
  id: string;
  result: CheckResult;
  title: string; // Title for the check item
  associatedDocs?: DocumentDetail[]; // Optional: Documents associated with this check
}

const CheckItem: React.FC<CheckItemProps> = ({ result, title, associatedDocs }) => {
  const { isCompliant, explanation, detail } = result;

  // Determine icon and color based on compliance
  let icon: React.ReactNode, colorClass: string, statusText: string;
  if (isCompliant) {
    icon = <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0" />;
    colorClass = 'text-green-700 bg-green-50 border-green-300';
    statusText = 'Compliant';
  } else {
    icon = <XCircle className="h-5 w-5 text-red-500 flex-shrink-0" />;
    colorClass = 'text-red-700 bg-red-50 border-red-300';
    statusText = 'Non-Compliant';
  }

  return (
    <div className={`mb-4 p-3 border rounded-md ${colorClass} shadow-sm`}>
      <div className="flex items-start">
        <div className="mt-0.5 mr-2">{icon}</div>
        <div className="flex-1">
          <h4 className="font-medium text-base text-gray-800">{title}</h4>

          <div className={`text-sm font-semibold mb-1 ${isCompliant ? 'text-green-600' : 'text-red-600'}`}>
            {statusText}
          </div>

          {explanation && (
            <p className="text-sm text-gray-700 whitespace-pre-wrap">
              {formatJsonAsText(explanation)}
            </p>
          )}

          {detail && (
            <div className="mt-2 text-xs text-gray-600 bg-gray-50 p-2 rounded">
              <p className="font-semibold mb-1">Details:</p>
              <div className="whitespace-pre-wrap">{formatJsonAsText(detail)}</div>
            </div>
          )}

          {associatedDocs && associatedDocs.length > 0 && (
            <div className="mt-2 pt-2 border-t border-gray-200">
              <p className="text-xs text-gray-500 font-semibold mb-1">Associated Document(s):</p>
              <ul className="list-disc list-inside pl-1">
                {associatedDocs.map(doc => (
                  <li key={doc.id} className="text-xs text-gray-600">
                    {doc.name} {doc.url ? <a href={doc.url} target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">(view)</a> : ''}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CheckItem;