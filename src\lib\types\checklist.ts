export interface CustomChecklistItem {
  id: string;
  name: string;
  description: string;
  type: 'single' | 'multi';
  documentTypes: ('auditReport' | 'annexureA' | 'annexureB' | 'balanceSheet' | 'notes' | 'plNotes' | 'annualReport' | 'secretarialCompliance')[];
  checkType: string;
  parameters?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

export interface DocumentInfo {
  url: string;
  name: string;
  id: string;
  status?: string;
}

export interface DocumentExtractedData {
  text: string;
  metadata?: any;
  auditReport?: string;
  annexureA?: string;
  annexureB?: string;
  caroAnnexure?: string;
  ifcAnnexure?: string;
}

export interface ProcessingProgress {
  completed: number;
  total: number; 
  currentCheck: string;
}

export interface CheckResult {
  isCompliant: boolean;
  explanation: string;
  confidence: number;
  source?: string;
  extractedData?: string | DocumentExtractedData;
}
