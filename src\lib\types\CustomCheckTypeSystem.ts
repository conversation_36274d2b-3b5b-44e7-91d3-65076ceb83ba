export type CustomCheckType = 
  | 'text_search'           // Search for specific text in document
  | 'regex_match'           // Match text using regular expression
  | 'number_comparison'     // Compare numeric values
  | 'paragraph_match'       // Compare paragraphs between documents
  | 'section_exists'        // Check if a section exists
  | 'value_extraction'      // Extract specific values from text
  | 'cross_document_match'; // Compare content between documents

export interface ParameterDefinition {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'regex' | 'array';
  description: string;
  required: boolean;
  defaultValue?: any;  
}

export interface CustomCheckTypeDefinition {
  type: CustomCheckType;
  description: string;
  applicableToDocTypes: string[];
  parameters: ParameterDefinition[];
  supportsMultiDoc: boolean;
  validate: (params: Record<string, any>) => { isValid: boolean; error?: string };
}

export const checkTypeDefinitions: Record<CustomCheckType, CustomCheckTypeDefinition> = {
  text_search: {
    type: 'text_search',
    description: 'Search for specific text in document',
    applicableToDocTypes: ['auditReport', 'annexureA', 'annexureB', 'balanceSheet', 'notes', 'plNotes', 'annualReport', 'secretarialCompliance'],
    parameters: [
      {
        name: 'searchText',
        type: 'string',
        description: 'Text to search for',
        required: true
      },
      {
        name: 'caseSensitive',
        type: 'boolean',
        description: 'Whether to perform case-sensitive search',
        required: false,
        defaultValue: false
      }
    ],
    supportsMultiDoc: false,
    validate: (params) => {
      if (!params.searchText) {
        return { isValid: false, error: 'searchText is required' };
      }
      return { isValid: true };
    }
  },
  regex_match: {
    type: 'regex_match',
    description: 'Match text using regular expression',
    applicableToDocTypes: ['auditReport', 'annexureA', 'annexureB', 'balanceSheet', 'notes', 'plNotes', 'annualReport', 'secretarialCompliance'],
    parameters: [
      {
        name: 'pattern',
        type: 'regex',
        description: 'Regular expression pattern to match',
        required: true
      }
    ],
    supportsMultiDoc: false,
    validate: (params) => {
      if (!params.pattern) {
        return { isValid: false, error: 'pattern is required' };
      }
      try {
        new RegExp(params.pattern);
        return { isValid: true };
      } catch (e) {
        return { isValid: false, error: 'Invalid regular expression pattern' };
      }
    }
  },
  number_comparison: {
    type: 'number_comparison',
    description: 'Compare numeric values',
    applicableToDocTypes: ['balanceSheet', 'notes', 'plNotes'],
    parameters: [
      {
        name: 'extractionPattern',
        type: 'regex',
        description: 'Pattern to extract number',
        required: true
      },
      {
        name: 'operator',
        type: 'string',
        description: 'Comparison operator (>, <, =, >=, <=)',
        required: true
      },
      {
        name: 'compareValue',
        type: 'number',
        description: 'Value to compare against',
        required: true
      }
    ],
    supportsMultiDoc: false,
    validate: (params) => {
      if (!params.extractionPattern || !params.operator || params.compareValue === undefined) {
        return { isValid: false, error: 'extractionPattern, operator, and compareValue are required' };
      }
      if (!['>', '<', '=', '>=', '<='].includes(params.operator)) {
        return { isValid: false, error: 'Invalid operator' };
      }
      try {
        new RegExp(params.extractionPattern);
        return { isValid: true };
      } catch (e) {
        return { isValid: false, error: 'Invalid extraction pattern' };
      }
    }
  },
  paragraph_match: {
    type: 'paragraph_match',
    description: 'Compare paragraphs between documents',
    applicableToDocTypes: ['auditReport', 'annexureA', 'annexureB'],
    parameters: [
      {
        name: 'paragraphIdentifier',
        type: 'string',
        description: 'Text to identify the relevant paragraph',
        required: true
      }
    ],
    supportsMultiDoc: true,
    validate: (params) => {
      if (!params.paragraphIdentifier) {
        return { isValid: false, error: 'paragraphIdentifier is required' };
      }
      return { isValid: true };
    }
  },
  section_exists: {
    type: 'section_exists',
    description: 'Check if a section exists',
    applicableToDocTypes: ['auditReport', 'annexureA', 'annexureB', 'balanceSheet', 'notes', 'plNotes', 'annualReport', 'secretarialCompliance'],
    parameters: [
      {
        name: 'sectionIdentifier',
        type: 'string',
        description: 'Text to identify the section',
        required: true
      }
    ],
    supportsMultiDoc: false,
    validate: (params) => {
      if (!params.sectionIdentifier) {
        return { isValid: false, error: 'sectionIdentifier is required' };
      }
      return { isValid: true };
    }
  },
  value_extraction: {
    type: 'value_extraction',
    description: 'Extract specific values from text',
    applicableToDocTypes: ['balanceSheet', 'notes', 'plNotes'],
    parameters: [
      {
        name: 'extractionPattern',
        type: 'regex',
        description: 'Pattern to extract value',
        required: true
      }
    ],
    supportsMultiDoc: false,
    validate: (params) => {
      if (!params.extractionPattern) {
        return { isValid: false, error: 'extractionPattern is required' };
      }
      try {
        new RegExp(params.extractionPattern);
        return { isValid: true };
      } catch (e) {
        return { isValid: false, error: 'Invalid extraction pattern' };
      }
    }
  },
  cross_document_match: {
    type: 'cross_document_match',
    description: 'Compare content between documents',
    applicableToDocTypes: ['auditReport', 'annexureA', 'annexureB', 'balanceSheet', 'notes', 'plNotes'],
    parameters: [
      {
        name: 'extractionPatterns',
        type: 'array',
        description: 'Array of patterns to extract from each document',
        required: true
      },
      {
        name: 'matchType',
        type: 'string',
        description: 'Type of match (exact, contains, number)',
        required: true
      }
    ],
    supportsMultiDoc: true,
    validate: (params) => {
      if (!params.extractionPatterns || !Array.isArray(params.extractionPatterns)) {
        return { isValid: false, error: 'extractionPatterns must be an array' };
      }
      if (!params.matchType || !['exact', 'contains', 'number'].includes(params.matchType)) {
        return { isValid: false, error: 'Invalid matchType' };
      }
      return { isValid: true };
    }
  }
};
