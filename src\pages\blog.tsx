import React from "react";
import { <PERSON><PERSON>ef<PERSON> } from "lucide-react";
import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";

const Blog = () => {
  return (
    <div className="min-h-screen bg-slate-900 text-white">
      {/* Header */}
      <header className="bg-slate-900/95 backdrop-blur-lg border-b border-white/10">
        <div className="w-full px-6 md:px-12 lg:px-20 py-6 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="h-10 w-10 rounded-xl bg-gradient-to-br from-blue-600 to-purple-600 flex items-center justify-center text-white font-bold">
              P
            </div>
            <span className="text-xl font-bold text-white">PKF Audit AI</span>
          </div>

          <Link to="/">
            <Button variant="ghost" className="text-white">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Home
            </Button>
          </Link>
        </div>
      </header>

      {/* Page Content */}
      <div className="w-full px-6 md:px-12 lg:px-20 py-16">
        <h1 className="text-4xl md:text-5xl font-bold mb-6">PKF Audit AI Blog</h1>
        <p className="text-xl text-white/80 mb-12 max-w-3xl">
          Stay up to date with the latest news, insights, and updates in AI-powered audit technology.
        </p>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {/* Blog Post 1 */}
          <div className="bg-white/5 backdrop-blur-sm border border-white/20 rounded-2xl overflow-hidden">
            <div className="h-48 bg-gradient-to-br from-blue-600/20 to-purple-600/20">
              <div className="h-full w-full flex items-center justify-center">
                <div className="h-20 w-20 rounded-xl bg-gradient-to-br from-blue-600 to-purple-600 flex items-center justify-center text-white font-bold text-4xl">
                  AI
                </div>
              </div>
            </div>
            <div className="p-6">
              <div className="text-sm text-blue-400 mb-2">May 15, 2025</div>
              <h2 className="text-xl font-bold mb-3">The Future of AI in Financial Auditing</h2>
              <p className="text-white/70 mb-4">
                Explore how artificial intelligence is transforming the audit landscape and what it means for financial professionals.
              </p>
              <Button variant="link" className="text-blue-400 p-0">Read More →</Button>
            </div>
          </div>

          {/* Blog Post 2 */}
          <div className="bg-white/5 backdrop-blur-sm border border-white/20 rounded-2xl overflow-hidden">
            <div className="h-48 bg-gradient-to-br from-green-600/20 to-emerald-600/20">
              <div className="h-full w-full flex items-center justify-center">
                <div className="h-20 w-20 rounded-xl bg-gradient-to-br from-green-600 to-emerald-600 flex items-center justify-center text-white font-bold text-4xl">
                  $
                </div>
              </div>
            </div>
            <div className="p-6">
              <div className="text-sm text-blue-400 mb-2">May 8, 2025</div>
              <h2 className="text-xl font-bold mb-3">5 Ways AI Reduces Audit Costs</h2>
              <p className="text-white/70 mb-4">
                Discover how implementing AI-powered audit solutions can significantly reduce costs while improving accuracy.
              </p>
              <Button variant="link" className="text-blue-400 p-0">Read More →</Button>
            </div>
          </div>

          {/* Blog Post 3 */}
          <div className="bg-white/5 backdrop-blur-sm border border-white/20 rounded-2xl overflow-hidden">
            <div className="h-48 bg-gradient-to-br from-purple-600/20 to-pink-600/20">
              <div className="h-full w-full flex items-center justify-center">
                <div className="h-20 w-20 rounded-xl bg-gradient-to-br from-purple-600 to-pink-600 flex items-center justify-center text-white font-bold text-4xl">
                  📊
                </div>
              </div>
            </div>
            <div className="p-6">
              <div className="text-sm text-blue-400 mb-2">April 30, 2025</div>
              <h2 className="text-xl font-bold mb-3">Case Study: How PKF AI Saved 200+ Hours</h2>
              <p className="text-white/70 mb-4">
                Learn how a leading financial firm leveraged our AI solutions to save hundreds of audit hours.
              </p>
              <Button variant="link" className="text-blue-400 p-0">Read More →</Button>
            </div>
          </div>

          {/* Blog Post 4 */}
          <div className="bg-white/5 backdrop-blur-sm border border-white/20 rounded-2xl overflow-hidden">
            <div className="h-48 bg-gradient-to-br from-orange-600/20 to-red-600/20">
              <div className="h-full w-full flex items-center justify-center">
                <div className="h-20 w-20 rounded-xl bg-gradient-to-br from-orange-600 to-red-600 flex items-center justify-center text-white font-bold text-4xl">
                  🔒
                </div>
              </div>
            </div>
            <div className="p-6">
              <div className="text-sm text-blue-400 mb-2">April 22, 2025</div>
              <h2 className="text-xl font-bold mb-3">Security in AI Audit Platforms</h2>
              <p className="text-white/70 mb-4">
                Understanding the security measures that keep your financial data safe in AI-powered audit platforms.
              </p>
              <Button variant="link" className="text-blue-400 p-0">Read More →</Button>
            </div>
          </div>

          {/* Blog Post 5 */}
          <div className="bg-white/5 backdrop-blur-sm border border-white/20 rounded-2xl overflow-hidden">
            <div className="h-48 bg-gradient-to-br from-indigo-600/20 to-blue-600/20">
              <div className="h-full w-full flex items-center justify-center">
                <div className="h-20 w-20 rounded-xl bg-gradient-to-br from-indigo-600 to-blue-600 flex items-center justify-center text-white font-bold text-4xl">
                  📱
                </div>
              </div>
            </div>
            <div className="p-6">
              <div className="text-sm text-blue-400 mb-2">April 15, 2025</div>
              <h2 className="text-xl font-bold mb-3">Mobile Audit Tools: On-the-Go Analysis</h2>
              <p className="text-white/70 mb-4">
                How mobile audit capabilities are changing the way financial professionals work in the field.
              </p>
              <Button variant="link" className="text-blue-400 p-0">Read More →</Button>
            </div>
          </div>

          {/* Blog Post 6 */}
          <div className="bg-white/5 backdrop-blur-sm border border-white/20 rounded-2xl overflow-hidden">
            <div className="h-48 bg-gradient-to-br from-teal-600/20 to-green-600/20">
              <div className="h-full w-full flex items-center justify-center">
                <div className="h-20 w-20 rounded-xl bg-gradient-to-br from-teal-600 to-green-600 flex items-center justify-center text-white font-bold text-4xl">
                  🧠
                </div>
              </div>
            </div>
            <div className="p-6">
              <div className="text-sm text-blue-400 mb-2">April 8, 2025</div>
              <h2 className="text-xl font-bold mb-3">AI vs Human Auditors: The Perfect Blend</h2>
              <p className="text-white/70 mb-4">
                Why the future of auditing isn't about replacing humans, but enhancing their capabilities with AI.
              </p>
              <Button variant="link" className="text-blue-400 p-0">Read More →</Button>
            </div>
          </div>
        </div>

        {/* Newsletter Signup */}
        <div className="mt-20 bg-white/5 backdrop-blur-xl border border-white/20 rounded-3xl p-8 text-center">
          <h2 className="text-2xl font-bold mb-4">Subscribe to Our Newsletter</h2>
          <p className="text-white/70 mb-8 max-w-2xl mx-auto">
            Stay updated with the latest insights, news, and updates in AI audit technology delivered straight to your inbox.
          </p>
          <div className="flex max-w-md mx-auto">
            <input
              type="email"
              placeholder="Your email address"
              className="flex-1 bg-white/10 border border-white/20 rounded-l-xl px-4 py-3 text-white placeholder-white/50 focus:outline-none focus:border-blue-400"
            />
            <Button className="rounded-r-xl bg-gradient-to-r from-blue-600 to-purple-600">
              Subscribe
            </Button>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-slate-900 border-t border-white/10 py-8">
        <div className="w-full px-6 md:px-12 lg:px-20 text-center">
          <p className="text-white/60">© {new Date().getFullYear()} PKF Audit AI. All rights reserved.</p>
        </div>
      </footer>
    </div>
  );
};

export default Blog;
