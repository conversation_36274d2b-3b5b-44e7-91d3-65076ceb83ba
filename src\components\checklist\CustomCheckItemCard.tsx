import { But<PERSON> } from "@/components/ui/button";
import { CustomChecklistItem } from "@/lib/types/checklist";
import { checkTypeDefinitions, CustomCheckType } from '@/lib/types/CustomCheckTypeSystem';
import { CheckCircle, Edit, Trash2, Info } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface CustomCheckItemCardProps {
  item: CustomChecklistItem;
  onEdit: (item: CustomChecklistItem) => void;
  onDelete: (id: string) => void;
}

export const CustomCheckItemCard = ({ item, onEdit, onDelete }: CustomCheckItemCardProps) => {
  const checkTypeDef = checkTypeDefinitions[item.checkType as CustomCheckType];
  
  const formatDocumentType = (type: string) => {
    switch(type) {
      case 'auditReport': return 'Audit Report';
      case 'annexureA': return 'Annexure A - CARO';
      case 'annexureB': return 'Annexure B - IFC';
      case 'balanceSheet': return 'Balance Sheet';
      case 'notes': return 'Notes to Accounts';
      case 'plNotes': return 'P&L Notes';
      case 'annualReport': return 'Annual Report';
      case 'secretarialCompliance': return 'Secretarial Compliance Report';
      default: return type;
    }
  };

  const formatParameters = (params: Record<string, any> = {}) => {
    if (!checkTypeDef) return '';
    
    return checkTypeDef.parameters
      .map(param => {
        const value = params[param.name];
        if (value === undefined) return null;
        
        let formattedValue = '';
        switch (param.type) {
          case 'boolean':
            formattedValue = value ? 'Yes' : 'No';
            break;
          case 'array':
            formattedValue = Array.isArray(value) ? value.join(', ') : value;
            break;
          default:
            formattedValue = value.toString();
        }
        
        return `${param.name}: ${formattedValue}`;
      })
      .filter(Boolean)
      .join(', ');
  };

  return (
    <li className="checklist-item flex items-start justify-between bg-white p-4 rounded-lg shadow-sm border">
      <div className="flex items-start flex-1">
        <CheckCircle className="h-4 w-4 text-green-500 mt-1 mr-2 flex-shrink-0" />
        <div className="flex-1">
          <div className="flex items-center">
            <strong className="mr-2">{item.name}</strong>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger>
                  <Info className="h-4 w-4 text-gray-400" />
                </TooltipTrigger>
                <TooltipContent>
                  {checkTypeDef?.description || item.checkType}
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
          <p className="text-gray-600">{item.description}</p>
          <div className="mt-1 text-sm text-gray-500">
            <span className="inline-block bg-blue-100 text-blue-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded">
              {item.type === 'single' ? 'Single Document Check' : 'Multi-Document Check'}
            </span>
            <span className="inline-block bg-purple-100 text-purple-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded">
              {checkTypeDef?.description || item.checkType}
            </span>
          </div>
          <div className="mt-1 text-sm text-gray-500">
            Documents: {item.documentTypes.map(formatDocumentType).join(", ")}
          </div>
          {Object.keys(item.parameters || {}).length > 0 && (
            <div className="mt-1 text-sm text-gray-500">
              Parameters: {formatParameters(item.parameters)}
            </div>
          )}
        </div>
      </div>
      <div className="flex items-center gap-2 ml-4">
        <Button
          variant="ghost"
          size="sm"
          className="text-gray-600 hover:text-blue-600"
          onClick={() => onEdit(item)}
        >
          <Edit className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          className="text-gray-600 hover:text-red-600"
          onClick={() => onDelete(item.id)}
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>
    </li>
  );
};
