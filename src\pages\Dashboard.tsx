
import React from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { BarChart3, FileText, History, Upload, ArrowUpRight, PlusCircle, Clock } from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { getAnalysisHistory } from "@/lib/storageService";
import { format } from "date-fns";
import { CheckResult } from "@/lib/googleAI";

const Dashboard = () => {
  const { currentUser } = useAuth();
  const navigate = useNavigate();

  const { data: analysisHistory, isLoading } = useQuery({
    queryKey: ["analysisHistory", currentUser?.uid],
    queryFn: () => (currentUser ? getAnalysisHistory(currentUser.uid) : Promise.resolve([])),
    enabled: !!currentUser,
  });

  const handleAnalyzerClick = () => {
    navigate("/dashboard/analyzer");
  };

  const handleViewDetails = (id: string) => {
    navigate(`/dashboard/analysis/${id}`);
  };

  // Function to determine status badge color
  const getStatusColor = (compliant: number, total: number) => {
    const ratio = compliant / total;
    if (ratio === 1) return "bg-green-500";
    if (ratio >= 0.75) return "bg-yellow-500";
    return "bg-red-500";
  };

  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Dashboard</h1>
      </div>

      {/* Welcome Card with Animation */}
      <Card className="border-0 shadow-lg bg-primary text-white overflow-hidden relative animate-scale-in">
        <CardContent className="p-8 relative z-10">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-6">
            <div>
              <h2 className="text-2xl font-semibold mb-2">
                Welcome back, {currentUser?.displayName || "User"}
              </h2>
              <p className="text-white/90 max-w-md">
                Let's analyze your audit reports for compliance and disclosures. Upload new documents or review your previous analyses.
              </p>
            </div>
            <Button
              size="lg"
              onClick={handleAnalyzerClick}
              className="bg-white hover:bg-white/90 text-primary font-medium rounded-full"
            >
              <Upload className="mr-2 h-4 w-4" />
              Start New Analysis
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Quick Stats with Improved Visuals */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="dashboard-card group hover:border-primary/30">
          <div className="flex items-start justify-between">
            <div>
              <p className="text-gray-500 mb-1">Total Analyses</p>
              <h3 className="text-3xl font-bold">{analysisHistory?.length || 0}</h3>
            </div>
            <div className="bg-primary/10 p-3 rounded-full group-hover:bg-primary/20 transition-colors">
              <BarChart3 className="h-6 w-6 text-primary" />
            </div>
          </div>
        </Card>

        <Card className="dashboard-card group hover:border-primary/30">
          <div className="flex items-start justify-between">
            <div>
              <p className="text-gray-500 mb-1">Documents Analyzed</p>
              <h3 className="text-3xl font-bold">
                {analysisHistory?.reduce(
                  (total, item) =>
                    total +
                    (item.documents.audit_report ? 1 : 0) +
                    (item.documents.annexure_a ? 1 : 0) +
                    (item.documents.annexure_b ? 1 : 0),
                  0
                ) || 0}
              </h3>
            </div>
            <div className="bg-primary/10 p-3 rounded-full group-hover:bg-primary/20 transition-colors">
              <FileText className="h-6 w-6 text-primary" />
            </div>
          </div>
        </Card>

        <Card className="dashboard-card group hover:border-primary/30">
          <div className="flex items-start justify-between">
            <div>
              <p className="text-gray-500 mb-1">Recent Analysis</p>
              <h3 className="text-3xl font-bold">
                {isLoading
                  ? "..."
                  : analysisHistory && analysisHistory.length > 0
                    ? format(new Date(analysisHistory[0].timestamp), "MMM d")
                    : "None"}
              </h3>
            </div>
            <div className="bg-primary/10 p-3 rounded-full group-hover:bg-primary/20 transition-colors">
              <History className="h-6 w-6 text-primary" />
            </div>
          </div>
        </Card>
      </div>

      {/* Recent Analyses with Enhanced Table */}
      <Card className="border-0 shadow-lg">
        <CardHeader className="bg-gray-50 border-b pb-6">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Recent Analyses</CardTitle>
              <CardDescription>
                Your most recent audit report analyses
              </CardDescription>
            </div>
            {analysisHistory && analysisHistory.length > 0 && (
              <Button
                variant="outline"
                onClick={() => navigate("/dashboard/history")}
                className="flex items-center"
              >
                View All <ArrowUpRight className="h-4 w-4 ml-1" />
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent className="p-0">
          {isLoading ? (
            <div className="text-center py-16 space-y-4">
              <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full mx-auto"></div>
              <p className="text-gray-500">Loading your analysis history...</p>
            </div>
          ) : analysisHistory && analysisHistory.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr>
                    <th className="bg-gray-50 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company</th>
                    <th className="bg-gray-50 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                    <th className="bg-gray-50 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Report Type</th>
                    <th className="bg-gray-50 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th className="bg-gray-50 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 bg-white">
                  {analysisHistory.slice(0, 5).map((analysis) => {
                    // Count compliant checks
                    // Use fixed total of 50 checks
                    const totalChecks = 50;
                    const compliantChecks = Object.values(analysis.results as Record<string, CheckResult>).filter(
                      (result) => result.isCompliant
                    ).length;

                    return (
                      <tr key={analysis.id} className="hover:bg-gray-50 transition-colors">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="font-medium text-gray-900">{analysis.company_name}</div>
                          <div className="text-sm text-gray-500 flex items-center">
                            <Clock className="h-3 w-3 mr-1" />
                            {format(new Date(analysis.timestamp), "h:mm a")}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {format(new Date(analysis.timestamp), "MMM d, yyyy")}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <span className="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                            {analysis.parameters.audit_report_type}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div
                              className={`h-2.5 w-2.5 rounded-full mr-2 ${getStatusColor(compliantChecks, totalChecks)}`}
                            ></div>
                            <span className="text-sm">
                              {compliantChecks}/{totalChecks} checks passed
                            </span>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleViewDetails(analysis.id)}
                            className="hover:bg-gray-100 hover:text-gray-900"
                          >
                            View Details
                          </Button>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-16 space-y-4">
              <div className="bg-gray-50 rounded-full w-16 h-16 flex items-center justify-center mx-auto text-gray-400">
                <PlusCircle className="h-8 w-8" />
              </div>
              <div>
                <p className="text-lg font-medium text-gray-900">No analysis history found</p>
                <p className="text-gray-500 mt-1">Start by analyzing an audit report.</p>
              </div>
              <Button
                onClick={handleAnalyzerClick}
                className="mt-2 bg-primary hover:bg-primary/90"
              >
                <Upload className="mr-2 h-4 w-4" />
                New Analysis
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default Dashboard;
