{
  "rules": {
    ".read": false,
    ".write": false,
    
    "users": {
      "$uid": {
        // Allow users to read and write only their own data
        ".read": "$uid === auth.uid",
        ".write": "$uid === auth.uid"
      }
    },
    
    "documents": {
      "$uid": {
        // Allow users to read and write only their own documents
        ".read": "$uid === auth.uid",
        ".write": "$uid === auth.uid",
        "$documentId": {
          ".validate": "newData.hasChildren(['name', 'type', 'url', 'path', 'uploadedAt', 'status'])"
        }
      }
    },
    
    "analysis_results": {
      "$uid": {
        // Allow users to read and write only their own analysis results
        ".read": "$uid === auth.uid",
        ".write": "$uid === auth.uid"
      }
    },
    
    "companies": {
      "$uid": {
        // Allow users to read and write only their own companies
        ".read": "$uid === auth.uid",
        ".write": "$uid === auth.uid"
      }
    },
    
    "partners": {
      "$uid": {
        // Allow users to read and write only their own partners
        ".read": "$uid === auth.uid",
        ".write": "$uid === auth.uid"
      }
    },
    
    "firm_settings": {
      "$uid": {
        // Allow users to read and write only their own firm settings
        ".read": "$uid === auth.uid",
        ".write": "$uid === auth.uid"
      }
    }
  }
}
