/**
 * Result of a compliance check run on a document
 */
export interface CheckResult {
  isCompliant: boolean;
  explanation: string;
  confidence?: number;
  extractedData?: any;
  detail?: string;
  evidence?: string;
  source?: string;
}

// Gemini API key
const API_KEY = "AIzaSyA2-3gPSsIMLH3ZVqQ6vmLl5STSYRrmAT0"; // Replace with your actual API key

// Cache for PDF base64 data to avoid repeated conversions
const pdfCache = new Map<string, string>();

/**
 * Convert an ArrayBuffer to a Base64 string in a browser-compatible way
 * This replaces the Node.js Buffer.from() method which isn't available in browsers
 */
function arrayBufferToBase64(buffer: ArrayBuffer): string {
  const bytes = new Uint8Array(buffer);
  let binary = '';
  const len = bytes.byteLength;
  for (let i = 0; i < len; i++) {
    binary += String.fromCharCode(bytes[i]);
  }
  return window.btoa(binary);
}

/**
 * Process a PDF file directly with Gemini for a specific check
 */
export const processPdfWithGemini = async (
  pdfFile: File,
  checkType: string,
  parameters?: Record<string, any>
): Promise<CheckResult> => {
  try {
    console.log(`Processing ${checkType} check with Gemini API`);

    // Create a cache key based on the file name and size
    const cacheKey = `${pdfFile.name}_${pdfFile.size}`;

    // Check if we already have the PDF in cache
    let pdfBase64: string;
    if (pdfCache.has(cacheKey)) {
      console.log(`Using cached PDF data for ${pdfFile.name}`);
      pdfBase64 = pdfCache.get(cacheKey)!;
    } else {
      // Get the PDF content as an ArrayBuffer
      const pdfArrayBuffer = await pdfFile.arrayBuffer();

      // Convert ArrayBuffer to Base64 using browser-compatible method
      pdfBase64 = arrayBufferToBase64(pdfArrayBuffer);

      // Store in cache for future use
      pdfCache.set(cacheKey, pdfBase64);
      console.log(`Cached PDF data for ${pdfFile.name}`);
    }

    // Create prompt based on check type
    const prompt = createPromptForCheckType(checkType, parameters);

    // Add retry logic for API calls
    const maxRetries = 3;
    let retryCount = 0;
    let response: Response | undefined;

    while (retryCount < maxRetries) {
      try {
        // Use a direct fetch approach to the Gemini API with timeout
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

        response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro:generateContent?key=${API_KEY}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            contents: [{
              parts: [
                { text: prompt },
                {
                  inline_data: {
                    mime_type: "application/pdf",
                    data: pdfBase64
                  }
                }
              ]
            }],
            generation_config: {
              temperature: 0.1,
              top_k: 32,
              top_p: 0.95,
              max_output_tokens: 8192,
            }
          }),
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (response.ok) {
          break; // Success, exit retry loop
        } else {
          throw new Error(`Gemini API error: ${response.status} ${response.statusText}`);
        }
      } catch (error) {
        retryCount++;
        console.warn(`Retry ${retryCount}/${maxRetries} for ${checkType} due to: ${error}`);

        if (retryCount >= maxRetries) {
          throw error; // Max retries reached, propagate the error
        }

        // Exponential backoff
        await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, retryCount)));
      }
    }

    if (!response || !response.ok) {
      throw new Error(`Failed to get response from Gemini API after ${maxRetries} attempts`);
    }

    let data: any;
    try {
      data = await response.json();

      // Check if the response has the expected structure
      if (!data.candidates || !data.candidates[0] || !data.candidates[0].content || !data.candidates[0].content.parts || !data.candidates[0].content.parts[0].text) {
        console.error("Unexpected Gemini API response structure:", data);
        throw new Error("Unexpected response format from Gemini API");
      }
    } catch (error) {
      console.error("Error parsing Gemini API response:", error);
      throw new Error(`Failed to parse Gemini API response: ${error instanceof Error ? error.message : String(error)}`);
    }

    // Extract the response text
    const responseText = data.candidates[0].content.parts[0].text;

    // Parse the response
    const lines = responseText.split('\n');
    const firstLine = lines[0].trim().toLowerCase();
    const explanation = lines.slice(1).join('\n').trim();

    // Check if the response matches expected format - "yes" for compliant
    const isCompliant = firstLine === 'yes';

    // Extract additional data where available
    let extractedData = null;

    if (checkType === "bs_current_liab_vs_asset" && lines.length >= 3) {
      try {
        const liabilities = parseFloat(lines[0].trim());
        const assets = parseFloat(lines[1].trim());
        extractedData = { liabilities, assets };
      } catch (e) {
        console.warn("Failed to parse numeric values from response", e);
      }
    } else if (checkType === "balance_sheet_inventory_value" && lines.length >= 2) {
      try {
        const value = parseFloat(lines[0].replace(/[^0-9.]/g, ''));
        const source = lines[1];
        extractedData = { value, source };
      } catch (e) {
        console.warn("Failed to parse inventory value", e);
      }
    } else if (checkType === "caro_clause_count" && lines.length >= 4) {
      try {
        extractedData = {
          applicable: lines[1].replace("Applicable:", "").trim(),
          notApplicable: lines[2].replace("Not Applicable:", "").trim(),
          total: lines[3].replace("Total:", "").trim()
        };
      } catch (e) {
        console.warn("Failed to parse CARO clause counts", e);
      }
    }

    // Ensure all properties are defined (not undefined) to prevent Firebase errors
    return {
      isCompliant,
      explanation,
      confidence: 0.9,
      extractedData: extractedData || null,
      detail: lines.length > 2 ? lines.slice(2).join('\n').trim() : ""
    };

  } catch (error) {
    console.error(`Error processing PDF with Gemini: ${error}`);

    // Return a fallback result with all properties defined (not undefined)
    return {
      isCompliant: false,
      explanation: `Error processing with Gemini API: ${error instanceof Error ? error.message : String(error)}`,
      confidence: 0.5,
      extractedData: null,
      detail: ""
    };
  }
};

/**
 * Create a prompt for a specific check type
 */
function createPromptForCheckType(checkType: string, parameters?: Record<string, any>): string {
  switch (checkType) {
    case "company_format":
      const companyName = parameters?.company_name || "";
      return `
        Analyze this audit report PDF. Check if there is EXACTLY the text "To the Members of ${companyName}"
        below the Title and above the Opinion Section. It should match word-for-word, including spaces.

        Return ONLY:
        - First line: "Yes" or "No" (indicating if the exact text is found)
        - Additional explanation of what you found or why it doesn't match
      `;

    case "profit_loss":
      const profitOrLoss = parameters?.profit_or_loss || "Profit";
      return `
        Analyze this audit report PDF. Check if the document mentions "${profitOrLoss.toLowerCase()}"
        in the opinion section, consistent with the company's financial performance.

        Return ONLY:
        - First line: "Yes" or "No" (indicating if the mention is consistent)
        - Additional explanation of what you found
      `;

    case "brsr_brr":
      const isTop1000or500 = parameters?.top_1000_or_500 === "Yes";
      if (isTop1000or500) {
        return `
          You are analyzing an audit report PDF. The user indicates the company IS among top 1000 or top 500 listed.
          Therefore, we expect "Business Responsibility and Sustainability Report (BRSR)" — NOT plain "business responsibility report".

          ***STRICT REQUIREMENTS***:
          1) Look specifically in the 'Information Other than the financial statements' section (case-insensitive search).
          2) If you find either:
             - The exact phrase "Business Responsibility and Sustainability Report"
             - OR the acronym "BRSR"
             respond with:
                - First line: "Yes"
                - Second line: A brief snippet or explanation
          3) If you instead find "business responsibility report" (BRR) without "sustainability",
             OR you do not find BRSR at all, respond:
                - First line: "No"
                - Second line: Explanation
          4) If both BRSR and BRR are present, respond "No" because that's inconsistent.

          Return ONLY:
          - The first line: "Yes" or "No"
          - The second line: short reason.
        `;
      } else {
        return `
          You are analyzing an audit report PDF. The user indicates the company is NOT among top 1000 or top 500 listed.
          Therefore, we expect the EXACT phrase "business responsibility report" (BRR) — NOT BRSR.

          ***STRICT REQUIREMENTS***:
          1) Look specifically in the 'Information Other than the financial statements' section (case-insensitive search).
          2) If you find "Business Responsibility and Sustainability Report" or "BRSR" anywhere, respond:
                - First line: "No"
                - Second line: Explanation (because that's not acceptable for this scenario)
          3) If you find the exact phrase "business responsibility report" (three words consecutively),
             respond "Yes" plus a brief snippet.
          4) If you do NOT find "business responsibility report," respond "No" with an explanation.

          Return ONLY:
          - The first line: "Yes" or "No"
          - The second line: short reason.
        `;
      }

    case "signature_date":
      const auditDate = parameters?.audit_date ? new Date(parameters.audit_date) : null;

      // Format for special ordinal dates (like 3rd, 23rd)
      const getOrdinalDate = (date: Date) => {
        const day = date.getDate();
        const suffix = ['th', 'st', 'nd', 'rd'][day % 10 > 3 ? 0 : day % 100 - day % 10 != 10 ? day % 10 : 0];
        const month = date.toLocaleDateString('en-GB', { month: 'long' });
        const year = date.getFullYear();
        return `${day}${suffix} ${month} ${year}`;
      };

      // Create multiple date format variations to increase matching chances
      const ordinalDateStr = auditDate ? getOrdinalDate(auditDate) : "23rd May 2023";
      const standardDateStr = auditDate ? auditDate.toLocaleDateString('en-US', { day: 'numeric', month: 'long', year: 'numeric' }) : "May 23, 2023";
      const numericDateStr = auditDate ? auditDate.toLocaleDateString('en-GB', { day: 'numeric', month: 'numeric', year: 'numeric' }) : "23/05/2023";

      return `
        Analyze this audit report PDF to check if the PKF Sridhar & Santhanam LLP signature block contains the date that matches the audit date.

        1. Locate the signature block containing:
           - "For PKF Sridhar & Santhanam LLP"
           - "Chartered Accountants"
           - "Firm's Registration No.003990S/S200018"
           - Partner name, membership number, and UDIN

        2. Extract the complete signature block information, including:
           - Firm name
           - Registration number
           - Partner name and designation
           - Membership number
           - UDIN number
           - Place
           - Date

        3. Format all extracted information as HTML, with each part on its own line with <b> tags for labels and normal text for values,
           followed by <br> tags.

        4. Check if the date mentioned after "Date:" matches ANY of these equivalent formats:
           - ${ordinalDateStr} (e.g., "23rd May 2023")
           - ${standardDateStr} (e.g., "May 23, 2023")
           - ${numericDateStr} (e.g., "23/05/2023")

        5. Be flexible with date formats - the day and month should match, but the format might vary.
           For example, "23rd May 2023", "23 May 2023", "May 23, 2023", "23/05/2023" should all be considered equivalent.

        Return:
        - First line: "Yes" or "No" (indicating if the date is correct)
        - Second line onwards: The formatted signature block with each piece of information on its own line, formatted with HTML tags
          For example:
          <b>Firm:</b> PKF Sridhar & Santhanam LLP<br>
          <b>Registration No:</b> 003990S/S200018<br>
          etc.
      `;

    case "financial_statements_type":
      const reportType = parameters?.report_type || "Normal";
      if (reportType === "Normal") {
        return `
          Analyze this audit report PDF. For Normal type audit reports, no specific check is needed.

          Return ONLY:
          - First line: "Yes" (as Normal reports don't need special validation)
          - Then a simple explanation that no specific check is required for Normal report type
        `;
      } else {
        return `
          I need you to perform a CTRL+F search for all instances of "financial statements" in this document:

          1. Find every instance of the phrase "financial statements" in the document (case-insensitive)
          2. For each instance, check whether "${reportType.toLowerCase()}" directly precedes it
          3. Count how many instances have the proper qualifier and how many don't
          4. For instances missing the qualifier, extract the context (5-10 words before and after)

          Return:
          - First line: "Yes" if ALL instances are properly qualified, "No" if ANY are not
          - Second line: "Found X out of Y instances properly qualified with '${reportType.toLowerCase()}'"
          - For unqualified instances, list the context with the phrase "financial statements"
          - Brief explanation of your assessment
        `;
      }

    case "opinion_type_consistency":
      const opinionType = parameters?.opinion_type || "Unmodified";
      if (opinionType.toLowerCase() === "unmodified" || opinionType.toLowerCase() === "unqualified") {
        return `
          Analyze this audit report PDF. For Unmodified/Unqualified opinion types, no specific labeling check is needed.

          Return ONLY:
          - First line: "Yes" (as Unmodified opinions don't need special labeling)
          - Then a simple explanation that unmodified opinions don't require specific labeling
        `;
      } else {
        return `
          Analyze this audit report PDF to check if the opinion is consistently labeled as "${opinionType} opinion" throughout the document.

          1. Find all occurrences of the word "opinion" in relevant sections (excluding headers, auditor responsibilities, etc.)
          2. Check if the opinion is correctly labeled as "${opinionType} opinion" in each relevant instance.

          Return ONLY:
          - First line: "Yes" or "No" (indicating if the labeling is consistent)
          - Then explain what you found, including relevant text extracts
        `;
      }

    case "company_name_consistency":
      const companyNameForConsistency = parameters?.company_name || "";
      const baseName = companyNameForConsistency.split(" ")[0] || "";
      return `
        Analyze this audit report PDF to check if the company name "${companyNameForConsistency}" is used consistently throughout the document.

        1. Identify all instances where the base name "${baseName}" appears as part of a company name.
        2. Compare these mentions against the expected full name "${companyNameForConsistency}".
        3. Check for variations, different subsidiary names, or inconsistent usage.
        4. Generate a list of all variations found and categorize them as follows:
           - If the name appears EXACTLY as "${companyNameForConsistency}" mark it as "Compliant"
           - If the name appears with DIFFERENT wording (missing words, extra words, different order) mark it as "Non-Compliant"
           - For each non-compliant variation, provide the exact text and the context where it was found.

        Return:
        - First line: "Yes" or "No" (indicating if the company name is consistent)
        - Second line: The total count of company name mentions
        - Third line: The number of variations found
        - Then list ALL non-compliant variations found, formatted like:
          "VARIATION: [exact text found]"
          "CONTEXT: [surrounding sentence or paragraph]"
        - Finally, briefly summarize the issues found
      `;

    case "caro_interlink_ar":
      return `
        You are given an Audit Report PDF. Locate the subheading "Report on Other Legal and Regulatory Requirements".
        Under this subheading, identify the paragraph number (e.g. '1.', '2.', or '(i)') that references
        "Companies (Auditors' Report) Order, 2020" or "CARO" (case-insensitive).

        Steps to follow:
        1) Extract text from that subheading onward.
        2) Find the paragraph that mentions 'Companies (Auditors' Report) Order, 2020' (or 'CARO').
        3) Return ONLY the paragraph number like: "Paragraph: 1"
           If not found, return "Paragraph: Not Found"

        Output format:
        - First line: "Yes"
        - Second line onward: "Paragraph: <number>" or "Paragraph: Not Found"
      `;

    case "caro_interlink_annexure":
      return `
        You are given the CARO Annexure PDF. Look for the heading "Annexure A" (case-insensitive).
        Right below that heading, it should say something like:
            "Referred to in paragraph X on 'Report on Other Legal and Regulatory Requirements'..."

        Steps:
        1) Identify the heading "Annexure A".
        2) Find the line that says "Referred to in paragraph X on 'Report on Other Legal and Regulatory Requirements'..."
        3) Return ONLY that X (the paragraph number), e.g. "Paragraph: 2"
           If not found, return "Paragraph: Not Found"

        Output format:
        - First line: "Yes"
        - Second line onward: "Paragraph: <number>" or "Paragraph: Not Found"
      `;

    case "ifc_interlink_ar":
      return `
        You are given an Audit Report PDF. Under the subheading "Report on Other Legal and Regulatory Requirements",
        find the paragraph (e.g. '2(g)' or '3(b)') that references:
          "adequacy of the internal financial controls with reference to the standalone financial statements"
          AND mentions "Annexure B".

        Steps:
        1) Extract text from that subheading onward.
        2) Identify the line referencing both "internal financial controls" and "Annexure B".
        3) Return ONLY the paragraph identifier in the format: "Paragraph: 2(g)"
           If not found, return "Paragraph: Not Found"

        Output format:
        - First line: "Yes"
        - Then: "Paragraph: <para_id>" or "Paragraph: Not Found"
      `;

    case "ifc_interlink_annexure":
      return `
        You are given the Annexure B PDF. Look for the heading "Annexure B" (case-insensitive).
        Right below that heading, you should see something like:
           "Referred to in paragraph 2(g) on 'Report on Other Legal and Regulatory Requirements'..."

        Steps:
        1) Identify the heading "Annexure B".
        2) Find the exact text "Referred to in paragraph X on 'Report on Other Legal and Regulatory Requirements'..."
           (where X might be '2(g)', '3(b)', etc.)
        3) Return ONLY that X in the format: "Paragraph: 2(g)"
           If not found, return "Paragraph: Not Found"

        Output format:
        - First line: "Yes"
        - Then: "Paragraph: <para_id>" or "Paragraph: Not Found"
      `;

    case "caro_clause_count":
      return `
        You are given a CARO Annexure PDF (stand‑alone).
        TASK A– Count all clause headings actually discussed.
        • They appear as roman numerals "(i)", "(ii)", … "(xx)".

        TASK B–Locate any **separate paragraph** that lists clauses declared
        "not applicable / not relevant". Extract those clause numbers.

        FORMAT (STRICT):
        Line 1 → "Yes" or "No" (Yes if COUNT(applicable)+COUNT(not applicable) ==20)
        Line 2 → "Applicable: (i),(ii)…."
        Line 3 → "Not Applicable: (viii)…."
        Line 4 → "Total: <N> of 20"
        Line 5 → Short explanation
      `;

    case "caro_clause_xxi":
      return `
        You are given a CARO Annexure PDF (consolidated).
        Search the entire document (case‑insensitive) for the clause heading "(xxi)".

        Return STRICTLY:
        Line 1  →  "Yes"  if **exactly one** occurrence of "(xxi)" is found
                    "No"   if none or more than one are found
        Line 2  →  The sentence (or heading line) where "(xxi)" appears, or
                   "Not found" if answer is No
      `;

    case "ar_dispute_immovable":
      return `
        You're given an Audit Report PDF.
        Search for any mention of disputes, litigation, conflict or legal proceedings
        related to immovable properties (land, buildings, ROU assets, investment property).
        If found, return exactly two lines:

            YES
            <the sentence or paragraph containing the dispute>

        Otherwise, return:

            NO
            None
      `;

    case "bs_has_immovable_property":
      return `
        Search this PDF (Balance-Sheet or Notes) for rows/lines whose description
        contains any of these keywords (ignore case):

        • "leasehold land"
        • "ROU"  OR  "right of use"
        • "freehold land"
        • "buildings"
        • "investment property"

        If ANY of these appear in the current-year column with a non-zero amount,
        output:

        Line 1 → YES
        Line 2 → the first matching line you found.

        If none appear or all are zero/blank, output:

        Line 1 → NO
        Line 2 → None
      `;

    case "balance_sheet_ppe_value":
      return `
        You are given the Balance Sheet PDF.
        Locate the line (or table row) for "Property, Plant and Equipment" (ignore case).
        Extract the **numeric amount** reported for the current year (ignore the previous‑year column).

        Return STRICTLY in two lines:
        Line 1 → the amount as a plain number (no commas, no currency, no words)
        Line 2 → the exact line you extracted it from.
      `;

    case "balance_sheet_intangible_value":
      return `
        You're given a Balance-Sheet PDF with two side-by-side columns:
        • one headed "As at March 31, 2024"  (current year)
        • one headed "As at March 31, 2023"  (prior year)

        TASK:
        1. Locate the row whose description is exactly "Intangible assets"
        (ignore case), not "under development" or anything else.
        2. Read the two numbers in that row: the first belongs under the
        2024 header, the second under 2023.
        3. RETURN EXACTLY two lines, labeled:
            Line 1 → CURRENT_YEAR: <2024 amount as plain number>
            Line 2 → PRIOR_YEAR:   <2023 amount as plain number>
        (No commas, symbols or extra words.)

        If the row or one of the year-columns is missing, return 0 for that year.
      `;

    case "balance_sheet_inventory_value":
      return `
        You are analysing a Balance-Sheet PDF that has two numeric columns:
        • current year  (e.g. "As at 31 March 2024")
        • prior year    (e.g. "As at 31 March 2023")

        GOAL → current-year **Inventories** amount.

        VERY STRICT RULES
        ─────────────────
        • Accept **only** a row whose description (ignoring case/spacing) is
        exactly the single word "Inventories" OR starts with "(a) Inventories".
        • Do NOT accept rows that contain "Other", "Trade receivables",
        "Bank", "Current Assets", "Financial Assets", etc.
        • If no such row exists, the amount is **0**.

        WHAT TO RETURN (exactly two lines)
        Line 1 → <amount>   ← plain number; 0 if the row is missing
        Line 2 → <row text> ← the row you used, or "None" if missing
      `;

    case "bs_secured_borrowings_gt5cr":
      return `
        Look under the CURRENT LIABILITIES section for a line that contains
        both words "secured" and "borrowings" OR "working capital loan"
        (ignore case). Read the amount in the current-year column
        ("As at 31 March 2024").

        Return STRICTLY two lines:
        Line 1 → YES  if the amount is greater than 5 crore, else NO
        Line 2 → the row text with the amount (or "None")
      `;

    case "notes_has_new_invest_loan_guar":
      return `
        You are reading the Investments / Loans / Guarantees note that has
        TWO numeric columns (₹ crore):

             • Column-1  = closing balance for the CURRENT year (FY-24)
             • Column-2  = comparative balance for the PREVIOUS year (FY-23)

        A row proves a **NEW** transaction ONLY when:

             ①  Current-year amount > 0
             ②  Previous-year amount is exactly 0 (or blank / "–")

        Accept a row only if its description contains at least one of:
            loan, advance in the nature of loan,
            financial guarantee, security provided, investment

        Ignore a row completely if the description contains any of:
            mutual fund, CWIP, PPE, trade, receivable, payable,
            FVTOCI, FVOCI, equity, share, previous year,
            security deposit, deposit

        Output EXACTLY two lines:

            YES
            <first qualifying row verbatim>

        — or —

            NO
            None
      `;

    case "caro_inventory_clause":
      return `
        You're given a CARO Annexure PDF (Annexure A).

        TASK:
        1. First locate the top‐level clause marked "(ii)".
        2. Immediately after that, find the next line that begins with "a)" (or "(a)")
        whose text mentions "inventor" (to catch "inventory" or "stock").
        3. If you find such a sub‐clause, return:
            Line 1 → Yes
            Line 2 → the full text of that "a) …" line.
        4. Otherwise, return:
            Line 1 → No
            Line 2 → Missing
      `;

    case "caro_inventory_clause_covers_transit":
      return `
        Extract clause "(ii)(a)" from the CARO Annexure and tell whether it
        explicitly covers
        • "goods in transit"  or  "stock in transit"  OR
        • "third parties" (as in stocks with third parties).
        Return two lines:
        Line 1 → Yes  if any of those phrases appear, else No
        Line 2 → the clause text (or "Missing")
      `;

    case "caro_inventory_clause_iib":
      return `
        Extract clause "(ii)(b)" from the CARO Annexure OR any paragraph that
        mentions BOTH "quarterly return" and "inventory" (ignore case).

        Return two lines:
        Line 1 → Yes  if such wording present, else No
        Line 2 → the sentence / heading found (or "Missing")
      `;

    case "caro_ppe_clause":
      return `
        You are given a CARO Annexure PDF.
        Confirm BOTH of these sub‑headings exist exactly (ignore case of roman numerals):
            1. "(i)(a)(A)"  — about Maintenance of Records
            2. "(i)(b)"     — about Physical Verification

        Return STRICTLY:
        Line 1 → "Yes" if **both** sub‑headings found exactly once, else "No".
        Line 2 → if Yes ⇒ "Both sub‑headings present".  If No ⇒ list which one(s) missing.
      `;

    case "notes_has_goods_in_transit":
      return `
        Search every inventory-related note for the phrases
        • "goods in transit"
        • "goods-in-transit"
        • "stock in transit"
        • "stock-in-transit"
        (ignore case, underscore, hyphen).

        If any phrase is found in the 2024 column (even inside brackets),
        return exactly two lines:

            YES
            <the sentence or table row that contains the phrase>

        Otherwise:

            NO
            None
      `;

    case "caro_title_deed_clause":
      return `
        Scan the CARO Annexure for the sub-clause labelled "(i)(c)" or any line that
        mentions BOTH words "title" and "deed" (ignore case).

        Return STRICTLY:
        Line 1 → Yes   if found exactly once
                    No   if not found
        Line 2 → the sentence or heading where it appears, or "Missing"
      `;

    case "bs_has_revaluation":
      return `
        Determine whether the company recorded any **revaluation** in the current
        year (31 Mar 2024).

        Treat as YES if EITHER:
        • PPE / Intangible / ROU note shows an addition or deletion line that
            contains the word "revaluation", OR
        • Reserves & Surplus / Other-equity note shows a movement labelled
            "Revaluation reserve" that increased or decreased during the year.

        Return STRICTLY two lines:
        Line 1 → YES  or  NO
        Line 2 → the first matching line you found, or "None"
      `;

    case "caro_revaluation_clause":
      return `
        Scan the CARO Annexure for sub-clause "(i)(d)" OR any paragraph that
        contains both words "revalued" and "property" (ignore case).

        Return STRICTLY two lines:
        Line 1 → Yes  if found, else  No
        Line 2 → the sentence / heading where it appears, or "Missing"
      `;

    case "caro_clause_iii_present":
      return `
        Extract clause "(iii)" from the CARO Annexure or any paragraph that
        contains at least one of the words "investment", "loan", "guarantee",
        or "security" (ignore case).

        Return exactly two lines:
        Line 1 → Yes  if such text is present, else No
        Line 2 → the clause text (or "Missing")
      `;

    case "notes_rpt_loans_guar_total":
      return `
        In the Related-Party-Transactions (RPT) note, capture the **closing
        balance for the most recent financial year** of items that are
        *financing* in nature:

        • rows whose description contains
            "loan", "advance", "in the nature of loan",
            "financial guarantee", "security provided"
        • AND the counter-party is a subsidiary, associate, or joint venture.

        EXCLUDE rows that clearly refer to operating or equity items, i.e.
        any description containing
        "trade", "purchase", "sale", "receivable", "payable",
        "interest", "dividend", "share", "equity", "FVOCI", "capital".

        Sum only the qualifying closing-balance amounts.

        Return EXACTLY two lines:
        TOTAL: <number>
        Details: desc1=<amt1>; desc2=<amt2>; …  or "None"
      `;

    case "caro_iii_aA_balance":
      return `
        Extract sub-clause "(iii)(a)(A)" from the CARO Annexure and
        return the number that represents "balance outstanding at the
        balance-sheet date" (in ₹ crore).

        Return two lines:
            BALANCE: <number>
            <clause text or 'Missing'>
      `;

    case "notes_contg_fin_guar_total":
      return `
        In the Contingent Liabilities note, locate any line that contains the
        phrase "financial guarantee" or "guarantees given" (ignore case).
        Sum the current-year (31 Mar 2024) amounts for all such lines.

        OUTPUT exactly two lines:
        Line 1 → TOTAL: <plain number>            (0 if none)
        Line 2 → Details: desc1=<amt1>; desc2=<amt2>; …  or "None"
      `;

    case "caro_iii_a_guar_total":
      return `
        Extract both sub-clauses (iii)(a)(A) and (iii)(a)(B) from the CARO
        Annexure, then add their numeric amounts.

        OUTPUT two lines:
        Line 1 → TOTAL: <plain number>
        Line 2 → (A)=<amtA>; (B)=<amtB>  or "Missing"
      `;

    case "caro_iii_b_to_e_present":
      return `
        Extract the text of clause "(iii)" from the CARO Annexure.
        Confirm that sub-clauses (b), (c), (d) and (e) are **all** present.

        Return two lines:
        Line 1 → Yes   if every one of (b)(c)(d)(e) exists, else No
        Line 2 → "(b) present=<T/F>; (c)=<T/F>; (d)=<T/F>; (e)=<T/F>"
      `;

    case "notes_rpt_has_loans":
      return `
        Scan the Related–Party Transactions note; return **YES** only if a row
        contains at least one of ("loan", "advance in the nature of loan", "advance") (ignore case) *and* none of
        ("lease", "rent", "commission", "service", "salary",
        "purchase", "sale", "trade", "dividend", "interest",
        "equity", "share", "mutual fund").  Output exactly two lines:

            YES
            <verbatim row text>

        or

            NO
            None
      `;

    case "caro_iii_f_present":
      return `
        You are given Annexure A (CARO).

        Locate the top-level heading "(iii)" and check if a sub-clause
        labelled "(iii)(f)" (variants: "(iii) (f)", "(iii)( f )" etc.) exists.

        If found, return:
            YES
            <full sub-clause heading + first sentence>

        Else return:
            NO
            Missing
      `;

    case "notes_new_finance_total":
      return `
        You are given the full Notes-to-Accounts PDF.

        TASK
        ▸ Scan every note headed *Investments*, *Loans*, *Other financial assets*,
        *Contingent liabilities / guarantees / securities* and the
        *Related-party-transactions* (RPT) note.

        ▸ Pick ONLY the lines that (a) relate to the CURRENT-YEAR movement
        and (b) contain **any** of these keywords
            "loan", "advance in the nature of loan",
            "investment", "financial guarantee", "security provided"

        ✦ Ignore mutual-fund lines and any rows where the keyword is preceded
            by "trade", "equity", "share", "FVTOCI", "CWIP", "PY" or "previous".

        ▸ A "new" amount exists when the **current-year column** is positive and
        the **previous-year comparative** column is zero / blank.

        OUTPUT (exactly two lines):
        TOTAL: <numeric-sum>
        Details: desc1=<amt1>; desc2=<amt2>; …   OR  "None"
      `;

    case "bs_opening_networth":
      return `
        From the Balance-Sheet extract the **previous-year** column
        (31-Mar-<PY>) for:
        • "Equity share capital"  (paid-up capital)
        • "Other equity" or "Reserves & surplus"
        • Any line that contains "revaluation reserve"

        Compute:
            NetWorth = Capital + Reserves_Surplus – RevaluationReserve

        Return two lines ONLY:
        NETWORTH: <number>
        Source: cap=<val1>; reserves=<val2>; reval=<val3>
      `;

    case "bs_current_liab_vs_asset":
      return `
        You are given a Balance Sheet PDF with two side-by-side columns:
        • one headed "Current Liabilities"
        • one headed "Current Assets"

        1. Find the numeric total for Current Liabilities.
        2. Find the numeric total for Current Assets.
        3. Compare them.

        Return STRICTLY three lines:
        Line 1 → the current liabilities total as a plain number (no commas)
        Line 2 → the current assets total as a plain number
        Line 3 → YES if liabilities > assets, NO otherwise
      `;

    case "caro_intangible_clause":
      return `
        You are given a CARO Annexure PDF.
        Search for sub‑heading "(i)(a)(B)" (maintenance of records for INTANGIBLE assets).
        Return STRICTLY:
        Line 1 → "Yes" if it appears exactly once, else "No"
        Line 2 → "Found" or "Missing"
      `;

    case "caro_nbfc_iii_clause_present":
      return `
        Scan this CARO Annexure for the sub‑clause labels "(iii)(a)" or "(iii)(e)".
        Return STRICTLY:
        Line 1 → "Yes" if either label is found, else "No"
        Line 2 → First matching sentence (or "Not found")
      `;

    case "ar_has_immovable_property_disputes":
      return `
        Search this Audit Report PDF for any mention of disputes, litigations or legal
        proceedings related to immovable property. Look for words like
        "dispute", "litigation", "pending" near "land", "building", "title", "deed".

        Return EXACTLY two lines:

            YES
            <the sentence or phrase found>

        or

            NO
            None
      `;

    case "custom":
      const customPrompt = parameters?.prompt || "";
      return `
        Analyze this PDF document according to the following instructions:

        ${customPrompt}

        Return ONLY:
        - First line: "Yes" or "No" (indicating if the check passes)
        - Then provide a brief explanation of your findings
      `;

    default:
      return `
        Analyze this PDF document for the check type: ${checkType}.

        Return ONLY:
        - First line: "Yes" or "No" (indicating if the check passes)
        - Then provide a brief explanation of your findings
      `;
  }
}
