// Analysis.tsx - FINAL VERSION - NO EXTERNAL FORM IMPORTS

import React, { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";

// Import types ONLY - NO COMPONENTS
import { 
  DocumentFiles,
  AnalysisParameters,
  processDocumentsForAnalysis,
  getAnalysisSummary
} from "@/lib/workingDocumentProcessor";

// COMPLETELY INLINE FORM - NO EXTERNAL DEPENDENCIES
const InlineTestForm = ({ onStartAnalysis, isProcessing }: {
  onStartAnalysis: (documents: DocumentFiles, parameters: AnalysisParameters) => void;
  isProcessing: boolean;
}) => {
  const [companyName, setCompanyName] = useState("PKF Test Company");
  const [auditFile, setAuditFile] = useState<File | null>(null);

  console.log('🔥 InlineTestForm rendered with props:', {
    onStartAnalysisType: typeof onStartAnalysis,
    isProcessing
  });

  const handleSubmit = () => {
    console.log('🚀 INLINE FORM SUBMIT CLICKED');
    console.log('onStartAnalysis type:', typeof onStartAnalysis);
    console.log('onStartAnalysis value:', onStartAnalysis);

    if (typeof onStartAnalysis !== 'function') {
      alert('❌ onStartAnalysis is not a function!');
      return;
    }

    if (!companyName.trim()) {
      alert('Please enter company name');
      return;
    }

    if (!auditFile) {
      alert('Please upload audit report');
      return;
    }

    const documents: DocumentFiles = {
      audit_report: auditFile
    };

    const parameters: AnalysisParameters = {
      company_name: companyName,
      audit_date: new Date(),
      profit_or_loss: "Profit",
      company_listing_status: "Unlisted",
      top_1000_or_500: "No",
      audit_report_type: "Normal",
      audit_opinion_type: "Unmodified",
      is_nbfc: "No",
    };

    console.log('✅ Calling onStartAnalysis with:', { documents, parameters });
    onStartAnalysis(documents, parameters);
  };

  return (
    <div style={{ padding: '20px', border: '3px solid green', borderRadius: '10px', backgroundColor: '#f0fff0' }}>
      <h2 style={{ color: 'green', marginBottom: '20px' }}>
        ✅ INLINE FORM (NO EXTERNAL IMPORTS)
      </h2>
      
      {/* Debug Panel */}
      <div style={{ 
        padding: '15px', 
        backgroundColor: 'green', 
        color: 'white', 
        borderRadius: '5px', 
        marginBottom: '20px' 
      }}>
        <div><strong>🔥 THIS IS THE INLINE FORM</strong></div>
        <div>onStartAnalysis Type: <strong>{typeof onStartAnalysis}</strong></div>
        <div>Is Function: <strong>{typeof onStartAnalysis === 'function' ? 'YES ✅' : 'NO ❌'}</strong></div>
        <div>Processing: <strong>{isProcessing ? 'YES' : 'NO'}</strong></div>
      </div>

      {/* Company Name */}
      <div style={{ marginBottom: '15px' }}>
        <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
          Company Name
        </label>
        <input
          type="text"
          value={companyName}
          onChange={(e) => setCompanyName(e.target.value)}
          style={{ 
            width: '100%', 
            padding: '10px', 
            border: '2px solid #ccc', 
            borderRadius: '5px',
            fontSize: '16px'
          }}
          placeholder="Enter company name"
          disabled={isProcessing}
        />
      </div>

      {/* File Upload */}
      <div style={{ marginBottom: '20px' }}>
        <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
          Audit Report PDF
        </label>
        <input
          type="file"
          accept=".pdf"
          onChange={(e) => setAuditFile(e.target.files?.[0] || null)}
          style={{ 
            width: '100%', 
            padding: '10px', 
            border: '2px solid #ccc', 
            borderRadius: '5px',
            fontSize: '16px'
          }}
          disabled={isProcessing}
        />
        {auditFile && (
          <div style={{ color: 'green', fontSize: '14px', marginTop: '5px' }}>
            ✅ {auditFile.name} ({(auditFile.size / 1024).toFixed(1)}KB)
          </div>
        )}
      </div>

      {/* Submit Button */}
      <button
        onClick={handleSubmit}
        disabled={isProcessing || !companyName.trim() || !auditFile}
        style={{
          width: '100%',
          padding: '15px',
          backgroundColor: isProcessing || !companyName.trim() || !auditFile ? '#ccc' : '#28a745',
          color: 'white',
          border: 'none',
          borderRadius: '5px',
          fontSize: '18px',
          fontWeight: 'bold',
          cursor: isProcessing || !companyName.trim() || !auditFile ? 'not-allowed' : 'pointer'
        }}
      >
        {isProcessing ? '⏳ Processing...' : '🚀 START ANALYSIS TEST'}
      </button>
    </div>
  );
};

const Analysis = () => {
  const { currentUser } = useAuth();
  const { toast } = useToast();
  const [isProcessing, setIsProcessing] = useState(false);

  console.log('🔥 Analysis component rendered');

  // THE MAIN FUNCTION
  const handleStartAnalysis = async (
    documents: DocumentFiles,
    parameters: AnalysisParameters
  ) => {
    console.log('🎉 === ANALYSIS FUNCTION CALLED ===');
    console.log('Documents:', documents);
    console.log('Parameters:', parameters);

    if (!currentUser) {
      toast({
        title: "Authentication Required",
        description: "Please log in to perform analysis",
        variant: "destructive",
      });
      return;
    }

    setIsProcessing(true);

    try {
      // Show immediate success
      toast({
        title: "🎉 FUNCTION CALL SUCCESS!",
        description: "The prop passing is working perfectly!",
      });

      console.log('🚀 Starting real document processing...');
      
      // Call the real processing function
      const results = await processDocumentsForAnalysis(
        documents,
        parameters,
        (progress) => {
          console.log(`Progress: ${progress.completed}/${progress.total} - ${progress.currentCheck}`);
        }
      );

      console.log('✅ Processing completed:', results);

      // Generate summary
      const summary = getAnalysisSummary(results);
      console.log('📊 Summary:', summary);

      toast({
        title: "✅ Analysis Complete!",
        description: `Completed ${summary.total} checks with ${summary.compliancePercentage}% compliance`,
      });

    } catch (error) {
      console.error("❌ Error:", error);
      toast({
        title: "Error",
        description: `Error: ${error instanceof Error ? error.message : String(error)}`,
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div style={{ padding: '20px' }}>
      <h1 style={{ fontSize: '32px', fontWeight: 'bold', marginBottom: '20px' }}>
        🧪 Audit Report Analyzer - WORKING TEST
      </h1>

      <div style={{ 
        padding: '20px', 
        backgroundColor: 'white', 
        borderRadius: '10px', 
        boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
        marginBottom: '20px'
      }}>
        <h2 style={{ fontSize: '24px', marginBottom: '10px' }}>Function Test</h2>
        <p style={{ color: '#666', marginBottom: '20px' }}>
          This test uses a completely inline form with no external component imports.
        </p>

        {/* Parent Debug */}
        <div style={{ 
          padding: '15px', 
          backgroundColor: '#e3f2fd', 
          borderRadius: '5px',
          marginBottom: '20px'
        }}>
          <h4 style={{ color: '#1976d2', marginBottom: '10px' }}>Parent Component Status</h4>
          <div style={{ fontSize: '14px' }}>
            <div>Function Type: <strong>{typeof handleStartAnalysis}</strong></div>
            <div>Function Exists: <strong>{typeof handleStartAnalysis === 'function' ? 'YES ✅' : 'NO ❌'}</strong></div>
            <div>Current User: <strong>{currentUser?.uid || 'Not logged in'}</strong></div>
            <div>Processing: <strong>{isProcessing ? 'YES' : 'NO'}</strong></div>
          </div>
        </div>

        {/* INLINE FORM - NO IMPORTS */}
        <InlineTestForm 
          onStartAnalysis={handleStartAnalysis}
          isProcessing={isProcessing}
        />
      </div>
    </div>
  );
};

export default Analysis;