// interlinkedProcessor.ts - Handle interlinked document comparisons

import { CheckResult } from './checkDefinitions';
import { processSingleDocumentCheck } from './geminiProcessor';

export interface DocumentFile {
  file: File;
  type: string;
  name: string;
}

/**
 * Compare two document check results for reference matching
 */
function compareReferenceNumbers(
  result1: CheckResult,
  result2: CheckResult,
  checkType: string
): CheckResult {
  const data1 = result1.extractedData?.paragraphComparison || '';
  const data2 = result2.extractedData?.paragraphComparison || '';
  
  // Extract paragraph numbers from the comparison strings
  const extractParagraphNumber = (text: string): string => {
    const match = text.match(/paragraph:\s*([^,\s]+)/i);
    return match ? match[1].trim() : '';
  };
  
  const para1 = extractParagraphNumber(data1);
  const para2 = extractParagraphNumber(data2);
  
  const isCompliant = para1 !== '' && para2 !== '' && para1 === para2;
  
  return {
    isCompliant,
    explanation: isCompliant 
      ? `Reference numbers match: ${para1}` 
      : `Reference numbers do not match. Document 1: ${para1 || 'Not found'}, Document 2: ${para2 || 'Not found'}`,
    confidence: 0.9,
    extractedData: {
      document1Reference: para1,
      document2Reference: para2,
      checkType
    }
  };
}

/**
 * Process Audit Report + CARO reference matching
 */
export async function processAuditReportCAROInterlink(
  auditReportFile: File,
  caroFile: File,
  parameters: Record<string, any>
): Promise<CheckResult> {
  try {
    console.log('Processing Audit Report + CARO interlink check');
    
    // Get reference from Audit Report
    const auditReportResult = await processSingleDocumentCheck(
      auditReportFile,
      'caro_interlink_ar',
      'caro_interlink',
      parameters
    );
    
    // Get reference from CARO
    const caroResult = await processSingleDocumentCheck(
      caroFile,
      'caro_interlink_annexure',
      'caro_interlink',
      parameters
    );
    
    // Compare the references
    return compareReferenceNumbers(auditReportResult, caroResult, 'caro_interlink');
    
  } catch (error) {
    console.error('Error processing Audit Report + CARO interlink:', error);
    return {
      isCompliant: false,
      explanation: `Error processing interlink check: ${error instanceof Error ? error.message : String(error)}`,
      confidence: 0.5
    };
  }
}

/**
 * Process Audit Report + IFC reference matching
 */
export async function processAuditReportIFCInterlink(
  auditReportFile: File,
  ifcFile: File,
  parameters: Record<string, any>
): Promise<CheckResult> {
  try {
    console.log('Processing Audit Report + IFC interlink check');
    
    // Get reference from Audit Report
    const auditReportResult = await processSingleDocumentCheck(
      auditReportFile,
      'ifc_interlink_ar',
      'ifc_interlink',
      parameters
    );
    
    // Get reference from IFC
    const ifcResult = await processSingleDocumentCheck(
      ifcFile,
      'ifc_interlink_annexure',
      'ifc_interlink',
      parameters
    );
    
    // Compare the references
    return compareReferenceNumbers(auditReportResult, ifcResult, 'ifc_interlink');
    
  } catch (error) {
    console.error('Error processing Audit Report + IFC interlink:', error);
    return {
      isCompliant: false,
      explanation: `Error processing interlink check: ${error instanceof Error ? error.message : String(error)}`,
      confidence: 0.5
    };
  }
}

/**
 * Extract numeric value from a check result
 */
function extractNumericValue(result: CheckResult): number {
  if (result.extractedData?.value !== undefined) {
    return Number(result.extractedData.value);
  }
  
  // Try to extract from explanation
  const numericMatch = result.explanation.match(/(\d+(?:\.\d+)?)/);
  return numericMatch ? Number(numericMatch[1]) : 0;
}

/**
 * Process Balance Sheet + CARO PPE Check
 */
export async function processBalanceSheetCAROPPE(
  balanceSheetFile: File,
  caroFile: File,
  parameters: Record<string, any>
): Promise<CheckResult> {
  try {
    console.log('Processing Balance Sheet + CARO PPE check');
    
    // Get PPE value from Balance Sheet
    const balanceSheetResult = await processSingleDocumentCheck(
      balanceSheetFile,
      'balance_sheet_ppe_value',
      'balance_sheet_ppe_value',
      parameters
    );
    
    const ppeValue = extractNumericValue(balanceSheetResult);
    
    if (ppeValue <= 0) {
      return {
        isCompliant: true,
        explanation: `PPE value is ${ppeValue}, so CARO PPE clauses are not required`,
        confidence: 0.9,
        extractedData: { ppeValue, caroRequired: false }
      };
    }
    
    // Check CARO clauses (i)(a)(A) and (i)(b)
    const caroResult = await processSingleDocumentCheck(
      caroFile,
      'caro_ppe_clause',
      'caro_ppe_clause',
      parameters
    );
    
    return {
      isCompliant: caroResult.isCompliant,
      explanation: `PPE value: ${ppeValue}. CARO PPE clauses compliance: ${caroResult.isCompliant ? 'Yes' : 'No'}. ${caroResult.explanation}`,
      confidence: 0.9,
      extractedData: {
        ppeValue,
        caroRequired: true,
        caroCompliant: caroResult.isCompliant,
        caroDetails: caroResult.extractedData
      }
    };
    
  } catch (error) {
    console.error('Error processing Balance Sheet + CARO PPE check:', error);
    return {
      isCompliant: false,
      explanation: `Error processing check: ${error instanceof Error ? error.message : String(error)}`,
      confidence: 0.5
    };
  }
}

/**
 * Process Balance Sheet + CARO Intangible Assets Check
 */
export async function processBalanceSheetCAROIntangible(
  balanceSheetFile: File,
  caroFile: File,
  parameters: Record<string, any>
): Promise<CheckResult> {
  try {
    console.log('Processing Balance Sheet + CARO Intangible Assets check');
    
    // Get Intangible Assets value from Balance Sheet
    const balanceSheetResult = await processSingleDocumentCheck(
      balanceSheetFile,
      'balance_sheet_intangible_value',
      'balance_sheet_intangible_value',
      parameters
    );
    
    const intangibleValue = extractNumericValue(balanceSheetResult);
    
    if (intangibleValue <= 0) {
      return {
        isCompliant: true,
        explanation: `Intangible assets value is ${intangibleValue}, so CARO intangible assets clause is not required`,
        confidence: 0.9,
        extractedData: { intangibleValue, caroRequired: false }
      };
    }
    
    // Check CARO clause (i)(a)(B)
    const caroResult = await processSingleDocumentCheck(
      caroFile,
      'caro_intangible_clause',
      'caro_intangible_clause',
      parameters
    );
    
    return {
      isCompliant: caroResult.isCompliant,
      explanation: `Intangible assets value: ${intangibleValue}. CARO intangible assets clause compliance: ${caroResult.isCompliant ? 'Yes' : 'No'}. ${caroResult.explanation}`,
      confidence: 0.9,
      extractedData: {
        intangibleValue,
        caroRequired: true,
        caroCompliant: caroResult.isCompliant,
        caroDetails: caroResult.extractedData
      }
    };
    
  } catch (error) {
    console.error('Error processing Balance Sheet + CARO Intangible Assets check:', error);
    return {
      isCompliant: false,
      explanation: `Error processing check: ${error instanceof Error ? error.message : String(error)}`,
      confidence: 0.5
    };
  }
}

/**
 * Process Balance Sheet + CARO Inventory Check
 */
export async function processBalanceSheetCAROInventory(
  balanceSheetFile: File,
  caroFile: File,
  notesFile: File,
  parameters: Record<string, any>
): Promise<CheckResult> {
  try {
    console.log('Processing Balance Sheet + CARO + Notes Inventory check');
    
    // Get Inventory value from Balance Sheet
    const balanceSheetResult = await processSingleDocumentCheck(
      balanceSheetFile,
      'balance_sheet_inventory_value',
      'balance_sheet_inventory_value',
      parameters
    );
    
    const inventoryValue = extractNumericValue(balanceSheetResult);
    
    // Check Notes for inventory details
    const notesResult = await processSingleDocumentCheck(
      notesFile,
      'notes_inventory_details',
      'notes_inventory_details',
      parameters
    );
    
    if (inventoryValue <= 0) {
      return {
        isCompliant: true,
        explanation: `Inventory value is ${inventoryValue}, so CARO inventory clause is not required`,
        confidence: 0.9,
        extractedData: { inventoryValue, caroRequired: false },
        source: 'interlinked'
      };
    }
    
    // Check CARO clause (ii)(a)
    const caroResult = await processSingleDocumentCheck(
      caroFile,
      'caro_inventory_clause',
      'caro_inventory_clause',
      parameters
    );
    
    return {
      isCompliant: caroResult.isCompliant && notesResult.isCompliant,
      explanation: `Inventory value: ${inventoryValue}. Notes disclosure: ${notesResult.explanation}. CARO inventory clause compliance: ${caroResult.isCompliant ? 'Yes' : 'No'}. ${caroResult.explanation}`,
      confidence: 0.9,
      extractedData: {
        inventoryValue,
        caroRequired: true,
        caroCompliant: caroResult.isCompliant,
        notesCompliant: notesResult.isCompliant,
        caroDetails: caroResult.extractedData,
        notesDetails: notesResult.extractedData
      },
      source: 'interlinked'
    };
    
  } catch (error) {
    console.error('Error processing Balance Sheet + CARO Inventory check:', error);
    return {
      isCompliant: false,
      explanation: `Error processing check: ${error instanceof Error ? error.message : String(error)}`,
      confidence: 0.5
    };
  }
}

/**
 * Process Balance Sheet + CARO Secured Borrowings Check
 */
export async function processBalanceSheetCAROSecuredBorrowings(
  balanceSheetFile: File,
  caroFile: File,
  notesFile: File,
  parameters: Record<string, any>
): Promise<CheckResult> {
  try {
    console.log('Processing Balance Sheet + CARO + Notes Secured Borrowings check');
    
    // Get Secured Borrowings from Balance Sheet
    const balanceSheetResult = await processSingleDocumentCheck(
      balanceSheetFile,
      'bs_secured_borrowings_gt5cr',
      'bs_secured_borrowings_gt5cr',
      parameters
    );
    
    // Check Notes for borrowings details
    const notesResult = await processSingleDocumentCheck(
      notesFile,
      'notes_secured_borrowings',
      'notes_secured_borrowings',
      parameters
    );
    
    const borrowingsAbove5Cr = balanceSheetResult.isCompliant; // This check returns true if > 5cr
    
    if (!borrowingsAbove5Cr) {
      return {
        isCompliant: true,
        explanation: `Secured borrowings are not above ₹5 crores, so CARO clause (ii)(b) is not required`,
        confidence: 0.9,
        extractedData: { borrowingsAbove5Cr: false, caroRequired: false },
        source: 'interlinked'
      };
    }
    
    // Check CARO clause (ii)(b) for quarterly returns
    const caroResult = await processSingleDocumentCheck(
      caroFile,
      'caro_inventory_clause_iib',
      'caro_inventory_clause_iib',
      parameters
    );
    
    return {
      isCompliant: caroResult.isCompliant && notesResult.isCompliant,
      explanation: `Secured borrowings > ₹5 crores. Notes disclosure: ${notesResult.explanation}. CARO clause (ii)(b) compliance: ${caroResult.isCompliant ? 'Yes' : 'No'}. ${caroResult.explanation}`,
      confidence: 0.9,
      extractedData: {
        borrowingsAbove5Cr: true,
        caroRequired: true,
        caroCompliant: caroResult.isCompliant,
        notesCompliant: notesResult.isCompliant,
        caroDetails: caroResult.extractedData,
        notesDetails: notesResult.extractedData
      },
      source: 'interlinked'
    };
    
  } catch (error) {
    console.error('Error processing Balance Sheet + CARO Secured Borrowings check:', error);
    return {
      isCompliant: false,
      explanation: `Error processing check: ${error instanceof Error ? error.message : String(error)}`,
      confidence: 0.5
    };
  }
}