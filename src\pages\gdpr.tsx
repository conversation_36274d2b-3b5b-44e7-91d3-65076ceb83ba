import React from "react";
import { ArrowL<PERSON>t, Shield, FileText, <PERSON>r<PERSON><PERSON><PERSON>, Eye, AlertTriangle } from "lucide-react";
import { Link } from "react-router-dom";
import { Button } from "@/components/ui/button";

const GDPR = () => {
  return (
    <div className="min-h-screen bg-slate-900 text-white">
      {/* Header */}
      <header className="bg-slate-900/95 backdrop-blur-lg border-b border-white/10">
        <div className="w-full px-6 md:px-12 lg:px-20 py-6 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="h-10 w-10 rounded-xl bg-gradient-to-br from-blue-600 to-purple-600 flex items-center justify-center text-white font-bold">
              P
            </div>
            <span className="text-xl font-bold text-white">PKF Audit AI</span>
          </div>

          <Link to="/">
            <Button variant="ghost" className="text-white">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Home
            </Button>
          </Link>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-24 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-900 via-blue-900 to-slate-900"></div>
        <div className="w-full px-6 md:px-12 lg:px-20 relative z-10">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">GDPR</span> Compliance
            </h1>
            <p className="text-xl md:text-2xl text-white/80 mb-8">
              How PKF Audit AI protects your data under the General Data Protection Regulation
            </p>
            <div className="text-sm text-blue-300 mb-4">Last updated: May 15, 2025</div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-16 w-full px-6 md:px-12 lg:px-20">
        <div className="max-w-4xl mx-auto space-y-12">
          <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-3xl p-8">
            <div className="flex items-start gap-4 mb-6">
              <div className="h-12 w-12 rounded-xl bg-blue-600/20 flex items-center justify-center">
                <Shield className="h-6 w-6 text-blue-400" />
              </div>
              <div>
                <h2 className="text-2xl font-bold mb-2">Our GDPR Commitment</h2>
                <p className="text-white/70 leading-relaxed">
                  PKF Audit AI is fully committed to compliance with the General Data Protection Regulation (GDPR), the European Union's comprehensive data protection law. We have implemented technical and organizational measures to ensure that all data processing activities meet the requirements of the GDPR. Our approach to GDPR compliance is proactive and embedded in everything we do.
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-3xl p-8">
            <div className="flex items-start gap-4 mb-6">
              <div className="h-12 w-12 rounded-xl bg-purple-600/20 flex items-center justify-center">
                <FileText className="h-6 w-6 text-purple-400" />
              </div>
              <div>
                <h2 className="text-2xl font-bold mb-2">Lawful Basis for Processing</h2>
                <p className="text-white/70 leading-relaxed">
                  We process personal data only with a valid lawful basis as required by GDPR. Depending on the context, we rely on different lawful bases including:
                </p>
                <ul className="list-disc pl-6 mt-4 space-y-2 text-white/70">
                  <li>Contractual necessity when providing our audit services</li>
                  <li>Legitimate interests for business operations and improvements</li>
                  <li>Legal obligations for regulatory compliance</li>
                  <li>Explicit consent for marketing communications and certain data usages</li>
                </ul>
              </div>
            </div>
          </div>

          <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-3xl p-8">
            <div className="flex items-start gap-4 mb-6">
              <div className="h-12 w-12 rounded-xl bg-green-600/20 flex items-center justify-center">
                <UserCheck className="h-6 w-6 text-green-400" />
              </div>
              <div>
                <h2 className="text-2xl font-bold mb-2">Your Data Subject Rights</h2>
                <p className="text-white/70 leading-relaxed">
                  Under GDPR, PKF Audit AI respects and facilitates your rights as a data subject, including:
                </p>
                <ul className="list-disc pl-6 mt-4 space-y-2 text-white/70">
                  <li><span className="font-semibold">Right to Access</span> - You can request copies of your personal data</li>
                  <li><span className="font-semibold">Right to Rectification</span> - You can request corrections to inaccurate data</li>
                  <li><span className="font-semibold">Right to Erasure</span> - You can request deletion of your data (under certain conditions)</li>
                  <li><span className="font-semibold">Right to Restriction</span> - You can request limits on how we use your data</li>
                  <li><span className="font-semibold">Right to Object</span> - You can object to our processing of your data</li>
                  <li><span className="font-semibold">Right to Data Portability</span> - You can request your data in a portable format</li>
                </ul>
                <p className="text-white/70 mt-4">
                  To exercise any of these rights, please contact our Data Protection <NAME_EMAIL>.
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-3xl p-8">
            <div className="flex items-start gap-4 mb-6">
              <div className="h-12 w-12 rounded-xl bg-yellow-600/20 flex items-center justify-center">
                <Eye className="h-6 w-6 text-yellow-400" />
              </div>
              <div>
                <h2 className="text-2xl font-bold mb-2">Data Protection Measures</h2>
                <p className="text-white/70 leading-relaxed">
                  PKF Audit AI implements appropriate technical and organizational measures to ensure security of personal data, including:
                </p>
                <ul className="list-disc pl-6 mt-4 space-y-2 text-white/70">
                  <li>End-to-end encryption for all data transfers</li>
                  <li>Regular security assessments and penetration testing</li>
                  <li>Multi-factor authentication for system access</li>
                  <li>Data minimization principles throughout our processes</li>
                  <li>Staff training on data protection and privacy</li>
                  <li>Data Protection Impact Assessments for new processing activities</li>
                </ul>
              </div>
            </div>
          </div>

          <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-3xl p-8">
            <div className="flex items-start gap-4 mb-6">
              <div className="h-12 w-12 rounded-xl bg-red-600/20 flex items-center justify-center">
                <AlertTriangle className="h-6 w-6 text-red-400" />
              </div>
              <div>
                <h2 className="text-2xl font-bold mb-2">Data Breach Notification</h2>
                <p className="text-white/70 leading-relaxed">
                  In the unlikely event of a data breach affecting personal data, PKF Audit AI has established procedures to:
                </p>
                <ul className="list-disc pl-6 mt-4 space-y-2 text-white/70">
                  <li>Notify the relevant supervisory authority within 72 hours</li>
                  <li>Inform affected data subjects without undue delay</li>
                  <li>Document all breaches and remediation measures</li>
                  <li>Implement preventive measures to reduce future risks</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact DPO Section */}
      <section className="py-16 w-full px-6 md:px-12 lg:px-20 bg-gradient-to-br from-blue-900/30 to-purple-900/30">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl font-bold mb-6">Contact Our Data Protection Officer</h2>
          <p className="text-xl text-white/70 mb-8">
            For any GDPR-related inquiries or to exercise your data subject rights
          </p>
          <Link to="/contact">
            <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 rounded-full px-8 py-3">
              Contact DPO
            </Button>
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t border-white/10 py-8">
        <div className="w-full px-6 md:px-12 lg:px-20 text-center">
          <p className="text-white/60">
            © {new Date().getFullYear()} PKF Audit AI. All rights reserved.
          </p>
        </div>
      </footer>
    </div>
  );
};

export default GDPR;
