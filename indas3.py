import streamlit as st
import google.generativeai as genai
import io
import pandas as pd
import re  # Import re at the module level
from pathlib import Path
import json
import hashlib
from pdfminer.high_level import extract_text
from io import BytesIO
from typing import Tuple 
import io, re
from pdfminer.pdfparser import PDFSyntaxError


# File paths for persistent storage
DATA_DIR = Path("data")
QUESTIONS_FILE = DATA_DIR / "questions.txt"
CUSTOM_PROMPTS_FILE = DATA_DIR / "custom_prompts.txt"

# Ensure data directory exists
DATA_DIR.mkdir(exist_ok=True)

temp = 0.1
top_k = 0.75

# Hardcoded credentials
USERS = {
    "admin": "8c6976e5b5410415bde908bd4dee15dfb167a9c873fc4bb8a81f6f2ab448a918",  # admin
    "user": "04f8996da763b7a969b1028ee3007569eaf3a635486ddab211d512c85b9df8fb",  # user
}


def save_questions(questions):
    try:
        with open(QUESTIONS_FILE, 'w') as f:
            json.dump(questions, f, indent=4)
    except OSError as e:
        print(f"Error saving questions: {str(e)}")
        st.error("Failed to save questions. Please try again.")


def load_questions():
    
    try:
        if QUESTIONS_FILE.exists():
            with open(QUESTIONS_FILE, 'r') as f:
                content = f.read().strip()
                if content:
                    return json.loads(content)
    except (json.JSONDecodeError, OSError) as e:
        print(f"Error loading questions: {str(e)}")
    # Default question if file not found
    return {
        'q1': {
            'question': 'Is this a Nidhi Company?',
            'prompt': 'Check if there is any mention of the company being a Nidhi Company'
        }
    }


def save_custom_prompts(prompts):
    """Save custom prompts to file"""
    try:
        if not isinstance(prompts, list):
            prompts = []
        with open(CUSTOM_PROMPTS_FILE, 'w') as f:
            json.dump(prompts, f, indent=4)
    except Exception as e:
        print(f"Error saving custom prompts: {e}")
        st.error("Failed to save custom prompts")


def load_custom_prompts():
    """Load custom prompts from file"""
    try:
        if CUSTOM_PROMPTS_FILE.exists():
            with open(CUSTOM_PROMPTS_FILE, 'r') as f:
                data = json.load(f)
                if isinstance(data, list):
                    return data
    except Exception as e:
        print(f"Error loading custom prompts: {e}")
    return []


def verify_password(username, password):
    """Verify the password hash against stored credentials"""
    if username not in USERS:
        return False

    password_hash = hashlib.sha256(password.encode()).hexdigest()
    return password_hash == USERS[username]


# Page configuration
st.set_page_config(page_title="PDF Analyzer", layout="wide")

# Initialize session state
if 'current_page' not in st.session_state:
    st.session_state.current_page = 'login'
if 'answers' not in st.session_state:
    st.session_state.answers = {}
if 'questions' not in st.session_state:
    st.session_state.questions = load_questions()
if 'custom_prompts' not in st.session_state:
    st.session_state.custom_prompts = load_custom_prompts()
if 'next_question_id' not in st.session_state:
    st.session_state.next_question_id = len(st.session_state.questions) + 1
if 'logged_in' not in st.session_state:
    st.session_state.logged_in = False
if 'username' not in st.session_state:
    st.session_state.username = ""
if 'company_name_issues' not in st.session_state:
    st.session_state.company_name_issues = []
if 'company_listing_status' not in st.session_state:
    st.session_state.company_listing_status = 'Unlisted'

if 'audit_report_type' not in st.session_state:
    st.session_state.audit_report_type = 'Normal'

if 'profit_or_loss' not in st.session_state:
    st.session_state.profit_or_loss = 'Profit'

if 'audit_opinion_type' not in st.session_state:
    st.session_state.audit_opinion_type = 'Unmodified'

if 'top_1000_or_500' not in st.session_state:
    st.session_state.top_1000_or_500 = 'No'

if 'is_nbfc' not in st.session_state:
    st.session_state.is_nbfc = 'No'


##########################
# Initialize Gemini
##########################
def initialize_gemini():
    # Replace with your valid API key
    api_key = "AIzaSyA2-3gPSsIMLH3ZVqQ6vmLl5STSYRrmAT0"
    genai.configure(api_key=api_key)
    return genai.GenerativeModel('gemini-1.5-pro')


# ---------------------------------------------------------------------------



def extract_pdf_text(uploaded_file):
    # uploaded_file is a Streamlit UploadedFile
    pdf_bytes = uploaded_file.getvalue()
    # Wrap bytes in a BytesIO, then hand to extract_text
    return extract_text(BytesIO(pdf_bytes))


#2.4 caro-NOA new investment


def extract_new_invest_loans_guar(notes_pdf_path: str) -> dict:
    """
    Scans the Notes-to-Accounts PDF for:
      • New investments in the year (excluding “mutual fund” lines)
      • New loans made in the year
      • New financial guarantees or security deposits provided

    Returns a dict with either the first matching line or None:
        {
          "investments": <line> or None,
          "loans":       <line> or None,
          "guarantees":  <line> or None,
        }
    """
    raw = extract_pdf_text(notes_pdf_path)
    # split into non-empty lines
    lines = [ln.strip() for ln in raw.splitlines() if ln.strip()]

    result = {"investments": None, "loans": None, "guarantees": None}

    # 1) Investments – look for “investment…during the year”, skip “mutual fund”
    for ln in lines:
        if (re.search(r"investment[s]?\s.*during the year", ln, re.I)
            and not re.search(r"mutual fund", ln, re.I)):
            result["investments"] = ln
            break

    # 2) Loans – look for “loan…during the year”
    for ln in lines:
        if re.search(r"loan[s]?\s.*during the year", ln, re.I):
            result["loans"] = ln
            break

    # 3) Guarantees/Security Deposits – only in Contingent Liabilities section,
    #    and only if the line contains a monetary amount (Cr, crore, ₹, etc.)
    parts = raw.lower().split("contingent liabilities", 1)
    if len(parts) > 1:
        for ln in parts[1].splitlines():
            if (
                re.search(r"\b(guarantee|security|deposit)\b", ln, re.I)
                and re.search(r"\d+[,\d]*(?:\.\d+)?\s*(?:cr|crore|₹)", ln, re.I)
            ):
                result["guarantees"] = ln.strip()
                break

    return result




# 2.3 ---Share Capital / Debentures increase → CARO clause (x)(a)
def extract_share_debenture_changes(pdf_file) -> dict:
    """
    Given a Balance Sheet PDF (Streamlit UploadedFile),
    extract current vs prior year numbers for:
      • Equity Share Capital
      • Debentures (debenture/debentures)

    Returns a dict:
        {
          "current_share_capital": float,
          "prior_share_capital": float,
          "current_debentures": float,
          "prior_debentures": float
        }
    """
    raw = extract_text(BytesIO(pdf_file.getvalue()))

    # Equity Share Capital row
    esc_re = re.compile(
        r"Equity\s+Share\s+Capital[\s\S]*?([\d,]+(?:\.\d+)?)\s+([\d,]+(?:\.\d+)?)",
        re.IGNORECASE
    )
    m1 = esc_re.search(raw)
    if m1:
        cur_sc = float(m1.group(1).replace(",", ""))
        prev_sc = float(m1.group(2).replace(",", ""))
    else:
        cur_sc = prev_sc = 0.0

    # Debentures row
    deb_re = re.compile(
        r"Debentur(?:e|es)[\s\S]*?([\d,]+(?:\.\d+)?)\s+([\d,]+(?:\.\d+)?)",
        re.IGNORECASE
    )
    m2 = deb_re.search(raw)
    if m2:
        cur_db = float(m2.group(1).replace(",", ""))
        prev_db = float(m2.group(2).replace(",", ""))
    else:
        cur_db = prev_db = 0.0

    return {
        "current_share_capital": cur_sc,
        "prior_share_capital":   prev_sc,
        "current_debentures":    cur_db,
        "prior_debentures":      prev_db
    }


##########################
# Process PDF with Gemini for specific checks
##########################
def process_pdf_for_check(pdf_file, model, check_type, parameters=None):
    """
    Process PDF directly using Gemini API for a specific check.

    Args:
        pdf_file: The uploaded PDF file
        model: The Gemini model instance
        check_type: Type of check to perform
        parameters: Additional parameters for the check

    Returns:
        tuple: (bool, str) indicating (is_compliant, reason)
    """
    try:
        # Get the PDF content as bytes
        pdf_content = pdf_file.getvalue()

        # Create prompt based on check type
        if check_type == "company_format":
            company_name = parameters.get("company_name", "")
            prompt = f"""
            Analyze this audit report PDF. Check if there is EXACTLY the text "To the Members of {company_name}" 
            below the Title and above the Opinion Section. It should match word-for-word, including spaces.

            1. First, extract all text from the PDF.
            2. Find the text "To the Members of {company_name}" in the document.
            3. Check if it appears between the title and the opinion section.
            4. Confirm it matches exactly, including spacing and capitalization.

            Return ONLY:
            - First line: "Yes" or "No" (indicating if the exact text is found)
            - Additional explanation of what you found or why it doesn't match
            """

        elif check_type == "brsr_brr":
            top_1000_500 = parameters.get("top_1000_500", "No")
            # Strict branching based on top_1000_500
            if top_1000_500 == "Yes":
                # Expect BRSR
                prompt = """
        You are analyzing an audit report PDF. The user indicates the company IS among top 1000 or top 500 listed.
        Therefore, we expect "Business Responsibility and Sustainability Report (BRSR)" — NOT plain "business responsibility report".

        ***STRICT REQUIREMENTS***:
        1) Look specifically in the 'Information Other than the financial statements' section (case-insensitive search).
        2) If you find either:
           - The exact phrase "Business Responsibility and Sustainability Report"
           - OR the acronym "BRSR"
           respond with:
              - First line: "Yes"
              - Second line: A brief snippet or explanation
        3) If you instead find "business responsibility report" (BRR) without "sustainability",
           OR you do not find BRSR at all, respond:
              - First line: "No"
              - Second line: Explanation
        4) If both BRSR and BRR are present, respond "No" because that's inconsistent.

        Return ONLY:
        - The first line: "Yes" or "No"
        - The second line: short reason.
        """
            else:
                # Expect BRR only
                prompt = """
        You are analyzing an audit report PDF. The user indicates the company is NOT among top 1000 or top 500 listed.
        Therefore, we expect the EXACT phrase "business responsibility report" (BRR) — NOT BRSR.

        ***STRICT REQUIREMENTS***:
        1) Look specifically in the 'Information Other than the financial statements' section (case-insensitive search).
        2) If you find "Business Responsibility and Sustainability Report" or "BRSR" anywhere, respond:
              - First line: "No"
              - Second line: Explanation (because that's not acceptable for this scenario)
        3) If you find the exact phrase "business responsibility report" (three words consecutively), 
           respond "Yes" plus a brief snippet. 
        4) If you do NOT find "business responsibility report," respond "No" with an explanation.

        Return ONLY:
        - The first line: "Yes" or "No"
        - The second line: short reason.
        """

            response = model.generate_content(
                contents=[
                    {"mime_type": "application/pdf", "data": pdf_content},
                    prompt
                ]
            )

            response_text = response.text.strip()
            lines = response_text.split('\n')
            if not lines:
                return (False, "No response received")

            first_line = lines[0].strip().lower()
            explanation = "\n".join(lines[1:]).strip() if len(lines) > 1 else ""

            if first_line == "yes":
                return (True, explanation)
            elif first_line == "no":
                return (False, explanation)
            else:
                return (False, f"Unclear response: {response_text[:200]}...")

        elif check_type == "signature_date":
            audit_date = parameters.get("audit_date")
            date_str = audit_date.strftime("%d %B %Y") if audit_date else "the specified date"
            prompt = f"""
            Analyze this audit report PDF to check if the PKF Sridhar & Santhanam LLP signature block contains the date {date_str}.

            1. Locate the signature block containing:
               - "For PKF Sridhar & Santhanam LLP"
               - "Chartered Accountants"
               - "Firm's Registration No.003990S/S200018"
               - Partner name, membership number, and UDIN

            2. Extract the complete signature block information, including:
               - Firm name
               - Registration number
               - Partner name and designation
               - Membership number
               - UDIN number
               - Place
               - Date

            3. Format all extracted information as HTML, with each part on its own line with <b> tags for labels and normal text for values, 
               followed by <br> tags.

            4. Check if the date mentioned after "Date:" is {date_str} or equivalent format 
               (like {audit_date.strftime("%drd %B %Y") if audit_date and audit_date.day == 3 or audit_date.day == 23 else "23rd May 2023"})

            Return:
            - First line: "Yes" or "No" (indicating if the date is correct)
            - Second line onwards: The formatted signature block with each piece of information on its own line, formatted with HTML tags
              For example:
              <b>Firm:</b> PKF Sridhar & Santhanam LLP<br>
              <b>Registration No:</b> 003990S/S200018<br>
              etc.
            """

        elif check_type == "financial_statements_type":

            report_type = parameters.get("report_type", "Normal")
            if report_type == "Normal":
                prompt = """
                Analyze this audit report PDF. For Normal type audit reports, no specific check is needed.

                Return ONLY:
                - First line: "Yes" (as Normal reports don't need special validation)
                - Then a simple explanation that no specific check is required for Normal report type
                """
            else:
                # Use CTRL+F style approach
                prompt = f"""
                I need you to perform a CTRL+F search for all instances of "financial statements" in this document:

                1. Find every instance of the phrase "financial statements" in the document (case-insensitive)
                2. For each instance, check whether "{report_type.lower()}" directly precedes it
                3. Count how many instances have the proper qualifier and how many don't
                4. For instances missing the qualifier, extract the context (5-10 words before and after)

                Return:
                - First line: "Yes" if ALL instances are properly qualified, "No" if ANY are not
                - Second line: "Found X out of Y instances properly qualified with '{report_type.lower()}'"
                - For unqualified instances, list the context with the phrase "financial statements" 
                - Brief explanation of your assessment
                """

        elif check_type == "opinion_type_consistency":
            opinion_type = parameters.get("opinion_type", "Unmodified")
            if opinion_type.lower() in ["unmodified", "unqualified"]:
                prompt = """
                Analyze this audit report PDF. For Unmodified/Unqualified opinion types, no specific labeling check is needed.

                Return ONLY:
                - First line: "Yes" (as Unmodified opinions don't need special labeling)
                - Then a simple explanation that unmodified opinions don't require specific labeling
                """
            else:
                prompt = f"""
                Analyze this audit report PDF to check if the opinion is consistently labeled as "{opinion_type} opinion" throughout the document.

                1. Find all occurrences of the word "opinion" in relevant sections (excluding headers, auditor responsibilities, etc.)
                2. Check if the opinion is correctly labeled as "{opinion_type} opinion" in each relevant instance.

                Return ONLY:
                - First line: "Yes" or "No" (indicating if the labeling is consistent)
                - Then explain what you found, including relevant text extracts
                """

        elif check_type == "company_name_consistency":
            company_name = parameters.get("company_name", "")
            base_name = company_name.split()[0] if company_name else ""
            prompt = f"""
                Analyze this audit report PDF to check if the company name "{company_name}" is used consistently throughout the document.

                1. Identify all instances where the base name "{base_name}" appears as part of a company name.
                2. Compare these mentions against the expected full name "{company_name}".
                3. Check for variations, different subsidiary names, or inconsistent usage.
                4. IMPORTANT: Generate a list of all variations found and categorize them as follows:
                   - If the name appears EXACTLY as "{company_name}" mark it as "Compliant"
                   - If the name appears with DIFFERENT wording (missing words, extra words, different order) mark it as "Non-Compliant"
                   - For each non-compliant variation, provide the exact text and the context where it was found.

                Return:
                - First line: "Yes" or "No" (indicating if the company name is consistent)
                - Second line: The total count of company name mentions
                - Third line: The number of variations found
                - Then list ALL non-compliant variations found, formatted like:
                  "VARIATION: [exact text found]"
                  "CONTEXT: [surrounding sentence or paragraph]"

                - Finally, briefly summarize the issues found
                """

        elif check_type == "financial_exposure_clause_iv":
            prompt = """
Analyze the provided financial report PDF to verify compliance with a specific financial threshold related to new loans, advances, investments, guarantees, and securities, and check for a corresponding disclosure under "clause (iv)".

Follow these steps carefully:

**Part 1: Extract Financial Data**

1.  **Beginning-of-Year Capital Base Figures:**
    *   Locate the Balance Sheet. Identify figures for the *beginning* of the current financial year (i.e., end of the previous financial year).
    *   a.  **Paid-up Share Capital (BoY):** Extract this amount.
    *   b.  **Reserves and Surplus (BoY):** Extract this amount.
    *   c.  **Revaluation Reserve (BoY):** Extract this amount. If not explicitly found, assume 0.

2.  **New Financial Transactions During the Current Year:**
    *   Scan the Notes to Accounts, specifically sections related to:
        *   Loans (Assets)
        *   Investments
        *   Contingent Liabilities (for Guarantees and Securities provided)
        *   Related Party Transactions (if relevant for these items)
    *   For each category below, identify and sum the amounts of *new* transactions made or provided *during the current financial year*. "New" means the transaction originated in the current year, or there was a significant increase from a zero/nil prior year balance for that specific item.
    *   a.  **Aggregate New Loans Granted:** Sum of all new loans.
    *   b.  **Aggregate New Advances in the Nature of Loans Granted:** Sum of all new advances.
    *   c.  **Aggregate New Investments Made:** Sum of all new investments.
    *   d.  **Aggregate New Guarantees Provided:** Sum of all new guarantees.
    *   e.  **Aggregate New Securities Provided:** Sum of all new securities.

**Part 2: Perform Calculations**

3.  **Calculate Total New Financial Exposure:**
    *   `Total_New_Exposure = (New Loans) + (New Advances) + (New Investments) + (New Guarantees) + (New Securities)` from step 2.

4.  **Calculate Adjusted Capital Base:**
    *   `Adjusted_Capital_Base = (Paid-up Share Capital BoY) + (Reserves and Surplus BoY) - (Revaluation Reserve BoY)` from step 1.

5.  **Calculate 60% Threshold:**
    *   `Threshold_Value = 0.60 * Adjusted_Capital_Base`.

**Part 3: Evaluate Condition and Check Disclosure**

6.  **Compare Exposure to Threshold:**
    *   Determine if `Total_New_Exposure > Threshold_Value`.

7.  **Determine Compliance and Report:**
    *   **If `Total_New_Exposure` is NOT greater than `Threshold_Value`:**
        *   The condition for checking "clause (iv)" disclosure is not met.
        *   Return "Yes" (indicating no non-compliance for this specific check).
        *   Provide an explanation stating the calculated values and that the threshold was not exceeded.
    *   **If `Total_New_Exposure` IS greater than `Threshold_Value`:**
        *   The condition is met. Now, you MUST search the entire document (especially the Directors' Report, Management Discussion and Analysis, or CARO report if present) for a specific disclosure related to "clause (iv)" that addresses this level of financial exposure.
        *   **If an adequate "clause (iv)" disclosure is found:**
            *   Return "Yes".
            *   Provide an explanation including the calculated values, confirmation that the threshold was exceeded, and a quote or summary of the "clause (iv)" disclosure found.
        *   **If an adequate "clause (iv)" disclosure is NOT found, or is insufficient:**
            *   Return "No".
            *   Provide an explanation including the calculated values, confirmation that the threshold was exceeded, and state that the required "clause (iv)" disclosure was not found or was inadequate.

**Output Format:**

Return ONLY:
-   **First line:** "Yes" or "No" (based on step 7).
-   **Second line onwards:** A detailed explanation covering:
    *   Paid-up Share Capital (BoY): [Amount]
    *   Reserves and Surplus (BoY): [Amount]
    *   Revaluation Reserve (BoY): [Amount]
    *   Adjusted Capital Base (BoY): [Calculated Amount]
    *   Aggregate New Loans: [Amount]
    *   Aggregate New Advances: [Amount]
    *   Aggregate New Investments: [Amount]
    *   Aggregate New Guarantees: [Amount]
    *   Aggregate New Securities: [Amount]
    *   Total New Financial Exposure: [Calculated Amount]
    *   60% Threshold Value: [Calculated Amount]
    *   Threshold Exceeded: [Yes/No]
    *   If Threshold Exceeded, Clause (iv) Disclosure Status: [e.g., "Found and adequate: (details)", "Not found", "Found but inadequate: (reason)"]
    *   If Threshold Not Exceeded: "Clause (iv) disclosure check not triggered as threshold was not met."

Ensure all monetary amounts are clearly stated. If any figure cannot be found, state "Not Found" for that specific item and proceed with calculations where possible, noting the missing data in the explanation.
            """
        elif check_type == "caro_interlink_ar":
            # Existing check: Audit Report referencing CARO
            prompt = """
            You are given an Audit Report PDF. Locate the subheading "Report on Other Legal and Regulatory Requirements".
            Under this subheading, identify the paragraph number (e.g. '1.', '2.', or '(i)') that references 
            "Companies (Auditors' Report) Order, 2020" or "CARO" (case-insensitive).

            Steps to follow:
            1) Extract text from that subheading onward.
            2) Find the paragraph that mentions 'Companies (Auditors' Report) Order, 2020' (or 'CARO').
            3) Return ONLY the paragraph number like: "Paragraph: 1"
               If not found, return "Paragraph: Not Found"

            Output format:
            - First line: "Yes"
            - Second line onward: "Paragraph: <number>" or "Paragraph: Not Found"
            """

        elif check_type == "caro_interlink_annexure":
            # Existing check: Annexure A referencing paragraph in AR
            prompt = """
            You are given the CARO Annexure PDF. Look for the heading "Annexure A" (case-insensitive).
            Right below that heading, it should say something like:
                "Referred to in paragraph X on 'Report on Other Legal and Regulatory Requirements'..."

            Steps:
            1) Identify the heading "Annexure A".
            2) Find the line that says "Referred to in paragraph X on 'Report on Other Legal and Regulatory Requirements'..."
            3) Return ONLY that X (the paragraph number), e.g. "Paragraph: 2"
               If not found, return "Paragraph: Not Found"

            Output format:
            - First line: "Yes"
            - Second line onward: "Paragraph: <number>" or "Paragraph: Not Found"
            """


        # ------------------------------------------
        # NEW IFC INTERLINK CHECKS
        # ------------------------------------------
        elif check_type == "ifc_interlink_ar":
            # (a) Find paragraph referencing IFC & Annexure B in AR
            prompt = """
            You are given an Audit Report PDF. Under the subheading "Report on Other Legal and Regulatory Requirements",
            find the paragraph (e.g. '2(g)' or '3(b)') that references:
              "adequacy of the internal financial controls with reference to the standalone financial statements" 
              AND mentions "Annexure B".

            Steps:
            1) Extract text from that subheading onward.
            2) Identify the line referencing both "internal financial controls" and "Annexure B".
            3) Return ONLY the paragraph identifier in the format: "Paragraph: 2(g)" 
               If not found, return "Paragraph: Not Found"

            Output format:
            - First line: "Yes"
            - Then: "Paragraph: <para_id>" or "Paragraph: Not Found"
            """

        elif check_type == "ifc_interlink_annexure":
            # (b) Find paragraph reference in Annexure B
            prompt = """
            You are given the Annexure B PDF. Look for the heading "Annexure B" (case-insensitive).
            Right below that heading, you should see something like:
               "Referred to in paragraph 2(g) on 'Report on Other Legal and Regulatory Requirements'..."

            Steps:
            1) Identify the heading "Annexure B".
            2) Find the exact text "Referred to in paragraph X on 'Report on Other Legal and Regulatory Requirements'..." 
               (where X might be '2(g)', '3(b)', etc.)
            3) Return ONLY that X in the format: "Paragraph: 2(g)"
               If not found, return "Paragraph: Not Found"

            Output format:
            - First line: "Yes"
            - Then: "Paragraph: <para_id>" or "Paragraph: Not Found"
            """
        # ------------------------------------------

        elif check_type == "custom":
            custom_prompt = parameters.get("prompt", "")
            prompt = f"""
                Analyze this audit report PDF according to the following instructions:

                {custom_prompt}

                Return ONLY:
                - First line: "Yes" or "No" (indicating if the check passes)
                - Then provide a brief explanation of your findings
            """

            # ------------------------------------------
            # NEW CARO CLAUSE‑COUNT CHECK
            # ------------------------------------------
        elif check_type == "caro_clause_count":
            """
            Confirm that the CARO Annexure for standalone financial statements
            covers exactly 20 clauses (i – xx).  
            • If any clause is marked “Not Applicable” in a separate
              paragraph/statement, include it in the total.  
            • Return “Yes” only when (applicable clauses + listed‑as‑not‑applicable)
              ==20.
            """

            prompt = f"""
        You are given a CARO Annexure PDF (stand‑alone).  
        TASK A– Count all clause headings actually discussed.  
        • They appear as roman numerals “(i)”, “(ii)”, … “(xx)”.

        TASK B–Locate any **separate paragraph** that lists clauses declared
        “not applicable / not relevant”. Extract those clause numbers.

        STEP 1 – Return two comma‑separated lists:  
        • LIST_APPLICABLE = numerals you found with substantive text  
        • LIST_NOT_APPLICABLE = numerals mentioned only in the separate statement

        STEP 2 – Return a one‑line verdict:  
        • “Yes” if COUNT(LIST_APPLICABLE)+COUNT(LIST_NOT_APPLICABLE) ==20  
        • “No”  if the total ≠20 (or you cannot decide)

        FORMAT (STRICT):  
        Line 1 → “Yes” or “No”  
        Line 2 → “Applicable: (i),(ii)….”  
        Line 3 → “Not Applicable: (viii)….”  
        Line 4 → “Total: <N> of 20”  
        Line 5 → Short explanation (≤40 words)
        """

            response = model.generate_content(
                contents=[
                    {"mime_type": "application/pdf", "data": pdf_content},
                    prompt
                ]
            )

            lines = response.text.strip().split("\n")
            if not lines:
                return (False, "No response")

            verdict = lines[0].strip().lower()
            analysis = "\n".join(lines[1:]).strip()

            return (verdict == "yes", analysis)

            # ------------------------------------------
            # NEW CARO CLAUSE (xxi) CHECK —for Consolidated
            # ------------------------------------------
        elif check_type == "caro_clause_xxi":
            """
            Confirm that Annexure A (CARO) for consolidated financial statements
            contains exactly one heading numbered “(xxi)”.
            """

            prompt = """
        You are given a CARO Annexure PDF (consolidated).  
        Search the entire document (case‑insensitive) for the clause heading “(xxi)”.

        Return STRICTLY:
        Line 1  →  “Yes”  if **exactly one** occurrence of “(xxi)” is found  
                    “No”   if none or more than one are found  
        Line 2  →  The sentence (or heading line) where “(xxi)” appears, or  
                   “Not found” if answer is No
        """

            response = model.generate_content(
                contents=[
                    {"mime_type": "application/pdf", "data": pdf_content},
                    prompt
                ]
            )

            lines = response.text.strip().split("\n")
            if not lines:
                return (False, "No response")

            verdict = lines[0].strip().lower()
            detail = "\n".join(lines[1:]).strip()

            return (verdict == "yes", detail)

        # --- BALANCE‑SHEET SPECIFIC CHECKS -----------------------------------
        elif check_type == "balance_sheet_ppe_value":
            prompt = """
            You are given the Balance Sheet PDF.
            Locate the line (or table row) for “Property, Plant and Equipment” (ignore case).
            Extract the **numeric amount** reported for the current year (ignore the previous‑year column).

            Return STRICTLY in two lines:
            Line 1 → the amount as a plain number (no commas, no currency, no words)
            Line 2 → the exact line you extracted it from.
            """
            resp = model.generate_content(contents=[{"mime_type": "application/pdf", "data": pdf_content}, prompt])
            lines = resp.text.strip().split("\n")
            if not lines:
                return None, "Could not extract PPE value"
            try:
                value = float(re.sub(r"[^0-9.]+", "", lines[0]))
                return value, lines[1] if len(lines) > 1 else "Source not captured"
            except ValueError:
                return None, "First line was not numeric: " + lines[0]
        
        elif check_type == "balance_sheet_intangible_value":
            # ── pull the current-year Intangible-Assets amount reliably ───────
            prompt = """
            You’re given a Balance-Sheet PDF with two side-by-side columns:
            • one headed “As at March 31, 2024”  (current year)
            • one headed “As at March 31, 2023”  (prior year)

            TASK:
            1. Locate the row whose description is exactly “Intangible assets”
            (ignore case), not “under development” or anything else.
            2. Read the two numbers in that row: the first belongs under the
            2024 header, the second under 2023.
            3. RETURN EXACTLY two lines, labeled:
                Line 1 → CURRENT_YEAR: <2024 amount as plain number>
                Line 2 → PRIOR_YEAR:   <2023 amount as plain number>
            (No commas, symbols or extra words.)

            If the row or one of the year-columns is missing, return 0 for that year.
            """
            resp = model.generate_content(
                contents=[{"mime_type": "application/pdf", "data": pdf_content}, prompt]
            )
            lines = resp.text.strip().split("\n")
            # Expecting:
            #   CURRENT_YEAR: 0.000
            #   PRIOR_YEAR:   20.59
            if len(lines) < 2:
                return None, "Could not extract labeled years: " + resp.text.strip()
            # Parse out the numbers after the colons
            try:
                curr = float(lines[0].split(":", 1)[1].strip())
            except:
                curr = 0.0
            # We ignore prior year here
            return curr, lines[0]
        
        # ── BALANCE-SHEET / NOTES: any immovable-property line? ───────────
        elif check_type == "bs_has_immovable_property":
            prompt = """
            Search this PDF (Balance-Sheet or Notes) for rows/lines whose description
            contains any of these keywords (ignore case):

            • "leasehold land"
            • "ROU"  OR  "right of use"
            • "freehold land"
            • "buildings"
            • "investment property"

            If ANY of these appear in the current-year column with a non-zero amount,
            output:

            Line 1 → YES
            Line 2 → the first matching line you found.

            If none appear or all are zero/blank, output:

            Line 1 → NO
            Line 2 → None
            """
            resp = model.generate_content(
                contents=[{"mime_type": "application/pdf", "data": pdf_content}, prompt]
            )
            lines = resp.text.strip().split("\n", 1)
            return (lines[0].strip().upper() == "YES"), (lines[1] if len(lines) > 1 else "None")
        






        # ── BALANCE‑SHEET: INVENTORY (current‑year figure) ───────────────
        elif check_type == "balance_sheet_inventory_value":
            prompt = """
            You are analysing a Balance-Sheet PDF that has two numeric columns:
            • current year  (e.g. “As at 31 March 2024”)
            • prior year    (e.g. “As at 31 March 2023”)

            GOAL → current-year **Inventories** amount.

            VERY STRICT RULES
            ─────────────────
            • Accept **only** a row whose description (ignoring case/spacing) is
            exactly the single word “Inventories” OR starts with “(a) Inventories”.
            • Do NOT accept rows that contain “Other”, “Trade receivables”,
            “Bank”, “Current Assets”, “Financial Assets”, etc.
            • If no such row exists, the amount is **0**.

            WHAT TO RETURN (exactly two lines)
            Line 1 → <amount>   ← plain number; 0 if the row is missing
            Line 2 → <row text> ← the row you used, or “None” if missing
            """
            resp = model.generate_content(
                contents=[{"mime_type": "application/pdf", "data": pdf_content}, prompt]
            )
            lines = resp.text.strip().split("\n")
            if not lines:
                return None, "Could not extract inventory value"
            try:
                value = float(re.sub(r"[^0-9.]+", "", lines[0]))
                return value, lines[1] if len(lines) > 1 else "Source not captured"
            except ValueError:
                return None, "First line was not numeric: " + lines[0]

        elif check_type == "caro_nbfc_iii_clause_present":
            prompt = """
            Scan this CARO Annexure for the sub‑clause labels “(iii)(a)” or “(iii)(e)”.
            Return STRICTLY:
            Line 1 → “Yes” if either label is found, else “No”
            Line 2 → First matching sentence (or “Not found”)
            """
            resp = model.generate_content(
                contents=[{"mime_type": "application/pdf", "data": pdf_content}, prompt]
            )
            verdict, detail = (
                resp.text.strip().split("\n", 1)
                if "\n" in resp.text else (resp.text.strip(), "")
            )
            return verdict.lower() == "yes", detail

       



 

        # ── NOTES: total financial guarantees in contingent liabilities ────
        elif check_type == "notes_contg_fin_guar_total":
            prompt = """
            In the Contingent Liabilities note, locate any line that contains the
            phrase “financial guarantee” or “guarantees given” (ignore case).
            Sum the current-year (31 Mar 2024) amounts for all such lines.

            OUTPUT exactly two lines:
            Line 1 → TOTAL: <plain number>            (0 if none)
            Line 2 → Details: desc1=<amt1>; desc2=<amt2>; …  or “None”
            """
            text = model.generate_content(
                contents=[{"mime_type": "application/pdf", "data": pdf_content}, prompt]
            ).text.strip()

            try:
                total = float(re.search(r"TOTAL:\s*([0-9.]+)", text).group(1))
            except Exception:
                total = 0.0
            return total, text          # two-item tuple


        # ── CARO (iii)(a)(A)+(B) – guarantees + securities balance ────────
        elif check_type == "caro_iii_a_guar_total":
            prompt = """
            Extract both sub-clauses (iii)(a)(A) and (iii)(a)(B) from the CARO
            Annexure, then add their numeric amounts.

            OUTPUT two lines:
            Line 1 → TOTAL: <plain number>
            Line 2 → (A)=<amtA>; (B)=<amtB>  or “Missing”
            """
            text = model.generate_content(
                contents=[{"mime_type": "application/pdf", "data": pdf_content}, prompt]
            ).text.strip()

            try:
                a = float(re.search(r"\(A\)\s*[:\-]?\s*([0-9][0-9,.]*)", text).group(1).replace(",", ""))
                b = float(re.search(r"\(B\)\s*[:\-]?\s*([0-9][0-9,.]*)", text).group(1).replace(",", ""))
                total = a + b
            except Exception:
                total = 0.0
            return total, text



       
        # --- NOTES → do RPT schedules show *loans / advances*? ----------------
        elif check_type == "notes_rpt_has_loans":
            FIN_KEYWORDS   = ("loan", "advance in the nature of loan", "advance")
            EXCLUDE_WORDS  = (
                "lease", "rent", "commission", "service", "salary",
                "purchase", "sale", "trade", "dividend", "interest",
                "equity", "share", "mutual fund"
            )

            prompt = f"""
            Scan the Related–Party Transactions note; return **YES** only if a row
            contains at least one of {FIN_KEYWORDS} (ignore case) *and* none of
            {EXCLUDE_WORDS}.  Output exactly two lines:

                YES
                <verbatim row text>

            or

                NO
                None
            """

            # ask Gemini
            resp_text = model.generate_content(
                contents=[{"mime_type": "application/pdf", "data": pdf_content}, prompt]
            ).text.strip()

            # strip out if it echoed the instruction back
            if resp_text.lower().startswith("return yes only"):
                return False, "None"

            # split into the two expected lines
            lines = resp_text.split("\n", 1)
            found  = lines[0].strip().upper() == "YES"
            detail = lines[1].strip() if len(lines) > 1 else "None"

            # final sanity‐check on keywords
            if found:
                low = detail.lower()
                if not any(kw in low for kw in FIN_KEYWORDS) or any(ex in low for ex in EXCLUDE_WORDS):
                    found = False
                    detail = "None"

            return found, detail




        # ── B/S: opening net-worth = share-capital + reserves – revaluation reserve ──
        elif check_type == "bs_opening_networth":
            prompt = """
            From the Balance-Sheet extract the **previous-year** column
            (31-Mar-<PY>) for:
            • "Equity share capital"  (paid-up capital)
            • "Other equity" or "Reserves & surplus"
            • Any line that contains "revaluation reserve"

            Compute:
                NetWorth = Capital + Reserves_Surplus – RevaluationReserve

            Return two lines ONLY:
            NETWORTH: <number>
            Source: cap=<val1>; reserves=<val2>; reval=<val3>
            """
            resp  = model.generate_content(
                        contents=[{"mime_type": "application/pdf", "data": pdf_content},
                                prompt]).text.strip().split("\n",1)
            val   = float(re.sub(r"[^0-9.]+", "", resp[0])) if resp else 0.0
            src   = resp[1] if len(resp) > 1 else "Source not captured"
            return val, src
        



        # ---------- Secretarial Report  – Section 185/186 qualification ----------
        elif check_type == "secretarial_185_186":
            prompt = """
            Scan this Secretarial Compliance (or Audit) Report for any paragraph
            that mentions **Section 185** or **Section 186** of the Companies Act
            *and* indicates contravention, violation, non-compliance, or a
            qualified opinion (keywords: contravent, violat, non-compli,
            qualified, qualification, "has not complied").

            Return EXACTLY two lines:
            Yes
            <120-char snippet around the finding>

            or

            No
            None
            """
            resp = model.generate_content(
                contents=[{"mime_type":"application/pdf","data":pdf_content}, prompt]
            ).text.strip().split("\n",1)
            return (resp[0].strip().lower()=="yes",
                    resp[1] if len(resp)>1 else "None")

        # ---------- CARO clause (iv) presence in Annexure A ----------------------
        elif check_type == "caro_iv_present":
            prompt = """
            Locate sub-clause heading “(iv)” in this CARO Annexure PDF.
            Return two lines:
            Yes
            <the sub-clause line + first sentence>
            or
            No
            Not found
            """
            resp = model.generate_content(
                contents=[{"mime_type":"application/pdf","data":pdf_content}, prompt]
            ).text.strip().split("\n",1)
            return (resp[0].strip().lower()=="yes",
                    resp[1] if len(resp)>1 else "Not found")

        # ---------- Secretarial Report – Section 42 / 62 qualification ----------
        elif check_type == "secretarial_42_62":
            prompt = """
            Search this Secretarial Compliance (or Audit) Report for any paragraph
            that mentions **Section 42** (private placement) or **Section 62**
            (rights issue) of the Companies Act **and** indicates
            contravention, violation, non-compliance, or a qualification
            (keywords: contravent, violat, non-compli, qualified, qualification,
            "has not complied").

            Return EXACTLY two lines:
            Yes
            <120-character snippet around the finding>

            or

            No
            None
            """
            ok, snip = model.generate_content(
                contents=[{"mime_type":"application/pdf","data":pdf_content}, prompt]
            ).text.strip().split("\n", 1)
            return (ok.strip().lower() == "yes", snip)

        # ---------- CARO clause (x)(b) presence in Annexure A -------------------
        elif check_type == "caro_xb_present":
            prompt = """
            Locate sub-clause heading “(x)(b)” in this CARO Annexure PDF.
            Return two lines:
            Yes
            <heading line + first sentence>
            or
            No
            Not found
            """
            ok, snip = model.generate_content(
                contents=[{"mime_type":"application/pdf","data":pdf_content}, prompt]
            ).text.strip().split("\n", 1)
            return (ok.strip().lower() == "yes", snip)
        
        # ─────────────────────────────────────────────────────────────
        # Secretarial Report → Sec 177 / 188 qualification?
        # ─────────────────────────────────────────────────────────────
        elif check_type == "secretarial_177_188":
            prompt = """
            Scan this Secretarial Audit / Compliance Report for any paragraph that
            mentions **Section 177** or **Section 188** of the Companies Act
            *and* indicates a qualification / non-compliance
            (keywords: contravention, violation, non-compliance, qualified,
            qualification, "has not complied", adverse).

            Return EXACTLY two lines:
            YES
            <90-character snippet around the finding>

            If there is no such qualification, return:
            NO
            None
            """
            verdict, snippet = model.generate_content(
                contents=[{"mime_type":"application/pdf","data":pdf_content}, prompt]
            ).text.strip().split("\n", 1)
            return verdict.strip().upper() == "YES", snippet


        # ─────────────────────────────────────────────────────────────
        # Annexure A → is sub-clause “(xiii)” present?
        # ─────────────────────────────────────────────────────────────
        elif check_type == "caro_xiii_present":
            prompt = """
            Locate sub-clause heading “(xiii)” in this CARO Annexure PDF.
            Return exactly two lines:
            YES
            <heading line + first sentence>    # context
            or
            NO
            Not found
            """
            verdict, snippet = model.generate_content(
                contents=[{"mime_type":"application/pdf","data":pdf_content}, prompt]
            ).text.strip().split("\n", 1)
            return verdict.strip().upper() == "YES", snippet

        # ────────────────────────────────────────────────────────────
        # Secretarial Report  →  Sec-192 qualification?
        # ────────────────────────────────────────────────────────────
        elif check_type == "secretarial_192":
            prompt = """
            Scan this Secretarial Audit / Compliance Report for any paragraph that
            mentions **Section 192** of the Companies Act (non-cash transactions with
            directors) AND indicates contravention / violation / non-compliance or
            contains a qualified opinion (keywords: contravent, violat, non-compli,
            qualified, qualification, "has not complied", adverse).

            Return exactly two lines:
            YES
            <90-character snippet around the finding>
            or
            NO
            None
            """
            verdict, snippet = model.generate_content(
                contents=[{"mime_type": "application/pdf", "data": pdf_content}, prompt]
            ).text.strip().split("\n", 1)
            return verdict.strip().upper() == "YES", snippet


        # ────────────────────────────────────────────────────────────
        # Annexure A  →  is sub-clause “(xv)” present?
        # ────────────────────────────────────────────────────────────
        elif check_type == "caro_xv_present":
            prompt = """
            Locate sub-clause heading “(xv)” in this CARO Annexure PDF.
            Return exactly two lines:
            YES
            <heading line + first sentence>
            or
            NO
            Not found
            """
            verdict, snippet = model.generate_content(
                contents=[{"mime_type": "application/pdf", "data": pdf_content}, prompt]
            ).text.strip().split("\n", 1)
            return verdict.strip().upper() == "YES", snippet

        # ────────────────────────────────────────────────────────────
        # Secretarial Report  →  Sec-135 qualification?
        # ────────────────────────────────────────────────────────────
        elif check_type == "secretarial_135":
            prompt = """
            Search this Secretarial Audit / Compliance Report for any paragraph that
            mentions **Section 135** of the Companies Act (Corporate Social
            Responsibility) AND also contains a sign of non-compliance or a qualified
            opinion (keywords: non-compliance, contravention, violation, qualified,
            qualification, adverse, "has not complied").

            Return exactly two lines:
            YES
            <90-character snippet around the finding>
            or
            NO
            None
            """
            verdict, snippet = model.generate_content(
                contents=[{"mime_type": "application/pdf", "data": pdf_content}, prompt]
            ).text.strip().split("\n", 1)
            return verdict.strip().upper() == "YES", snippet


        # ────────────────────────────────────────────────────────────
        # Annexure A  →  is sub-clause “(xx)” present?
        # ────────────────────────────────────────────────────────────
        elif check_type == "caro_xx_present":
            prompt = """
            Locate sub-clause heading “(xx)” in this CARO Annexure PDF.
            Return exactly two lines:
            YES
            <heading line + first sentence>
            or
            NO
            Not found
            """
            verdict, snippet = model.generate_content(
                contents=[{"mime_type": "application/pdf", "data": pdf_content}, prompt]
            ).text.strip().split("\n", 1)
            return verdict.strip().upper() == "YES", snippet

        
        
        # ────────────────────────────────────────────────────────────────
        # Secretarial Audit → Sections 73–76 qualification check
        # ────────────────────────────────────────────────────────────────
        elif check_type == "secretarial_qual_73_76":
            prompt = """
            Search this Secretarial Audit Report PDF for any qualifications under
            Sections 73 to 76 of the Companies Act (i.e. public deposits).
            Return exactly two lines:
            YES
            <the sentence or paragraph containing that qualification>
            or:
            NO
            None
            """
            resp = model.generate_content(
                contents=[{"mime_type":"application/pdf","data":pdf_content}, prompt]
            ).text.strip().split("\n", 1)
            found = resp[0].strip().upper() == "YES"
            detail = resp[1].strip() if len(resp) > 1 else "None"
            return found, detail


        # ────────────────────────────────────────────────────────────────
        # Notes-to-P&L: Fees paid to Cost Auditor → trigger for clause (vi)
        # ────────────────────────────────────────────────────────────────
        elif check_type == "notes_cost_auditor_fees":
            prompt = """
            Search this Notes-to-Accounts PDF for any mention of fees paid to the Cost Auditor.
            Return exactly two lines:
            YES
            <the sentence or table row containing 'Cost Auditor' or 'fees paid to Cost Auditor'>
            or, if not found:
            NO
            None
            """
            resp = model.generate_content(
                contents=[{"mime_type": "application/pdf", "data": pdf_content}, prompt]
            ).text.strip().split("\n", 1)
            found = resp[0].strip().upper() == "YES"
            detail = resp[1].strip() if len(resp) > 1 else "None"
            return found, detail

        
        # ─────────────────────────────────────────────────────────────
        # Full Annual Report → immovable-property disputes?
        # ─────────────────────────────────────────────────────────────
        elif check_type == "annual_immovable_dispute":
            prompt = """
            Search this full Annual Report (including board report, MD&A, etc.) for
            any paragraph that simultaneously mentions a DISPUTE / LITIGATION /
            LEGAL PROCEEDING and an IMMOVABLE PROPERTY word such as
            land, building, leasehold, freehold, "right of use", ROU, investment
            property, or title-deed issues.

            Return exactly two lines:
            YES
            <120-character snippet around the finding>
            or
            NO
            None
            """
            verdict, snippet = model.generate_content(
                contents=[{"mime_type": "application/pdf", "data": pdf_content}, prompt]
            ).text.strip().split("\n", 1)
            return verdict.strip().upper() == "YES", snippet

        elif check_type == "caro_clause_present":

            labels = parameters.get("label")
            # Support both a single string or a list of labels
            if isinstance(labels, str):
                labels = [labels]

            results = {}
            def normalize(s):
                return re.sub(r"\s+", "", s).lower()

            for label in labels:
                prompt = f"""
                Locate the CARO clause whose heading matches exactly "{label}" (ignore spaces/case).
                Return exactly two lines:
                    PRESENT
                    <clause heading + next two lines>
                or
                    ABSENT
                    None
                Only return PRESENT if the heading matches "{label}" exactly (ignoring spaces/case).
                """
                resp = model.generate_content(
                    contents=[{"mime_type": "application/pdf", "data": pdf_content}, prompt]
                ).text.strip().split("\n", 1)

                present = resp[0].strip().upper() == "PRESENT"
                context = resp[1].strip() if len(resp) > 1 else "None"

                # Post-process: check that the heading matches exactly
                heading_line = context.split("\n", 1)[0].split(".", 1)[0]
                if present and normalize(heading_line).startswith(normalize(label)):
                    results[label] = (True, context)
                else:
                    results[label] = (False, "None")

            # If only one label was checked, return a single result for backward compatibility
            if len(results) == 1:
                return list(results.values())[0]
            else:
                return results

        elif check_type == "bs_secured_borrowings_gemini":
            prompt = """
            You are given a Balance Sheet PDF.
            1) Find the section headed "Current Liabilities" (case-insensitive).
            2) Within that section, locate the row whose label is "(i) Borrowings" 
            (ignore spacing/case).
            3) Identify the amount in the column that corresponds to the current year
            (i.e. the first numeric column after the label).
            Return exactly two lines:
            AMOUNT: <number as plain number, in crores>
            LINE:   <the full row text including label and both columns>
            """
            resp = model.generate_content(
                contents=[{"mime_type":"application/pdf","data":pdf_content}, prompt]
            ).text.strip().split("\n", 1)

            # parse out the amount
            amt = 0.0
            if resp:
                line0 = resp[0].replace("AMOUNT:", "").strip()
                try:
                    amt = float(line0)
                except ValueError:
                    amt = 0.0

            detail = resp[1].strip() if len(resp) > 1 else ""
            return amt, detail



        elif check_type == "caro_revaluation_clause_id":
            prompt = """
                Read this PDF (it may be Balance-Sheet or Notes).
                If you find ANY of the following **for the CURRENT year**:
                1. A row in PPE / Intangible / ROU note that says
                    "Revaluation" (additions or deletions), OR
                2. A movement in "Revaluation Reserve" within other equity
                    (either increase or decrease),
                then output exactly two lines:
                YES
                <verbatim line that shows the revaluation>
                Otherwise output:
                NO
                None
                """
            resp = model.generate_content(
                contents=[{"mime_type": "application/pdf", "data": pdf_content}, prompt]
            )
            lines = resp.text.strip().split("\n", 1)
            verdict = lines[0].strip().upper() == "YES"
            explanation = lines[1].strip() if len(lines) > 1 else ""
            return verdict, explanation

        # ——————————————————————————————————————————————
        # A) Notes provision check
        # ——————————————————————————————————————————————
        elif check_type == "notes_provision_doubtful_loans":
            prompt = """
            Search this Notes-to-Accounts PDF for any mention of
            “provision for doubtful loans” or “provision for doubtful advances”.
            Important instructions please look at the exact sentence through out the document. if you find one of this “provision for doubtful loans” or “provision for doubtful advances”
            Don't use the general knowledge and Don't check any other if you fined one of this line please if you find keep yes
            YES
            <the sentence or table row containing the provision>
            or, if not found:
            NO
            None
            """
            resp = model.generate_content(
                contents=[
                    {"mime_type": "application/pdf", "data": pdf_content},
                    prompt
                ]
            ).text.strip().split("\n", 1)
            found = resp[0].strip().upper() == "YES"
            detail = resp[1].strip() if len(resp) > 1 else "None"
            return found, detail



        elif check_type == "find_benami":
            prompt = """
            Scan the entire PDF for any mention of the word "benami" (case-insensitive).  
            If found, return:
            YES
            <snippet: 60 characters before and after the word, with newlines replaced by spaces>
            If not found, return:
            NO
            None
            """
            verdict, snippet = model.generate_content(
                contents=[{"mime_type": "application/pdf", "data": pdf_file.getvalue()}, prompt]
            ).text.strip().split("\n", 1)
            return (verdict.strip().upper() == "YES", snippet.strip())


        elif check_type == "bs_stat_dues_same":
            prompt = """
            You are given a Balance Sheet PDF.
            1) Locate the sections headed "Provisions" or "Other Liabilities" (case‐insensitive).
            2) Within those sections, find any rows whose description contains any of:
            GST, Provident Fund, ESI, Income Tax, TDS, Sales Tax, Service Tax,
            Customs Duty, Excise Duty, VAT, Cess.
            3) For each such row, compare the current-year amount to the previous-year amount.
            4) If any row has the exact same number in both columns, output:
                YES
                <the full row text including both year-columns>
            Otherwise output:
                NO
                None
            """
            resp = model.generate_content(
                contents=[
                    {"mime_type":"application/pdf", "data": pdf_content},
                    prompt
                ]
            ).text.strip().split("\n", 1)
            present = resp[0].strip().upper() == "YES"
            context = resp[1].strip() if len(resp) > 1 else "None"
            return present, context

        # ────────────────────────────────────────────────────────────────
        # Notes: extract up to 3 statutory-dues items via Gemini
        # ────────────────────────────────────────────────────────────────
        elif check_type == "notes_stat_dues_gemini":
            prompt = """
            You have the Notes-to-Accounts PDF.
            In the sections headed “Provisions” or “Other Liabilities”,
            find up to three separate
            line items that mention any of these statutory-dues keywords:
            GST, Provident Fund, ESI, Income Tax, TDS, Sales Tax, Service Tax,
            Customs Duty, Excise Duty, VAT, Cess.

            Return a numbered list, one per line, like:
            1) <first line>
            2) <second line>
            3) <third line>

            If there are fewer than three, just list those you find.
            If none, return exactly the single line:
            None
            """
            resp = model.generate_content(
                contents=[
                    {"mime_type":"application/pdf","data":pdf_content},
                    prompt
                ]
            ).text.strip().split("\n")
            # clean up
            items = [ln.strip() for ln in resp if ln.strip()]
            return items


        elif check_type == "bs_curr_liab_vs_asset":
                
                # Enhanced Gemini prompt: extract and sum all current‐liabilities and current‐assets values
                prompt = """
                    You are given a Balance-Sheet PDF with two side-by-side columns: 
                    • one headed “As at March 31, 2024”  (current year)
                    • one headed “As at March 31, 2023”  (prior year)

                    TASK:
                    1. Under the heading “Current Liabilities”, find every numeric amount in the current-year column.
                    2. Under the heading “Current Assets”, find every numeric amount in the current-year column.
                    3. Compute the sum of the Current Liabilities amounts and the sum of the Current Assets amounts.

                    RETURN EXACTLY two lines (no commas or currency symbols):
                    Line 1 → LIABILITIES: <sum_of_liabilities>
                    Line 2 → ASSETS: <sum_of_assets>
                    """
                # Call Gemini
                resp_text = model.generate_content(
                    contents=[
                        {"mime_type": "application/pdf", "data": pdf_content},
                        prompt
                    ]
                ).text.strip()

                # Expecting something like:
                # LIABILITIES: 1032.90
                # ASSETS:  987.65
                lines = [ln.strip() for ln in resp_text.splitlines() if ln.strip()]

                # Parse out the numbers after the colons
                liab = 0.0
                asset = 0.0
                if len(lines) >= 1:
                    m = re.search(r"LIABILITIES:\s*([0-9.]+)", lines[0], re.I)
                    if m:
                        liab = float(m.group(1))
                if len(lines) >= 2:
                    m = re.search(r"ASSETS:\s*([0-9.]+)", lines[1], re.I)
                    if m:
                        asset = float(m.group(1))

                return liab, asset

        #bs has public deposit-caro
        elif check_type == "bs_has_public_deposits":
            prompt = """
            You are given a Balance Sheet PDF. Your task is to determine if any borrowings under "Non-Current Liabilities" or "Current Liabilities" include "fixed deposits" (case-insensitive).

            Instructions:
            1. Locate the sections titled "Non-Current Liabilities" and "Current Liabilities".
            2. Within each section, look for any line or row that contains both:
            - The word "borrowing" (or "borrowings") (case-insensitive)
            - The phrase "fixed deposit" (case-insensitive)
            3. If you find any such line, clause (v) of CARO should be included.

            Return STRICTLY:
            - Line 1: "YES" if any such line is found, otherwise "NO"
            - Line 2: The first matching line you found (verbatim), or "None" if not found
            """
            resp = model.generate_content(
                contents=[{"mime_type": "application/pdf", "data": pdf_content}, prompt]
            )
            lines = resp.text.strip().split("\n", 1)
            found = lines[0].strip().upper() == "YES"
            context = lines[1].strip() if len(lines) > 1 else "None"
            return found, context

        elif check_type == "notes_has_goods_in_transit":
            prompt = """
            You are given a Notes to Accounts PDF from a company's financial statements.

            Your task:
            1. Locate the note titled "9. Inventories" (the heading may appear as "9. Inventories", "9) Inventories", or "9 Inventories", case-insensitive).
            2. Within this section, search for any line that contains the phrase "goods in transit" or "stock in transit" (with or without hyphens, case-insensitive).
            3. If you find such a line, extract the amount shown in that line (look for a number followed by "crores", "₹", or "Rs.").
            4. If found, return:
            - Line 1: "YES"
            - Line 2: The full line you found, followed by the extracted value in parentheses (e.g., "Goods in transit ... ₹12.5 crores (12.5 Cr)")
            5. If not found, return:
            - Line 1: "NO"
            - Line 2: "None"

            Additionally, answer: If goods in transit is shown under the note on inventory, does clause (ii)(a) in CARO cover its verification also? Answer in one line after the above output.
            """
            resp = model.generate_content(
                contents=[{"mime_type": "application/pdf", "data": pdf_content}, prompt]
            )
            lines = resp.text.strip().split("\n")
            found = lines[0].strip().upper() == "YES"
            detail = lines[1].strip() if len(lines) > 1 else "None"
            explanation = lines[2].strip() if len(lines) > 2 else ""
            return found, f"{detail}\n{explanation}"





        # ────────────────────────────────────────────────────────────────
        # Notes-to-Accounts: up to 3 non-cash RPT lines → CARO clause (xv)
        # ────────────────────────────────────────────────────────────────
        elif check_type == "notes_rpt_noncash_multiple":
            prompt = """
            Scan the Related-Party Transactions PDF for any non-cash transactions
            (i.e., transfers of goods, services or assets without explicit monetary consideration).
            Return up to three lines describing each transaction, or exactly 'None' if none found.
            """
            resp = model.generate_content(
                contents=[
                    {"mime_type": "application/pdf", "data": pdf_content},
                    prompt
                ]
            ).text.strip()
            # Split into lines and limit to first 3
            lines = [ln.strip() for ln in resp.split("") if ln.strip()]
            return lines[:3] if lines and lines[0].upper() != "NONE" else []

        
        # ────────────────────────────────────────────────────────────────
        # Notes-to-Accounts: Related-party loans → CARO clauses (ix)(e)/(f)
        # ────────────────────────────────────────────────────────────────
        elif check_type == "notes_rpt_loans_taken":
            prompt = """
            You have the Notes-to-Accounts PDF.
            In the Related-Party-Transactions section, look for any row or sentence
            Check the "Loan Taken" indicating that the company has taken a loan or advance from a related party.
            Make sure the extract from the value from the row of "Loan Taken"
            

            Return exactly one line:
            YES
            <The table row containing the Loan taken>
            or:
            NO
            None
            """
            resp = model.generate_content(
                contents=[
                    {"mime_type": "application/pdf", "data": pdf_content},
                    prompt
                ]
            ).text.strip().split("\n", 1)

            found  = resp[0].strip().upper() == "YES"
            detail = resp[1].strip() if len(resp) > 1 else "None"
            return found, detail




        elif check_type == "inventory_writeoff_gemini":
            pl_notes_file = parameters.get("pl_notes_file")
            notes_file = parameters.get("notes_file")

            prompt = """
        Scan the provided Profit & Loss Notes and Notes to Accounts PDFs for any mention of "write-off of inventory", "inventory write-off", or "write-down of inventory" for the current year.
        If found, extract the sentence or table row containing the phrase. If not found, return "None" for evidence.
        Return STRICTLY:
        Line 1: "Yes" if a write-off is found, "No" otherwise.
        Line 2: "Write-off evidence: <sentence or None>"
        Line 3: Short explanation (≤40 words)
        """

            contents = []
            if pl_notes_file:
                contents.append({"mime_type": "application/pdf", "data": pl_notes_file.getvalue()})
            if notes_file:
                contents.append({"mime_type": "application/pdf", "data": notes_file.getvalue()})
            contents.append(prompt)

            resp = model.generate_content(contents=contents)
            lines = resp.text.strip().split("\n")
            if len(lines) < 2:
                return (False, "Incomplete response from Gemini")
            verdict = lines[0].strip().lower() == "yes"
            details = "\n".join(lines[1:]).strip()
            return (verdict, details)

        # ────────────────────────────────────────────────────────────────
        # Balance Sheet: Deemed deposits → clause (v)
        # ────────────────────────────────────────────────────────────────
        elif check_type == "deemed_deposits":
            prompt = """
            You have a Balance Sheet PDF. Search for these three line-items:
            • Advance from customers
            • Security deposit
            • Advance for sale of immovable property

            For each one that appears, extract the two numbers on that row:
            – the current-year amount
            – the prior-year amount

            Return one line per item found, in this exact format:
            <Item>: <Current> | <Prior>
            If an item isn’t present, omit it.
            """
            response = model.generate_content(
                contents=[
                    {"mime_type": "application/pdf", "data": pdf_content},
                    prompt
                ]
            ).text.strip()

            # Parse the lines into a list of dicts
            results = []
            for line in response.splitlines():
                # Expect e.g. "Advance from customers: 12.34 | 12.34"
                parts = line.split(":", 1)
                if len(parts) != 2:
                    continue
                item = parts[0].strip()
                nums = parts[1].split("|")
                if len(nums) != 2:
                    continue
                cur = nums[0].strip()
                prev = nums[1].strip()
                results.append({
                    "item": item,
                    "current": cur,
                    "prior": prev,
                })
            return results

        elif check_type == "notes_csr_unspent":
                    prompt = """
                    You have the Notes-to-Accounts PDF.
                    1. Find the “Corporate Social Responsibility” or “CSR” note.
                    2. Within that note, determine if any amount was unspent from the previous year
                    on completed CSR projects (i.e. excluding ongoing projects) as of the previous year-end.
                    3. If unspent amounts exist, identify whether they were:
                        a) transferred to the specified funds,
                        b) not transferred at all, or
                        c) transferred late (after the due date) in the current year.
                    Return exactly three lines, in this format:
                    UNPELAT: <Amount>  – <one of “Transferred on time” / “Transferred late” / “Not transferred”>
                    If no unspent CSR amount from previous year, return exactly:
                    None
                    """
                    resp = model.generate_content(
                        contents=[
                            {"mime_type": "application/pdf", "data": pdf_content},
                            prompt
                        ]
                    ).text.strip()
                    # split into non-blank lines
                    lines = [ln.strip() for ln in resp.splitlines() if ln.strip()]
                    # if Gemini says "None", treat as no unspent
                    if len(lines)==1 and lines[0].lower()=="none":
                        return None, None
                    # else return the single UNPELAT line
                    # e.g. "UNPELAT: 5.00 Cr – Transferred late"
                    return True, lines[0]    

        elif check_type == "notes_csr_unspent_ongoing":
            prompt = """
            You have the “Corporate Social Responsibility” note from the Notes-to-Accounts PDF.
            1. Within the CSR note, locate any line that refers to amounts earmarked for ongoing CSR projects
               at the end of the previous year.
            2. Determine if those amounts were:
                 a) transferred on time to the Special Account under Section 135(6),
                 b) transferred late, or
                 c) not transferred at all during the current year.
            Return exactly one line in this format:
              ONGOING: <Amount> – <“Transferred on time” / “Transferred late” / “Not transferred”>
            If no unspent amounts for ongoing projects exist, return exactly:
              None
            """
            resp = model.generate_content(
                contents=[
                    {"mime_type": "application/pdf", "data": pdf_content},
                    prompt
                ]
            ).text.strip()

            lines = [ln.strip() for ln in resp.splitlines() if ln.strip()]
            if len(lines) == 1 and lines[0].lower() == "none":
                return None, None
            # parse the single ONGOING line
            return True, lines[0]


        elif check_type == "notes_csr_current_unspent":
            prompt = """
            You have the Corporate Social Responsibility note from the Notes-to-Accounts PDF.
            1. Locate any line that shows an unspent amount for CSR activities
            (excluding ongoing projects) as of the current year-end.
            2. Determine whether that unspent amount was:
                a) transferred on time to the Special Account under Section 135(6),
                b) transferred late in the current year, or
                c) not transferred at all.
            Return **exactly** one line in this format:
            CURRENT_UNSPENT: <Amount> – <“Transferred on time” / “Transferred late” / “Not transferred”>
            If there is no unspent amount (excluding ongoing projects), return exactly:
            None
            """
            resp = model.generate_content(
                contents=[
                    {"mime_type": "application/pdf", "data": pdf_content},
                    prompt
                ]
            ).text.strip()
            lines = [ln.strip() for ln in resp.splitlines() if ln.strip()]
            if len(lines) == 1 and lines[0].lower() == "none":
                return None, None
            return True, lines[0]

        elif check_type == "notes_csr_current_ongoing_unspent":
            prompt = """
            You have the Corporate Social Responsibility note from the Notes-to-Accounts PDF.
            1. Within the CSR note, find any line showing an unspent amount for ongoing CSR projects
            as of the current year-end.
            2. Determine if that amount was:
                a) transferred on time to the Special Account under Section 135(6),
                b) transferred late in the year, or
                c) not transferred at all.
            Return exactly one line in this format:
            CURRENT_ONGOING: <Amount> – <“Transferred on time” / “Transferred late” / “Not transferred”>
            If there is no ongoing-project unspent amount, return exactly:
            None
            """
            resp = model.generate_content(
                contents=[
                    {"mime_type": "application/pdf", "data": pdf_content},
                    prompt
                ]
            ).text.strip()
            lines = [ln.strip() for ln in resp.splitlines() if ln.strip()]
            if len(lines) == 1 and lines[0].lower() == "none":
                return None, None
            return True, lines[0]



        else:
            # Default general extraction
            prompt = "Extract all text content from this PDF document."
            response = model.generate_content(
                contents=[
                    {
                        "mime_type": "application/pdf",
                        "data": pdf_content
                    },
                    prompt
                ]
            )
            return response.text

        # Add processing indication to the UI
        with st.spinner(f"Processing {check_type} check..."):
            # Process the PDF content using Gemini
            response = model.generate_content(
                contents=[
                    {
                        "mime_type": "application/pdf",
                        "data": pdf_content
                    },
                    prompt
                ]
            )

            # Process the response for compliance check
            response_text = response.text.strip()
            lines = response_text.split('\n')

            if not lines:
                return (False, "No response received from analysis")

            first_line = lines[0].strip().lower()
            explanation = "\n".join(lines[1:]).strip() if len(lines) > 1 else "No explanation provided"

            # For company name consistency check, extract variations
            if check_type == "company_name_consistency":
                variations = []
                in_variation = False
                current_variation = {"text": "", "context": ""}

                for line in lines[3:]:  # Skip the first 3 lines (Yes/No, count, variations count)
                    if line.startswith("VARIATION:"):
                        if current_variation["text"] and current_variation["context"]:
                            variations.append(current_variation.copy())
                        current_variation = {"text": line[10:].strip(), "context": ""}
                        in_variation = True
                    elif line.startswith("CONTEXT:") and in_variation:
                        current_variation["context"] = line[8:].strip()
                        in_variation = False
                    elif in_variation and current_variation["text"]:
                        current_variation["text"] += " " + line.strip()
                    elif not in_variation and current_variation["context"]:
                        current_variation["context"] += " " + line.strip()

                # Add the last variation if it exists
                if current_variation["text"] and current_variation["context"]:
                    variations.append(current_variation)

                # Store the variations in session state for highlighting
                st.session_state.company_name_issues = variations

            # Determine compliance
            if first_line == "yes":
                return (True, explanation)
            elif first_line == "no":
                return (False, explanation)
            else:
                # If response doesn't start with Yes/No, assume non-compliant
                return (False, f"Could not determine compliance. Response: {response_text[:200]}...")





    except Exception as e:
        error_message = f"Error processing PDF for {check_type} check: {str(e)}"
        print(error_message)
        return (False, error_message)


##########################
# Profit/Loss Validation Function
##########################
def validate_profit_loss(pdf_file, model, expected_type):
    """
    Validate if the PDF mentions the expected profit/loss type in the Opinion section.

    Args:
        pdf_file: The uploaded PDF file
        model: Gemini model instance
        expected_type: "Profit" or "Loss"

    Returns:
        tuple: (is_compliant, evidence_text, financial_year, explanation)
    """
    pdf_content = pdf_file.getvalue()
    opposite_type = "loss" if expected_type.lower() == "profit" else "profit"

    # Direct prompt focused on finding profit/loss mentions in the Opinion paragraph
    prompt = f"""
    TASK: Validate if the Opinion paragraph mentions '{expected_type.lower()}' or '{opposite_type}' for the financial year.

    SPECIFIC INSTRUCTIONS:
    1. First, locate the "INDEPENDENT AUDITORS' REPORT" section.

    2. Within this section, find the paragraph under the "Opinion" heading.
       - This paragraph typically starts right after "Opinion" and ends before the next heading.

    3. Within this Opinion paragraph ONLY, analyze if it explicitly mentions '{expected_type.lower()}' or '{opposite_type}' for a specific financial year.
       - Look for phrases like:
         * "the profit/loss for the year ended [date]"
         * "the profit/loss and other comprehensive income"

    4. CRITICAL: You must determine if the document mentions the EXACT WORD '{expected_type.lower()}' or '{opposite_type}'. 
       - If it mentions '{expected_type.lower()}', the document IS COMPLIANT with the user's expectation
       - If it mentions '{opposite_type}' instead, the document IS NOT COMPLIANT with the user's expectation

    5. Identify the specific financial year mentioned (e.g., "31 March 2023").

    PROVIDE EXACTLY:
    - First line: Just "COMPLIANT" (if '{expected_type.lower()}' is mentioned) or "NON-COMPLIANT" (if '{opposite_type}' is mentioned instead)
    - Second line: The exact quote from the Opinion paragraph containing the relevant word (whether compliant or not)
    - Third line: The financial year mentioned (e.g., "Financial Year: 31 March 2023")
    - Fourth line: One of these explanations:
      * "Expected '{expected_type.lower()}', found '{expected_type.lower()}'" (if compliant)
      * "Expected '{expected_type.lower()}', found '{opposite_type}'" (if not compliant)
    """

    with st.spinner("Analyzing profit/loss..."):
        response = model.generate_content(
            contents=[
                {"mime_type": "application/pdf", "data": pdf_content},
                prompt
            ]
        )

    response_text = response.text.strip()
    lines = response_text.split('\n')

    if not lines:
        return False, "No response received from analysis", "Unknown", "Analysis failed"

    # Process response
    first_line = lines[0].strip().upper()
    evidence = lines[1].strip() if len(lines) > 1 else "No specific evidence provided"

    # Extract financial year if provided
    financial_year = "Not specified"
    if len(lines) > 2:
        year_line = lines[2].strip()
        if "Financial Year:" in year_line:
            financial_year = year_line.replace("Financial Year:", "").strip()
        else:
            financial_year = year_line

    # Get explanation if provided
    explanation = lines[3].strip() if len(lines) > 3 else "No detailed explanation"

    is_compliant = (first_line == "COMPLIANT")

    return is_compliant, evidence, financial_year, explanation


##########################
# Add/Edit/Delete
##########################
def add_question():
    question = st.session_state.new_question
    prompt = st.session_state.new_prompt
    if question and prompt:
        q_id = f'q{st.session_state.next_question_id}'
        st.session_state.questions[q_id] = {
            'question': question,
            'prompt': prompt
        }
        st.session_state.next_question_id += 1
        save_questions(st.session_state.questions)
        st.session_state.new_question = ''
        st.session_state.new_prompt = ''


def delete_question(q_id):
    if q_id in st.session_state.questions:
        del st.session_state.questions[q_id]
        if q_id in st.session_state.answers:
            del st.session_state.answers[q_id]
        save_questions(st.session_state.questions)


def update_question(q_id):
    st.session_state.questions[q_id]['question'] = st.session_state[f'edit_question_{q_id}']
    st.session_state.questions[q_id]['prompt'] = st.session_state[f'edit_prompt_{q_id}']
    save_questions(st.session_state.questions)


##########################
# Login Page – shiny edition
##########################
def login_page():
    # ---------- GLOBAL CSS ----------
    st.markdown(
        """
        <style>
        /* -------------------------------------------------- HERO */
        .hero{
            background: radial-gradient(circle at 20% 20%, #004c99 0%, #002e5d 100%);
            color:#fff;
            padding:3rem 2rem 7rem 2rem;
            border-radius:0 0 30% 30%/10%;
            text-align:center;
            position:relative;               /* so ::after can be absolutely positioned */
            overflow:hidden;                 /* hide shine overflow */
            box-shadow:0 10px 25px rgba(0,0,0,.3);
        }
        .hero h1{margin:0;font-size:2.6rem;font-weight:700;}
        .hero h2{margin:.4rem 0 0;font-size:1.75rem;font-weight:500;letter-spacing:.04rem;} /* ↑ bigger */
        .hero h3{margin:.9rem 0 0;font-size:1.6rem;font-weight:600;}
        .hero p.tagline{font-size:.9rem;margin-top:1.4rem;color:#dce6ff;letter-spacing:.03rem;}

        /* ► SHINE animation on hero */
        .hero::after{
            content:"";
            position:absolute;
            top:0;left:-150%;
            width:200%;height:100%;
            background:linear-gradient(120deg,transparent 30%,rgba(255,255,255,.3) 50%,transparent 70%);
            animation:shine 8s linear infinite;
        }
        @keyframes shine{
            0%{transform:translateX(0);}
            100%{transform:translateX(75%);}
        }

        /* -------------------------------------------------- GLASS CARD */
        .glass{
            backdrop-filter:blur(12px)saturate(150%);
            -webkit-backdrop-filter:blur(12px)saturate(150%);
            background:rgba(255,255,255,.25);
            border-radius:16px;
            border:1px solid rgba(255,255,255,.4);
            box-shadow:0 4px 20px rgba(0,0,0,.15);
            padding:2rem 2.5rem;
            max-width:380px;
            margin:-2.5rem auto 0 auto;
            position:relative;z-index:10;
        }
        .glass label,.glass button{font-weight:600;}

        /* ► PULSE on Login button */
        .glass button[kind="primary"]{
            width:100%;border-radius:25px;padding:.6rem 0;font-size:1.05rem;
            animation:pulse 2.8s ease-in-out infinite;
        }
        @keyframes pulse{
            0%,100%{box-shadow:0 0 0 0 rgba(0,188,255,.4);}
            50%{box-shadow:0 0 15px 5px rgba(0,188,255,.25);}
        }

        /* -------------------------------------------------- STREAMLIT CHROME */
        header[data-testid="stHeader"]{visibility:hidden;}
        footer{visibility:hidden;}
        </style>
        """,
        unsafe_allow_html=True
    )

    # ---------- HERO BANNER ----------
    st.markdown(
        """
        <section class="hero">
            <h1>PKF&nbsp;Sridhar&nbsp;&amp;&nbsp;Santhanam&nbsp;LLP</h1>
            <h2>Chartered&nbsp;Accountants</h2>
            <h3>Audit&nbsp;–&nbsp;Checklist&nbsp;AI</h3>
            <p class="tagline">
                Developed&nbsp;by&nbsp;<strong>CAP&nbsp;CORPORATE&nbsp;AI&nbsp;SOLUTIONS&nbsp;LLP</strong>
            </p>
        </section>
        """,
        unsafe_allow_html=True
    )

    # ---------- LOGIN CARD ----------
    st.markdown('<div class="glass">', unsafe_allow_html=True)
    st.subheader("Secure Login", anchor=False)

    username = st.text_input("Username")
    password = st.text_input("Password", type="password")

    if st.button("Login", type="primary"):
        if verify_password(username, password):
            st.session_state.logged_in = True
            st.session_state.username = username
            st.session_state.current_page = 'analyze'
            st.success("Login successful!")
            st.rerun()
        else:
            st.error("Invalid username or password")

    st.markdown(
        """
        <p style="text-align:center;font-size:.8rem;margin-top:1.5rem;">
            <em>Default credentials – user: <code>admin</code>&nbsp;|&nbsp;pass: <code>admin</code></em>
        </p>
        """,
        unsafe_allow_html=True
    )
    st.markdown('</div>', unsafe_allow_html=True)




##########################
# MAIN Analysis Page
##########################

def analysis_page():
    st.title("Stage 1 & 2 – Audit Report Disclosure Analyzer")
    st.subheader("Developed by CAP CORPORATE AI SOLUTIONS LLP")
    st.sidebar.success(f"Logged in as: {st.session_state.username}")
    st.sidebar.button("Logout", on_click=lambda: logout())

    # ── Engagement Parameters ─────────────────────────────────────────────────
    company_name = st.text_input("Enter Company Name:", key="company_name")

    # Row 1
    col1, col2, col3 = st.columns(3)
    with col1:
        profit_or_loss = st.selectbox("📈 Outcome for the year", ["Profit", "Loss"], key="profit_or_loss")
    with col2:
        company_listing_status = st.selectbox("🏷️ Company status", ["Unlisted", "Listed"],index=1, key="company_listing_status")
    with col3:
        audit_opinion_type = st.selectbox("🖋️ Opinion type", ["Unmodified", "Qualified", "Adverse", "Disclaimer"], key="audit_opinion_type")

    # Row 2
    col4, col5, col6 = st.columns(3)
    with col4:
        audit_report_type = st.selectbox("📑 Report variant", ["Normal", "Consolidated", "Standalone"], key="audit_report_type")
    with col5:
        top_1000_or_500 = st.selectbox(
            "🏆 In Top-1000 / Top-500?",
            ["Yes", "No"],
            key="top_1000_or_500")
    with col6:
        is_nbfc = st.selectbox("🏢 Is the entity an NBFC / Financial Institution?", ["No", "Yes"], index=0,key="is_nbfc")

    # Date input
    audit_report_date = st.date_input("Enter Audit Report Date:", key="audit_report_date")

    st.markdown("---")

    # ── File Uploaders ─────────────────────────────────────────────────────────
    st.write("### Upload Required Documents")
    audit_report_file  = st.file_uploader("1. Audit Report", type=['pdf'], key="file_audit_report")
    annexure_a_file    = st.file_uploader("2. Annexure A (CARO)", type=['pdf'], key="file_annexure_a")
    annexure_b_file    = st.file_uploader("3. Annexure B (IFC)", type=['pdf'], key="file_annexure_b")
    balance_sheet_file = st.file_uploader("4. Balance Sheet", type=['pdf'], key="file_balance_sheet")
    notes_file         = st.file_uploader("5. Notes to Accounts", type=['pdf'], key="file_notes")
    pl_notes_file      = st.file_uploader("6. Profit & Loss Account", type="pdf", key="file_pl_notes")
    annual_report_file = st.file_uploader("7. Full Annual Report",         type=["pdf"], key="file_annual_report")  # NEW
    secretarial_comp_file = st.file_uploader("8. Secretarial Compliance Report",type=['pdf', 'doc', 'docx'],key="file_secretarial_compliance")
    csr_notes_file      = st.file_uploader("8. CSR Note (Notes to Accounts)", type=['pdf'], key="file_csr_notes")  # ← NEW


    if (any([audit_report_file, annexure_a_file, annexure_b_file, balance_sheet_file, notes_file, pl_notes_file , annual_report_file , secretarial_comp_file , csr_notes_file])
        and not company_name):
        st.warning("Please enter the company name to proceed with analysis")
        return

    run_all = st.button("▶️ Run All Checks", key="run_all")

    if not audit_report_file:
        st.info("Please upload the Audit Report PDF to run checks.")
        return

    # initialize
    model = initialize_gemini()
    st.session_state.analysis_log = []

    # extract text
    pdf_text = process_pdf_for_check(audit_report_file, model, "general") or (False, "")
    if not pdf_text.strip():
        st.warning("No text could be extracted from the PDF.")
        return

    st.markdown("## Analysis Results")

    # ──────────────────────────────────────────────────────────────
    # 1) Independent
    # ──────────────────────────────────────────────────────────────
    st.header("1) Independent")

    # 1.1 Audit Report
    st.subheader("1.1 Audit Report")
    with st.expander("Preview: Audit Report Text"):
        st.text_area("PDF Content", pdf_text, height=200, disabled=True)

    # Check 1
    if run_all or st.button("Run Check: To the Members format", key="btn1"):
        st.markdown(f"**Check 1: 'To the Members of {company_name}' format**")
        ok, reason = process_pdf_for_check(
            audit_report_file, model, "company_format", {"company_name": company_name}
        )
        st.write("✅ **Compliant**" if ok else "❌ **Non-Compliant**")
        st.write(f"**Reason:** {reason or 'N/A'}")

    # Check 2
    if run_all or st.button("Run Check: Profit/Loss in Opinion", key="btn2"):
        st.markdown(f"**Check 2: Profit or Loss in Opinion** (Selected: {profit_or_loss})")
        ok, quote, fy, analysis = validate_profit_loss(
            audit_report_file, model, profit_or_loss
        )
        st.write("✅ **Compliant**" if ok else "❌ **Non-Compliant**")
        st.markdown(f"**Quote:** {quote}  \n**Financial Year:** {fy}  \n**Analysis:** {analysis}")
        # highlight
        hl = re.sub(r'(?i)(profit)', r'<mark>\1</mark>', quote)
        hl = re.sub(r'(?i)(loss)', r'<mark>\1</mark>', hl)
        st.markdown("### Highlighted Evidence")
        st.markdown(hl, unsafe_allow_html=True)

    # Check 3 (BRSR / BRR) for listed
    if company_listing_status == "Listed":
        if run_all or st.button("Run Check: BRSR vs BRR", key="btn3"):
            st.markdown(f"**Check 3: BRSR vs BRR** (Top-1000/500 = {top_1000_or_500})")
            ok, reason = process_pdf_for_check(
                audit_report_file, model, "brsr_brr", {"top_1000_500": top_1000_or_500}
            )
            st.write("✅ **Compliant**" if ok else "❌ **Non-Compliant**")
            st.write(f"**Reason:** {reason}")
    else:
        st.markdown("**Check 3: BRSR / BRR – skipped (Unlisted)**")

    # Check 4 (Signature + Date)
    if run_all or st.button("Run Check: Signature + Date", key="btn4"):
        st.markdown("**Check 4: PKF Sridhar & Santhanam LLP Signature + Date**")
        ok, reason = process_pdf_for_check(
            audit_report_file, model, "signature_date", {"audit_date": audit_report_date}
        )
        st.write("✅ **Compliant**" if ok else "❌ **Non-Compliant**")
        if "<b>" in reason:
            st.markdown("<div><strong>Extracted Signature Block:</strong><br>" + reason + "</div>",
                        unsafe_allow_html=True)
        else:
            st.write(f"**Reason:** {reason}")

    # Check 5 (Financial Statements Type)
    if run_all or st.button("Run Check: Financial Statements Type", key="btn5"):
        st.markdown("**Check 5: Financial Statements Type Consistency**")
        if audit_report_type == "Normal":
            st.write("✅ **Compliant**")
            st.write("**Reason:** No specific check needed for Normal report type.")
        else:
            ok, reason = process_pdf_for_check(
                audit_report_file, model, "financial_statements_type", {"report_type": audit_report_type}
            )
            st.write("✅ **Compliant**" if ok else "❌ **Non-Compliant**")
            st.write(f"**Full Analysis:** {reason}")

    # Check 6 (Opinion Type Consistency)
    if run_all or st.button("Run Check: Opinion Type Consistency", key="btn6"):
        st.markdown("**Check 6: Opinion Type Consistency**")
        ok, reason = process_pdf_for_check(
            audit_report_file, model, "opinion_type_consistency", {"opinion_type": audit_opinion_type}
        )
        st.write("✅ **Compliant**" if ok else "❌ **Non-Compliant**")
        st.write(f"**Reason:** {reason}")

    # Check 7 (Company Name)
    if run_all or st.button("Run Check: Company Name Consistency", key="btn7"):
        st.markdown("**Check 7: Company Name Variations**")
        if company_name:
            ok, _ = process_pdf_for_check(
                audit_report_file, model, "company_name_consistency", {"company_name": company_name}
            )
            st.write("✅ **Compliant**" if ok else "❌ **Non-Compliant**")
            # ... insert variations table & expanders from your existing logic ...
        else:
            st.write("⚠️ **Skipped** – No company name provided.")

    st.markdown("---")

    # 1.2 CARO
    st.subheader("1.2 CARO")
    if not annexure_a_file:
        st.info("Upload Annexure A (CARO) to run CARO checks.")
    else:
        if audit_report_type == "Consolidated":
            if run_all or st.button("Run Check: Clause (xxi) Present", key="btn_caro_xxi"):
                st.markdown("**Check – Clause (xxi) Present – STAGE 2**")
                ok, reason = process_pdf_for_check(
                    annexure_a_file, model, "caro_clause_xxi"
                )
                st.write("✅ **Compliant**" if ok else "❌ **Non-Compliant**")
                st.write(reason)

                                    
            # NBFC exemption
        if is_nbfc=="Yes"  :
            run_nbfc_exempt = run_all or st.button(
                "Run Check: NBFC exemption for CARO clause (iii)(a) & (iii)(e)",
                key="btn_nbfc_exempt"
            )
            if run_nbfc_exempt:
                    st.markdown("**Check – NBFC exemption for CARO clause (iii)(a) & (iii)(e) – STAGE 2**")
                    if is_nbfc == "Yes":
                        nbfc_has_iii, nbfc_detail = process_pdf_for_check(
                            annexure_a_file, model, "caro_nbfc_iii_clause_present"
                        )
                        if nbfc_has_iii:
                            st.warning(
                                "⚠️ NBFCs / Financial Institutions are exempt from "
                                "clause (iii)(a) and clause (iii)(e). Ask the auditor why these "
                                "paragraphs are included."
                            )
                            st.write(nbfc_detail)
                        else:
                            st.success("✅ Clauses (iii)(a) & (iii)(e) correctly omitted for NBFCs.")
                    else:
                        st.info("ℹ️ Entity is not an NBFC / Financial Institution – normal clause (iii) applies.")

        else:
            if run_all or st.button("Run Check: 20 CARO Clauses Covered", key="btn_caro_count"):
                st.markdown("**Check – 20 CARO Clauses Covered – STAGE 2**")
                ok, reason = process_pdf_for_check(
                    annexure_a_file, model, "caro_clause_count"
                )
                st.write("✅ **Compliant**" if ok else "❌ **Non-Compliant**")
                st.write(reason)




    # 1.3 IFC
    st.subheader("1.3 IFC")
    st.info("No checks implemented yet for IFC. Coming soon…")


    # 2) Interlinked References
    st.header("2) Interlinked References")

    st.subheader("2.1 Audit Report – CARO")
    if annexure_a_file:
        if run_all or st.button("Run Check: AR ⇄ CARO", key="btn21a"):
            st.markdown("**Check 1) Extract paragraph referencing CARO in Audit Report**")
            ok, exp = process_pdf_for_check(audit_report_file, model, "caro_interlink_ar")
            st.write("**Audit Report:** Successful extraction:" if ok else "❌ Could not extract CARO reference")
            st.write(exp)
        if run_all or st.button("Run Check: CARO ⇄ AR", key="btn21b"):
            st.markdown("**(b) Extract paragraph referencing AR in Annexure A**")
            ok, exp = process_pdf_for_check(annexure_a_file, model, "caro_interlink_annexure")
            st.write("**Annexure A:** Successful extraction:" if ok else "❌ Could not extract reference")
            st.write(exp)

    st.subheader("2.2 Audit Report – IFC")
    if annexure_b_file:
        if run_all or st.button("Run Check: AR ⇄ IFC", key="btn22a"):
            st.markdown("**Check 1) Extract paragraph referencing IFC in Audit Report**")
            ok, exp = process_pdf_for_check(audit_report_file, model, "ifc_interlink_ar")
            st.write("**Audit Report:** Successful extraction:" if ok else "❌ Could not extract IFC reference")
            st.write(exp)
        if run_all or st.button("Run Check: IFC ⇄ AR", key="btn22b"):
            st.markdown("**(b) Extract paragraph referencing AR in Annexure B**")
            ok, exp = process_pdf_for_check(annexure_b_file, model, "ifc_interlink_annexure")
            st.write("**Annexure B:** Successful extraction:" if ok else "❌ Could not extract reference")
            st.write(exp)

#2.3 CARO-BS

    st.subheader("2.3 CARO – Balance Sheet")
    if annexure_a_file and balance_sheet_file:

# ────────────────────────────────────────────────────────────────
# PPE sub-clauses (i)(a)(A) and (i)(b)
# ────────────────────────────────────────────────────────────────
        run_ppe = run_all or st.button("Run Check: PPE sub-clauses", key="btn231")
        if run_ppe:
            st.markdown("##### Property, Plant & Equipment (PPE) – STAGE 2")

            # 1️⃣  Pull the current-year PPE amount
            val, src = process_pdf_for_check(
                balance_sheet_file, model,
                "balance_sheet_ppe_value"
            )
            st.write(f"PPE amount detected: {val} Cr (source: {src})")

            needs_clause = val > 0      # clause required only if PPE > 0

            st.markdown("**Checklist – CARO clauses (i)(a)(A) – (i)(b):**")
            for label in ["(i)(a)(A)", "(i)(b)"]:
                ok, snippet = process_pdf_for_check(
                    annexure_a_file, model,
                    "caro_clause_present", {"label": label}
                )

                if needs_clause:                # PPE exists ➜ clause must appear
                    if ok:
                        st.success(f"✅ Clause {label} present in CARO Annexure.")
                        st.write(snippet)
                    else:
                        st.error(f"❌ Clause {label} missing – auditor should address this.")
                else:                           # PPE is zero ➜ clause optional
                    if ok and "not applicable" not in snippet.lower():
                        st.info(
                            f"⚠️ Clause {label} appears even though PPE is nil "
                            "(acceptable if marked ‘Not applicable’)."
                        )
                        st.write(snippet)
                    else:
                        st.write(f"Clause {label} correctly omitted or marked ‘Not applicable’.")



                    

        # Intangible
# ────────────────────────────────────────────────────────────────
# Intangible-assets sub-clause (i)(a)(B)
# ────────────────────────────────────────────────────────────────
    if balance_sheet_file and annexure_a_file:
        run_intang = run_all or st.button(
            "Run Check: Intangible sub-clause", key="btn232"
        )
        if run_intang:
            st.markdown("##### Intangible Assets – STAGE 2")

            # 1️⃣  Pull current-year Intangible-asset amount
            val, src = process_pdf_for_check(
                balance_sheet_file, model,
                "balance_sheet_intangible_value"
            )
            st.write(f"Intangible-assets amount detected: {val} Cr (source: {src})")

            needs_clause = val > 0          # clause required only if amount > 0

            st.markdown("**Checklist – CARO clause (i)(a)(B):**")
            ok, snippet = process_pdf_for_check(
                annexure_a_file, model,
                "caro_clause_present", {"label": "(i)(a)(B)"}
            )

            if needs_clause:                # amount exists ➜ clause must appear
                if ok:
                    st.success("✅ Clause (i)(a)(B) present in CARO Annexure.")
                    st.write(snippet)
                else:
                    st.error("❌ Clause (i)(a)(B) missing – auditor should address this.")
            else:                           # amount is zero ➜ clause optional
                if ok and "not applicable" not in snippet.lower():
                    st.info(
                        "⚠️ Clause (i)(a)(B) appears even though Intangible-asset cost is nil "
                        "(acceptable if marked ‘Not applicable’)."
                    )
                    st.write(snippet)
                else:
                    st.write("Clause (i)(a)(B) correctly omitted or marked ‘Not applicable’.")



        # Inventory
        if run_all or st.button("Run Check: Inventory sub-clause", key="btn233"):
            st.markdown("##### Inventories – STAGE 2")

            # 1️⃣ Balance Sheet amount
            val, src = process_pdf_for_check(
                balance_sheet_file, model, "balance_sheet_inventory_value"
            )
            st.write(f"Inventory amount detected: {val} Cr (source: {src})")

            needs_clause = val > 0      # clause required only if inventory > 0

            # 2️⃣ CARO clause (ii)(a)
            st.markdown("**Checklist – CARO clause (ii)(a):**")
            label = "(ii)(a)"
            ok, snippet = process_pdf_for_check(
                annexure_a_file, model,
                "caro_clause_present", {"label": label}
            )

            if needs_clause:                       # inventory exists ➜ clause must appear
                if ok:
                    st.success(f"✅ Clause {label} present.")
                    st.write(snippet)
                else:
                    st.error(f"❌ Clause {label} missing – auditor should address this.")
            else:                                  # inventory is zero ➜ clause optional
                if ok and "not applicable" not in snippet.lower():
                    st.info(
                        f"⚠️ Clause {label} appears even though inventory is nil "
                        "(acceptable if marked ‘Not applicable’)."
                    )
                    st.write(snippet)
                else:
                    st.write(f"Clause {label} correctly omitted or marked ‘Not applicable’.")


        # ── Secured Borrowings vs CARO (ii)(b)
    if balance_sheet_file and annexure_a_file:
        if run_all or st.button(
            "Run Check: Secured Borrowings > ₹5 Cr → CARO clause (ii)(b)",
            key="btn_secured_borrowings_iib"
        ):
            st.markdown("##### 🏦 Secured Borrowings vs CARO clause (ii)(b)")

            # use Gemini to pull the current-year borrowing amount
            amt, bs_line = process_pdf_for_check(
                balance_sheet_file, model,
                "bs_secured_borrowings_gemini"
            )
            if bs_line:
                st.write(f"🔍 Secured borrowings detected: ₹{amt:,.2f} Cr — “{bs_line}”")
            else:
                st.info("ℹ️ No ‘(i) Borrowings’ row found under Current Liabilities.")

            exceeds = amt > 5.0
            if exceeds:
                st.success("✅ Borrowings exceed ₹ 5 Cr → clause (ii)(b) **required**.")
            else:
                st.info("ℹ️ Borrowings ≤ ₹ 5 Cr → clause (ii)(b) **not mandatory**.")

            ok_iib, det_iib = process_pdf_for_check(
                annexure_a_file, model,
                "caro_clause_present", {"label": "(ii)(b)"}
            )

            if exceeds:
                if ok_iib:
                    st.success("✅ CARO clause (ii)(b) present and correct.")
                else:
                    st.error("❌ Borrowings > ₹ 5 Cr but clause (ii)(b) missing.")
                    st.write(det_iib)
            else:
                if ok_iib and "not applicable" not in det_iib.lower():
                    st.warning("⚠️ Clause (ii)(b) present even though borrowings ≤ ₹ 5 Cr.")
                else:
                    st.success("✅ Clause (ii)(b) correctly omitted or marked ‘Not applicable’.")

            if det_iib and det_iib.strip().lower() not in ("none","missing"):
                st.markdown(f"**Clause (ii)(b) extract:** {det_iib}")




        # --------------------------------------------------------------------
        # Revised check – Deposits accepted  ↔  CARO clause (3)(v)
        # --------------------------------------------------------------------
        if run_all or st.button("Run Check: Deposits accepted  →  CARO clause (v)",
                                key="btn_fd"):
            st.markdown("##### Deposits accepted vs CARO clause (v)  – STAGE 2")

            # 1️⃣ Scan Balance Sheet liabilities for PUBLIC deposits (not FD collateral)
            if not balance_sheet_file:
                st.warning("Please upload the Balance Sheet to run this check.")
                needs_clause = None          # can’t evaluate without the Balance Sheet
            else:
                dep_found, dep_line = process_pdf_for_check(
                    balance_sheet_file, model, "bs_has_public_deposits"
                )
                if dep_found:
                    st.write(f"🔍 **Possible public deposit line found:** “{dep_line}”")
                else:
                    st.success("✅ No public deposits detected in liabilities section.")
                needs_clause = dep_found     # clause required only if deposits exist

            # 2️⃣ CARO clause (v)
            st.markdown("**Checklist – CARO clause (v):**")
            label = "(v)"
            ok, snippet = process_pdf_for_check(
                annexure_a_file, model,
                "caro_clause_present", {"label": label}
            )

            if needs_clause is None:                          # Balance Sheet missing
                if ok:
                    st.info(
                        f"⚠️ Clause {label} appears — verification pending Balance Sheet upload."
                    )
                    st.write(snippet)
                else:
                    st.warning(
                        f"⚠️ Could not confirm need for Clause {label}; Balance Sheet not provided."
                    )

            elif needs_clause:                                # deposits exist ➜ clause must appear
                if ok:
                    st.success(f"✅ Clause {label} present.")
                    st.write(snippet)
                else:
                    st.error(f"❌ Clause {label} missing – auditor should address this.")

            else:                                             # no deposits ➜ clause optional
                if ok and "not applicable" not in snippet.lower():
                    st.info(
                        f"⚠️ Clause {label} appears even though no public deposits detected "
                        "(acceptable if marked ‘Not applicable’)."
                    )
                    st.write(snippet)
                else:
                    st.write(f"Clause {label} correctly omitted or marked ‘Not applicable’.")


    # ────────────────────────────────────────────────────────────────
    # Statutory-dues unchanged (over 6 months) → CARO clause (vii)(a)
    # ────────────────────────────────────────────────────────────────
    if balance_sheet_file and annexure_a_file:
        run_stat_same = run_all or st.button(
            "Run Check: Statutory dues unchanged → CARO clause (vii)(a)",
            key="btn_caro_viia_stat_same"
        )
        if run_stat_same:
            st.markdown("##### Statutory-dues unchanged year-on-year → clause (vii)(a)")

            # 1) BS check via Gemini
            unchanged, row_text = process_pdf_for_check(
                balance_sheet_file, model, "bs_stat_dues_same", {}
            )
            if unchanged:
                st.write("🔍 **Statutory-due unchanged between years:**")
                st.markdown(f"> {row_text}")
            else:
                st.success("No statutory-dues line has the same amount in both years – clause (vii)(a) not required.")

            # 2) CARO clause (vii)(a) check
            caro_ok, caro_ctx = process_pdf_for_check(
                annexure_a_file, model,
                "caro_clause_present", {"label": "(vii)(a)"}
            )
            st.markdown("**Checklist – CARO clause (vii)(a):**")
            if unchanged:
                if caro_ok:
                    st.success("✅ Clause (vii)(a) present in CARO Annexure.")
                    st.write(caro_ctx)
                else:
                    st.error("❌ Clause (vii)(a) missing – auditor should cover long-outstanding statutory dues.")
            else:
                if caro_ok and "not applicable" not in caro_ctx.lower():
                    st.info("⚠️ Clause (vii)(a) appears even though not required (OK if marked ‘Not applicable’).")
                    st.write(caro_ctx)
                else:
                    st.write("Clause (vii)(a) correctly omitted or marked ‘Not applicable’.")

# 2.3 ---Share Capital / Debentures increase → CARO clause (x)(a)
    if balance_sheet_file and annexure_a_file:
        run_caro_xa_bs = run_all or st.button(
            "Run Check: Share Capital / Debentures → CARO clause (x)(a)",
            key="btn_caro_xa_bs"
        )
        if run_caro_xa_bs:
            st.markdown("##### Share-capital / Debentures increase – clause (x)(a)")

            # 1️⃣ Extract numbers from Balance Sheet
            nums      = extract_share_debenture_changes(balance_sheet_file)
            cur_sc    = nums["current_share_capital"]
            prev_sc   = nums["prior_share_capital"]
            cur_db    = nums["current_debentures"]
            prev_db   = nums["prior_debentures"]

            # 2️⃣ Check for increases
            inc_sc = cur_sc > prev_sc
            inc_db = cur_db > prev_db

            if inc_sc:
                st.write(f"🔍 Share Capital increased: ₹{prev_sc:,.2f} → ₹{cur_sc:,.2f}")
            if inc_db:
                st.write(f"🔍 Debentures increased:    ₹{prev_db:,.2f} → ₹{cur_db:,.2f}")

            else:
                st.success("No increase in Share Capital or Debentures – clause (x)(a) not required.")

            needs_clause = inc_sc or inc_db   # clause required only if any increase

            # 3️⃣ Verify CARO clause (x)(a)
            st.markdown("**Checklist – CARO clause (x)(a):**")
            label = "(x)(a)"
            has_xa, xa_ctx = process_pdf_for_check(
                annexure_a_file, model,
                "caro_clause_present", {"label": label}
            )

            if needs_clause:                           # increase exists ➜ clause must appear
                if has_xa:
                    st.success(f"✅ Clause {label} present in CARO Annexure.")
                    st.write(xa_ctx)
                else:
                    st.error(f"❌ Clause {label} missing – auditor should cover capital/debenture changes.")
            else:                                      # no increase ➜ clause optional
                if has_xa and "not applicable" not in xa_ctx.lower():
                    st.info(
                        f"⚠️ Clause {label} appears even though no increases detected "
                        "(acceptable if marked ‘Not applicable’)."
                    )
                    st.write(xa_ctx)
                else:
                    st.write(f"Clause {label} correctly omitted or marked ‘Not applicable’.")



    # ────────────────────────────────────────────────────────────────
    # Deemed Deposits → CARO clause (v)
    # ────────────────────────────────────────────────────────────────
        run_caro_v = run_all or st.button(
            "Run Check: Deemed deposits → CARO clause (v)",
            key="btn_caro_v_deemed"
        )
        if run_caro_v:
            st.markdown("##### Deemed deposit check – clause (v)")

            # 1️⃣ Extract year-on-year amounts
            results = process_pdf_for_check(
                balance_sheet_file, model,
                "deemed_deposits", {}
            )

            # 2️⃣ Find items where current == prior > 0
            deemed = [
                r for r in results
                if r["current"] == r["prior"] and float(r["current"]) > 0
            ]

            if deemed:
                st.write("🔍 **Potential deemed-deposit items (same balance):**")
                for d in deemed:
                    st.markdown(f"> **{d['item']}** – ₹{d['current']} Cr")

            else:
                st.success("No deemed-deposit items found; clause (v) not required.")

            needs_clause = bool(deemed)   # clause (v) needed only if deemed items found

            # 3️⃣ Verify CARO clause (v)
            st.markdown("**Checklist – CARO clause (v):**")
            label = "(v)"
            ok, ctx = process_pdf_for_check(
                annexure_a_file, model,
                "caro_clause_present", {"label": label}
            )

            if needs_clause:                              # deemed deposits exist ➜ clause must appear
                if ok:
                    st.success(f"✅ Clause {label} present in CARO Annexure.")
                    st.write(ctx)
                else:
                    st.error(f"❌ Clause {label} missing – auditor should address deemed deposits.")
            else:                                         # no deemed deposits ➜ clause optional
                if ok and "not applicable" not in ctx.lower():
                    st.info(
                        f"⚠️ Clause {label} appears even though no deemed deposits detected "
                        "(acceptable if marked ‘Not applicable’)."
                    )
                    st.write(ctx)
                else:
                    st.write(f"Clause {label} correctly omitted or marked ‘Not applicable’.")




    # ────────────────────────────────────────────────────────────────
    # Clause (ix)(d): Current Liabilities vs Current Assets
    # ────────────────────────────────────────────────────────────────
    if balance_sheet_file and annexure_a_file:
        run_caro_ixd = run_all or st.button(
            "Run Check: CL > CA → CARO clause (ix)(d)",
            key="btn_caro_ixd"
        )
        if run_caro_ixd:
            st.markdown("##### Clause (ix)(d): Current Liabilities vs Current Assets")

            # 1️⃣ Extract Current Liabilities and Current Assets
            liab, asset = process_pdf_for_check(
                balance_sheet_file, model,
                "bs_curr_liab_vs_asset", {}
            )
            st.markdown(f"• **Current Liabilities:** ₹{liab:,.2f} Cr")
            st.markdown(f"• **Current Assets:** ₹{asset:,.2f} Cr")

            # 2️⃣ Decide if clause is required
            needs_clause = liab > asset
            if needs_clause:
                st.error("❌ Current Liabilities exceed Current Assets; clause (ix)(d) must be disclosed.")
            else:
                st.success("✅ Current Assets exceed Current Liabilities; clause (ix)(d) not required.")

            # 3️⃣ Verify CARO clause (ix)(d)
            st.markdown("**Checklist – CARO clause (ix)(d):**")
            label = "(ix)(d)"
            ok, ctx = process_pdf_for_check(
                annexure_a_file, model,
                "caro_clause_present", {"label": label}
            )

            if needs_clause:                               # mismatch exists ➜ clause must appear
                if ok:
                    st.success(f"✅ Clause {label} present in CARO Annexure.")
                    st.write(ctx)
                else:
                    st.error(f"❌ Clause {label} missing – auditor should address current-liability mismatch.")
            else:                                          # no mismatch ➜ clause optional
                if ok and "not applicable" not in ctx.lower():
                    st.info(
                        f"⚠️ Clause {label} appears even though Current Assets ≥ Current Liabilities "
                        "(acceptable if marked ‘Not applicable’)."
                    )
                    st.write(ctx)
                else:
                    st.write(f"Clause {label} correctly omitted or marked ‘Not applicable’.")
    else:
        st.info("Upload both the Balance Sheet and CARO Annexure to run this check.")




#2.4CARO_NOTES

    st.subheader("2.4 CARO – Notes to Accounts")
    if annexure_a_file and notes_file:
        # Immovable Property
        if run_all or st.button("Run Check: Immovable Property clause", key="btn241"):
            st.markdown("##### Immovable Property – clause (i)(c)  – STAGE 2")

            # 1️⃣ Look for immovable-property disclosures in the Notes
            has_prop, det_line = process_pdf_for_check(
                notes_file, model, "bs_has_immovable_property"
            )
            if has_prop:
                st.write(f"🔍 **Immovable-property reference found:** “{det_line}”")
            else:
                st.success("✅ No immovable property detected in Notes to Accounts.")

            needs_clause = has_prop          # clause (i)(c) needed only if property exists

            # 2️⃣ Verify CARO clause (i)(c)
            st.markdown("**Checklist – CARO clause (i)(c):**")
            label = "(i)(c)"
            ok, snippet = process_pdf_for_check(
                annexure_a_file, model,
                "caro_clause_present", {"label": label}
            )

            if needs_clause:                             # property exists ➜ clause must appear
                if ok:
                    st.success(f"✅ Clause {label} present in CARO Annexure.")
                    st.write(snippet)
                else:
                    st.error(f"❌ Clause {label} missing – auditor should address immovable-property details.")
            else:                                        # no property ➜ clause optional
                if ok and "not applicable" not in snippet.lower():
                    st.info(
                        f"⚠️ Clause {label} appears even though no immovable property detected "
                        "(acceptable if marked ‘Not applicable’)."
                    )
                    st.write(snippet)
                else:
                    st.write(f"Clause {label} correctly omitted or marked ‘Not applicable’.")



        #revaluation
        if run_all or st.button("Run Check: Revaluation clause", key="btn_reval"):
            st.markdown("##### Revaluation – clause (i)(d)  – STAGE 2")

            # 1️⃣ Detect a revaluation event in the Notes
            has_reval, reval_line = process_pdf_for_check(
                notes_file, model, "caro_revaluation_clause_id", {}
            )
            if has_reval:
                st.write(f"🔍 **Revaluation reference found:** “{reval_line}”")
            else:
                st.success("✅ No asset revaluation detected in Notes to Accounts.")

            needs_clause = has_reval          # clause (i)(d) needed only if revaluation occurred

            # 2️⃣ Verify CARO clause (i)(d)
            st.markdown("**Checklist – CARO clause (i)(d):**")
            label = "(i)(d)"
            ok, snippet = process_pdf_for_check(
                annexure_a_file, model,
                "caro_clause_present", {"label": label}
            )

            if needs_clause:                               # revaluation exists ➜ clause must appear
                if ok:
                    st.success(f"✅ Clause {label} present in CARO Annexure.")
                    st.write(snippet)
                else:
                    st.error(f"❌ Clause {label} missing – auditor should address revaluation details.")
            else:                                          # no revaluation ➜ clause optional
                if ok and "not applicable" not in snippet.lower():
                    st.info(
                        f"⚠️ Clause {label} appears even though no revaluation detected "
                        "(acceptable if marked ‘Not applicable’)."
                    )
                    st.write(snippet)
                else:
                    st.write(f"Clause {label} correctly omitted or marked ‘Not applicable’.")





        # ── Goods-in-Transit / Transit Coverage ────────────────────────────────────
        if run_all or st.button("Run Check: Goods in Transit", key="btn243"):
            st.markdown("##### 🚚 Goods-in-Transit (Inventory) – clause (ii)(a)")

            # 1️⃣ Scan Notes to Accounts for a goods-in-transit reference
            found, detail, *_ = process_pdf_for_check(
                notes_file, model, "notes_has_goods_in_transit"
            )

            if found:
                st.write("Goods in Transit in Inventory Note: **Yes**")
                st.write(detail)
            else:
                st.write("Goods in Transit in Inventory Note: **No**")

            needs_clause = found            # clause (ii)(a) needed only if GIT appears

            # 2️⃣ Verify CARO clause (ii)(a)
            st.markdown("**Checklist – CARO clause (ii)(a):**")
            label = "(ii)(a)"
            ok, snippet = process_pdf_for_check(
                annexure_a_file, model,
                "caro_clause_present", {"label": label}
            )

            if needs_clause:                               # GIT exists ➜ clause must appear
                if ok:
                    st.success(f"✅ Clause {label} present.")
                    st.write(snippet)
                else:
                    st.error(f"❌ Clause {label} missing – auditor should address Goods-in-Transit.")
            else:                                          # no GIT ➜ clause optional
                if ok and "not applicable" not in snippet.lower():
                    st.info(
                        f"⚠️ Clause {label} appears even though no Goods-in-Transit detected "
                        "(acceptable if marked ‘Not applicable’)."
                    )
                    st.write(snippet)
                else:
                    st.write(f"Clause {label} correctly omitted or marked ‘Not applicable’.")




        # ── New Investments / Loans / Guarantees – clause (iii) ─────────────────────
        if run_all or st.button(
            "Run Check: New Inv/Loans/Guarantees vs CARO clause (iii)",
            key="btn_new_txn"
        ):
            st.markdown("##### 📊 New Investments / Loans / Guarantees vs CARO clause (iii)")

            # ① Parse Notes-to-Accounts for current-year movements
            txns = extract_new_invest_loans_guar(notes_file)
            inv, loan, guar = txns["investments"], txns["loans"], txns["guarantees"]

            st.markdown(f"ℹ️ Investments in Notes: {inv or 'None'}")
            st.markdown(f"ℹ️ Loans in Notes:       {loan or 'None'}")
            st.markdown(f"ℹ️ Guarantees in Notes:  {guar or 'None'}")

            needs_clause = any([inv, loan, guar])     # clause (iii) required only if something exists

            # ② Verify CARO clause (iii)
            st.markdown("**Checklist – CARO clause (iii):**")
            label = "(iii)"
            ok, snippet, *_ = process_pdf_for_check(
                annexure_a_file, model,
                "caro_clause_present", {"label": label}
            )

            if needs_clause:                                   # transactions exist → clause must appear
                if ok:
                    st.success(f"✅ Clause {label} present.")
                    st.write(snippet)
                else:
                    st.error(f"❌ Clause {label} missing – auditor should address new investments/loans/guarantees.")
            else:                                              # none exist → clause optional
                if ok and "not applicable" not in snippet.lower():
                    st.info(
                        f"⚠️ Clause {label} appears even though no new transactions detected "
                        "(acceptable if marked ‘Not applicable’)."
                    )
                    st.write(snippet)
                else:
                    st.write(f"Clause {label} correctly omitted or marked ‘Not applicable’.")


        # ── Related-party (RPT) loans/advances – clause (iii)(f) ────────────────────
        if run_all or st.button("Run Check: RPT Loans Advances", key="btn246"):
            st.markdown("##### 🤝 RPT Loans – clause (iii)(f)")

            ok_rpt, det_rpt, *_ = process_pdf_for_check(
                notes_file, model, "notes_rpt_has_loans"
            )
            needs_clause = ok_rpt             # clause (iii)(f) needed only if RPT loans exist

            if ok_rpt:
                st.write(f"Loans/advances disclosed → {det_rpt}")
            else:
                st.success("No related-party loans disclosed in Notes.")

            # Verify CARO clause (iii)(f)
            st.markdown("**Checklist – CARO clause (iii)(f):**")
            label = "(iii)(f)"
            ok_clause, snippet_rpt, *_ = process_pdf_for_check(
                annexure_a_file, model,
                "caro_clause_present", {"label": label}
            )

            if needs_clause:                                 # RPT loans exist → clause must appear
                if ok_clause:
                    st.success(f"✅ Clause {label} present.")
                    st.write(snippet_rpt)
                else:
                    st.error(f"❌ Clause {label} missing – auditor should address related-party loans.")
            else:                                            # none exist → clause optional
                if ok_clause and "not applicable" not in snippet_rpt.lower():
                    st.info(
                        f"⚠️ Clause {label} appears even though no related-party loans detected "
                        "(acceptable if marked ‘Not applicable’)."
                    )
                    st.write(snippet_rpt)
                else:
                    st.info(f"Clause {label} correctly omitted or marked ‘Not applicable’.")


        # Financial Guarantees
            # ── Financial Guarantees – clause (iii)(a)(A)+(B) ──────────────────────
        if is_nbfc == "No":          # <— NBFC filter

            if run_all or st.button("Run Check: Financial Guarantees total", key="btn247"):

                st.markdown("##### Financial Guarantees – clause (iii)(a)(A)+(B)")

                # 1) Total from Notes  (current-year column)
                notes_total, notes_detail = process_pdf_for_check(
                    notes_file, model, "notes_contg_fin_guar_total"
                )

                # 2) Total from CARO Annexure  (adds (A)+(B))
                caro_total, caro_detail = process_pdf_for_check(
                    annexure_a_file, model, "caro_iii_a_guar_total"
                )

                # ---------- Output paths -------------------------------------------------
                if notes_total > 0:      # Guarantees exist
                    st.write(f"Notes – financial guarantees total → ₹{notes_total:,.2f}")
                    st.write(f"CARO total → ₹{caro_total:,.2f}")

                    if abs(notes_total - caro_total) < 0.01:
                        st.success("✅ CARO (iii)(a)(A)+(B) agrees with Notes.")
                    else:
                        st.error("❌ CARO total does not match Notes – ask auditor to reconcile.")
                        st.write(caro_detail)

                else:                    # No guarantees in Notes (₹0)
                    # Check if clause present & states ‘Not applicable’ or 0
                    clause_present = "(iii)(a)(a)" in caro_detail.lower() or "(iii)(a)(b)" in caro_detail.lower()
                    na_flag = "not applicable" in caro_detail.lower() or caro_total == 0

                    if clause_present and na_flag:
                        st.success(
                            "No financial guarantees; CARO (iii)(a)(A) & (iii)(a)(B) present and state "
                            "‘Not applicable’ – figures tally (₹0)."
                        )
                    elif clause_present and not na_flag:
                        st.warning(
                            "Notes show no guarantees but CARO (iii)(a)(A)/(B) has a non-zero figure – please reconcile."
                        )
                        st.write(caro_detail)
                    else:
                        st.success("No financial guarantees – clause not required, correctly omitted.")



        # 60% Net-Worth Threshold
# 60 % Net-Worth Threshold – CARO clause (iv)
        if is_nbfc == "No":
            if run_all or st.button("Run Check: 60% Net-Worth Threshold", key="btn249"):
                st.markdown("##### 60% Net Worth Threshold Check – clause (iv)")

                # 1️⃣ Pull current-year new-finance aggregate and opening net-worth
                agg, det_agg, *_ = process_pdf_for_check(
                    notes_file, model, "financial_exposure_clause_iv"
                )
                nw, nw_det,  *_ = process_pdf_for_check(
                    balance_sheet_file, model, "bs_opening_networth"
                )

                threshold = 0.60 * (nw or 0)

                st.write(f"New finance aggregate → ₹{agg:,.2f}")
                st.write(f"Opening net-worth → ₹{nw:,.2f}  ({nw_det})")
                st.write(f"60 % threshold = ₹{threshold:,.2f}")

                needs_clause = agg > threshold        # clause (iv) needed only if aggregate > 60 % NW

                # 2️⃣ Verify CARO clause (iv)
                st.markdown("**Checklist – CARO clause (iv):**")
                label = "(iv)"
                ok, snippet, *_ = process_pdf_for_check(
                    annexure_a_file, model,
                    "caro_clause_present", {"label": label}
                )

                if needs_clause:                               # >60 % → clause must appear
                    if ok:
                        st.success(f"✅ Clause {label} present.")
                        st.write(snippet)
                    else:
                        st.error(f"❌ Clause {label} missing – auditor should address borrowing above 60 % of net worth.")
                else:                                          # ≤60 % → clause optional
                    if ok and "not applicable" not in snippet.lower():
                        st.info(
                            f"⚠️ Clause {label} appears even though aggregate ≤60 % "
                            "(acceptable if marked ‘Not applicable’)."
                        )
                        st.write(snippet)
                    else:
                        st.write(f"Clause {label} correctly omitted or marked ‘Not applicable’.")



        # Statutory Dues  →  CARO clause (vii)(b)
        # ────────────────────────────────────────────────────────────────────────
        # Statutory Dues   →   CARO clause (vii)(b)
        # ────────────────────────────────────────────────────────────────────────
        run_statutory = run_all or st.button(
            "Run Check: Statutory Dues → CARO clause (vii)(b)",
            key="btn_statutory_dues"
        )

        if run_statutory:
            st.markdown("#### Check – Statutory Dues (clause (vii)(b))")

            # ---------- 1️⃣ Scan Notes to Accounts (Contingent Liab & Provisions) ----------
            statutory_keywords = [
                "gst", "provident fund", "esi", "income tax", "tds",
                "sales tax", "service tax", "customs duty",
                "excise duty", "vat", "cess"
            ]

            def extract_note_section(text: str, header_regex: str) -> str:
                """
                Return the chunk that begins at `header_regex` and ends
                at the next numbered note heading (e.g. "\n 22 " or "\n22.") or EOF.
                """
                m_start = re.search(header_regex, text, re.I)
                if not m_start:
                    return ""
                after = text[m_start.end():]
                m_next = re.search(r"\n\s*\d+\s*(?:\.|\))\s", after)  # next note heading
                return after[:m_next.start()] if m_next else after

            statutory_hits = []   # snippets to display

            if not notes_file:
                st.warning("Upload the Notes to Accounts to run this check.")
                needs_clause = None           # cannot evaluate without the Notes
            else:
                notes_text   = extract_pdf_text(notes_file)
                notes_lower  = notes_text.lower()

                contg_sec = extract_note_section(notes_lower, r"contingent liabilities")
                prov_sec  = extract_note_section(notes_lower, r"(provisions|other current liabilities)")

                # look for keywords inside those sections only
                for kw in statutory_keywords:
                    if kw in contg_sec or kw in prov_sec:
                        idx = notes_lower.find(kw)
                        snippet = notes_text[max(0, idx-45): idx+len(kw)+45].replace("\n", " ")
                        statutory_hits.append(f"…{snippet}…")

                if statutory_hits:
                    st.write("🔍 **Statutory-dues items found in Contingent Liabilities / Provisions:**")
                    for snip in statutory_hits:
                        st.markdown(f"> {snip}")
                else:
                    st.write("No statutory-dues items inside those specific notes.")

                needs_clause = bool(statutory_hits)   # clause needed only if hits present

            # ---------- 2️⃣ Verify clause (vii)(b) in Annexure A --------------------------
            st.markdown("**Checklist – CARO clause (vii)(b):**")
            label = "(vii)(b)"
            ok, snippet, *_ = process_pdf_for_check(
                annexure_a_file, model,
                "caro_clause_present", {"label": label}
            )

            if needs_clause is None:                           # Notes missing
                if ok:
                    st.info(
                        f"⚠️ Clause {label} appears — verification pending Notes upload."
                    )
                    st.write(snippet)
                else:
                    st.warning(
                        f"⚠️ Could not confirm need for Clause {label}; Notes to Accounts not provided."
                    )

            elif needs_clause:                                 # statutory hits → clause must appear
                if ok:
                    st.success(f"✅ Clause {label} present.")
                    st.write(snippet)
                else:
                    st.error(f"❌ Clause {label} missing – auditor should address statutory-dues issues.")

            else:                                              # no hits → clause optional
                if ok and "not applicable" not in snippet.lower():
                    st.info(
                        f"⚠️ Clause {label} appears even though no statutory-dues issues detected "
                        "(acceptable if marked ‘Not applicable’)."
                    )
                    st.write(snippet)
                else:
                    st.write(f"Clause {label} correctly omitted or marked ‘Not applicable’.")


       
    # ────────────────────────────────────────────────────────────────
    # Provision for doubtful loans → combined Notes & CARO check
    # ────────────────────────────────────────────────────────────────
        if run_all or st.button(
            "Run Check: Provision for Doubtful Loans → CARO (iii)(b)-(e)",
            key="btn_prov_bcde"
        ):
            st.markdown("##### Provision for Doubtful Loans & Advances – CARO clauses (iii)(b)-(e)")

            # 1️⃣ Notes trigger – look for a doubtful-loan provision
            prov_found, prov_detail, *_ = process_pdf_for_check(
                notes_file, model, "notes_provision_doubtful_loans", {}
            )

            if prov_found:
                st.write(f"🔍 Provision found in Notes: {prov_detail}")
            else:
                st.success("No provision for doubtful loans/advances in Notes – clauses (iii)(b)-(e) not required.")

            needs_clause = prov_found      # sub-clauses required only if a provision exists

            # 2️⃣ Verify CARO clauses (iii)(b)-(e)
            st.markdown("**Checklist – CARO clauses (iii)(b)-(iii)(e):**")
            for label in ["(iii)(b)", "(iii)(c)", "(iii)(d)", "(iii)(e)"]:
                ok, snippet, *_ = process_pdf_for_check(
                    annexure_a_file, model,
                    "caro_clause_present", {"label": label}
                )

            needs_clause = prov_found          # clause (iii)(b)-(e) needed only if provision exists

            for label in ["(iii)(b)", "(iii)(c)", "(iii)(d)", "(iii)(e)"]:
                ok, snippet, *_ = process_pdf_for_check(
                    annexure_a_file, model,
                    "caro_clause_present", {"label": label}
                )

                if needs_clause:                               # provision exists → clause must appear
                    if ok:
                        st.success(f"✅ Clause {label} present.")
                        st.write(snippet)
                    else:
                        st.error(f"❌ Clause {label} missing – auditor should address this.")
                else:                                          # no provision → clause is N/A
                    if ok:
                        st.write(f"Clause {label} marked ‘Not applicable’.")
                        st.write(snippet)          # still show what the auditor wrote
                    else:
                        st.write(f"Clause {label} correctly omitted as ‘Not applicable’.")



       
    # Notes-to-P&L: Fees paid to Cost Auditor → trigger for clause (vi)
    
    if notes_file and annexure_a_file:
        run_caro_vi = run_all or st.button(
            "Run Check: Cost Auditor fees → CARO clause (vi)",
            key="btn_caro_vi"
        )
        if run_caro_vi:
            st.markdown("##### Fees paid to Cost Auditor – clause (vi)")

            # 1) Check P&L Notes for the trigger
            notes_ok, notes_ctx = process_pdf_for_check(
                notes_file, model,
                "notes_cost_auditor_fees", {}
            )
            if notes_ok:
                st.write("🔍 **Fees to Cost Auditor found in Notes:**")
                st.markdown(f"> {notes_ctx}")
            else:
                st.success("No fees-to-Cost-Auditor mention – clause (vi) not required.")

            # 2) Check CARO Annexure for clause (vi)
            caro_ok, caro_ctx = process_pdf_for_check(
                annexure_a_file, model,
                "caro_clause_present", {"label": "(vi)"}
            )
            st.markdown("**Checklist – CARO clause (vi):**")
            if notes_ok:
                if caro_ok:
                    st.success("✅ Clause (vi) present in CARO Annexure.")
                    st.write(caro_ctx)
                else:
                    st.error("❌ Clause (vi) missing – auditor should address Cost Auditor fees.")
            else:
                if caro_ok and "not applicable" not in caro_ctx.lower():
                    st.info("⚠️ Clause (vi) appears although no fees mention (OK if marked ‘Not applicable’).")
                    st.write(caro_ctx)
                else:
                    st.write("Clause (vi) correctly omitted or marked ‘Not applicable’.")
    

    # ────────────────────────────────────────────────────────────────
    # Statutory-dues in Provisions → extract up to 3 items via Gemini
    # plus CARO clause (vii)(a)
    # ────────────────────────────────────────────────────────────────
    if notes_file and annexure_a_file:
        run_vii_a_notes = run_all or st.button(
            "Run Check: Statutory-dues in Provisions → CARO clause (vii)(a)",
            key="btn_caro_viia_notes"
        )
        if run_vii_a_notes:
            st.markdown("##### Statutory-dues in Provisions/Other Liabilities – clause (vii)(a)")

            # 1) Pull up to 3 items from Notes
            items = process_pdf_for_check(
                notes_file, model, "notes_stat_dues_gemini", {}
            )
            if items and not (len(items)==1 and items[0].lower()=="none"):
                st.write("🔍 **Statutory-dues items found in Notes:**")
                for ln in items:
                    st.markdown(f"> {ln}")
            else:
                st.success("No statutory-dues in Provisions/Other Liabilities – clause (vii)(a) not required.")

            # 2) Verify CARO clause (vii)(a)
            ok, detail = process_pdf_for_check(
                annexure_a_file, model,
                "caro_clause_present", {"label": "(vii)(a)"}
            )
            st.markdown("**Checklist – CARO clause (vii)(a):**")
            if items and not (len(items)==1 and items[0].lower()=="none"):
                if ok:
                    st.success("✅ Clause (vii)(a) present in CARO Annexure.")
                    st.write(detail)
                else:
                    st.error("❌ Clause (vii)(a) missing – auditor should cover long-outstanding statutory dues.")
            else:
                if ok and "not applicable" not in detail.lower():
                    st.info("⚠️ Clause (vii)(a) appears although not required (OK if marked ‘Not applicable’).")
                    st.write(detail)
                else:
                    st.write("Clause (vii)(a) correctly omitted or marked ‘Not applicable’.")



# Non-cash RPT → extract up to 3 items via Gemini + CARO clause (xv)
        if notes_file and annexure_a_file:
            run_caro_xv = run_all or st.button(
                "Run Check: Non-cash RPT → CARO clause (xv)",
                key="btn_caro_xv"
            )
            if run_caro_xv:
                st.markdown("##### Non-cash related-party transactions – clause (xv)")

                # 1️⃣ Pull potential non-cash RPT lines from the Notes
                raw_items, *_ = process_pdf_for_check(
                    notes_file, model,
                    "notes_rpt_noncash_multiple", {}
                )

                if not isinstance(raw_items, list):
                    raw_items = []

                # 2️⃣ Keep only lines without digits → truly “non-cash”
                noncash_items = [ln for ln in raw_items if not re.search(r"\d", ln)]

                # 3️⃣ Show results
                if noncash_items:
                    st.markdown("🔍 **Non-cash transaction(s) found in RPT note:**")
                    for ln in noncash_items:
                        st.markdown(f"> {ln}")
                else:
                    st.success("✅ No non-cash related-party transactions found; clause (xv) not required.")

                needs_clause = bool(noncash_items)      # clause (xv) needed only if items exist

                # 4️⃣ Verify CARO clause (xv)
                st.markdown("**Checklist – CARO clause (xv):**")
                label = "(xv)"
                ok, ctx, *_ = process_pdf_for_check(
                    annexure_a_file, model,
                    "caro_clause_present", {"label": label}
                )

                if needs_clause:                          # items exist → clause must appear
                    if ok:
                        st.success(f"✅ Clause {label} present in CARO Annexure.")
                        st.write(ctx)
                    else:
                        st.error(f"❌ Clause {label} missing – auditor should address non-cash RPT items.")
                else:                                     # no items → clause optional
                    if ok and "not applicable" not in ctx.lower():
                        st.info(
                            f"⚠️ Clause {label} appears even though no non-cash RPT detected "
                            "(acceptable if marked ‘Not applicable’)."
                        )
                        st.write(ctx)
                    else:
                        st.write(f"Clause {label} correctly omitted or marked ‘Not applicable’.")



    # ────────────────────────────────────────────────────────────────
    # Related-party loans → CARO clauses (ix)(e) & (ix)(f)
    # ────────────────────────────────────────────────────────────────
    if notes_file and annexure_a_file:
        run_caro_iiief = run_all or st.button(
            "Run Check: Related-party loans → CARO clause (ix)(e)/(f)",
            key="btn_caro_iiief"
        )
        if run_caro_iiief:
            st.markdown("##### Related-party loans taken – clause (ix)(e) & (ix)(f)")

            # 1) Detect related-party loans
            rpt_found, rpt_detail = process_pdf_for_check(
                notes_file, model, "notes_rpt_loans_taken", {}
            )
            if rpt_found:
                st.write("🔍 **Related-party loan found in RPT note:**")
                st.markdown(f"> {rpt_detail}")
            else:
                st.success("No related-party loans found → clauses (iii)(e)/(f) not required.")

            # 2) Check CARO sub-clauses (iii)(e) and (iii)(f)
            st.markdown("**Checklist – CARO clauses (ix)(e) & (ix)(f):**")
            for label in ["(ix)(e)", "(ix)(f)"]:
                ok, ctx = process_pdf_for_check(
                    annexure_a_file, model,
                    "caro_clause_present", {"label": label}
                )
                if rpt_found:
                    if ok:
                        st.success(f"✅ Clause {label} present in CARO Annexure.")
                        st.write(ctx)
                    else:
                        st.error(f"❌ Clause {label} missing – auditor should address related-party loans.")
                else:
                    # if no loans, clause may still appear as N.A.
                    if ok and "not applicable" in ctx.lower():
                        st.info(f"✅ Clause {label} correctly marked ‘Not applicable’.")
                    elif ok:
                        st.warning(f"⚠️ Clause {label} appears (OK if marked ‘Not applicable’).")
                        st.write(ctx)
                    else:
                        st.write(f"Clause {label} correctly omitted.")
    

    else:
        st.info("Upload both the Notes-to-Accounts and CARO Annexure to run this check.")




   
    st.subheader("2.5) CARO – P&L, Notes ")


    # Inventory Write-off  ↔  CARO clause (ii)(a)   (P&L + Notes)
    # ──────────────────────────────────────────────────────────────────
    if (pl_notes_file or notes_file) and annexure_a_file:
        if run_all or st.button(
            "Run Check: Inventory Write-off & CARO clause (ii)(a)",
            key="btn_writeoff_caro"
        ):

            st.markdown("##### Inventory Write-off  ↔  CARO clause (ii)(a)")

            # 1️⃣ Detect inventory write-off in P&L / Notes
            found, details, *_ = process_pdf_for_check(
                pl_notes_file or notes_file, model,
                "inventory_writeoff_gemini",
                {"pl_notes_file": pl_notes_file, "notes_file": notes_file}
            )
            st.write("Inventory write-off detected:", "Yes" if found else "No")
            st.markdown(f"<pre>{details}</pre>", unsafe_allow_html=True)

            needs_clause = found        # clause (ii)(a) needed only if write-off exists

            # 2️⃣ Verify CARO clause (ii)(a)
            st.markdown("**Checklist – CARO clause (ii)(a):**")
            label = "(ii)(a)"
            ok, snippet, *_ = process_pdf_for_check(
                annexure_a_file, model,
                "caro_clause_present", {"label": label}
            )

            if needs_clause:                           # write-off exists → clause must appear
                if ok:
                    st.success(f"✅ Clause {label} present.")
                    st.write(snippet)
                else:
                    st.error(f"❌ Clause {label} missing – auditor should address the inventory write-off.")
            else:                                      # no write-off → clause is N/A
                if ok:
                    st.write(f"Clause {label} marked ‘Not applicable’.")
                    st.write(snippet)      # still show what the auditor wrote
                else:
                    st.write(f"Clause {label} correctly omitted as ‘Not applicable’.")
    else:
        st.info("Upload the P&L PDF (statement) *and* Annexure A (CARO) to run this write-off check.")


    # 2.6 CARO – Full Annual Report
    # ────────────────────────────────────────────────────────────────
    st.subheader("2.6 CARO – Full Annual Report")

    if annual_report_file and annexure_a_file:
        # Income-tax raids / Unrecorded income → clause (viii)
        # ───────────────────────── clause (viii) ─────────────────────────
        run_caro_viii = run_all or st.button(
            "Run Check: IT Raids / Unrecorded Income → CARO clause (viii)",
            key="btn_caro_viii"
        )
        if run_caro_viii:
            st.markdown("##### Income-tax raids / Unrecorded income – clause (viii)")
            ann_text = extract_pdf_text(annual_report_file).lower()
            hits = [kw for kw in [
                "income tax raids",
                "raids by income tax authorities",
                "unrecorded income"
            ] if kw in ann_text]

            needs_clause = bool(hits)
            if hits:
                st.write(f"🔍 **Trigger phrases found:** {', '.join(hits)}")
            else:
                st.success("No mentions of raids or unrecorded income – clause (viii) not required.")

            label = "(viii)"
            ok, snippet, *_ = process_pdf_for_check(
                annexure_a_file, model, "caro_clause_present", {"label": label}
            )
            st.markdown("**Checklist – CARO clause (viii)**")
            if needs_clause:
                if ok:
                    st.success(f"✅ Clause {label} present in CARO Annexure.")
                    st.write(snippet)
                else:
                    st.error(f"❌ Clause {label} missing – auditor should have addressed undisclosed income.")
            else:
                if ok:
                    st.info(f"Clause {label} marked ‘Not applicable’.")
                    st.write(snippet)
                else:
                    st.info(f"Clause {label} correctly omitted as ‘Not applicable’.")

        # Defaults / Wilful Defaulter / Diversion → clause (ix)
        run_caro_ix = run_all or st.button(
            "Run Check: Defaults / Wilful Defaulter / Diversion → CARO clause (ix)",
            key="btn_caro_ix"
        )
        if run_caro_ix:
            st.markdown("##### Borrowing Defaults – clause (ix)")
            ann_text = extract_pdf_text(annual_report_file).lower()
            hits_ix = [kw for kw in ["defaults", "wilful defaulter", "diversion"] if kw in ann_text]

            needs_clause = bool(hits_ix)
            if hits_ix:
                st.write(f"🔍 **Trigger phrases found:** {', '.join(hits_ix)}")
            else:
                st.success("No relevant trigger phrases – clause (ix) not required.")

            label = "(ix)"
            ok, snippet, *_ = process_pdf_for_check(
                annexure_a_file, model, "caro_clause_present", {"label": label}
            )
            st.markdown("**Checklist – CARO clause (ix)**")
            if needs_clause:
                if ok:
                    st.success(f"✅ Clause {label} present in CARO Annexure.")
                    st.write(snippet)
                else:
                    st.error(f"❌ Clause {label} missing – auditor should have addressed defaults/diversion.")
            else:
                if ok:
                    st.info(f"Clause {label} marked ‘Not applicable’.")
                    st.write(snippet)
                else:
                    st.info(f"Clause {label} correctly omitted as ‘Not applicable’.")

        # Rights Issue / Convertible Debentures → clause (x)(b)
        run_caro_xb = run_all or st.button(
            "Run Check: Rights Issue / Convertible Debentures → CARO clause (x)(b)",
            key="btn_caro_xb"
        )
        if run_caro_xb:
            st.markdown("##### Rights Issue – clause (x)(b)")
            ann_text = extract_pdf_text(annual_report_file).lower()
            hits_xb = [kw for kw in ["rights issue", "convertible debenture"] if kw in ann_text]

            needs_clause = bool(hits_xb)
            if hits_xb:
                st.write(f"🔍 **Trigger phrases found:** {', '.join(hits_xb)}")
            else:
                st.success("No rights-issue or convertible-debenture references – clause (x)(b) not required.")

            label = "(x)(b)"
            ok, snippet, *_ = process_pdf_for_check(
                annexure_a_file, model, "caro_clause_present", {"label": label}
            )
            st.markdown("**Checklist – CARO clause (x)(b)**")
            if needs_clause:
                if ok:
                    st.success(f"✅ Clause {label} present in CARO Annexure.")
                    st.write(snippet)
                else:
                    st.error(f"❌ Clause {label} missing – auditor should have covered the rights-issue / debentures.")
            else:
                if ok:
                    st.info(f"Clause {label} marked ‘Not applicable’.")
                    st.write(snippet)
                else:
                    st.info(f"Clause {label} correctly omitted as ‘Not applicable’.")

        # Fraud mention → clause (xi)(a)
    # ───────────────────────── clause (xi)(a) ─────────────────────────
        run_caro_xia = run_all or st.button(
            "Run Check: Fraud mention → CARO clause (xi)(a)",
            key="btn_caro_xia"
        )
        if run_caro_xia:
            st.markdown("##### Fraud – clause (xi)(a)")
            ann_text = extract_pdf_text(annual_report_file).lower()
            needs_clause = "fraud" in ann_text
            if needs_clause:
                st.write("🔍 **Trigger word ‘fraud’ found in the annual report.**")
            else:
                st.success("No ‘fraud’ reference – clause (xi)(a) not required.")

            label = "(xi)(a)"
            ok, snippet, *_ = process_pdf_for_check(
                annexure_a_file, model, "caro_clause_present", {"label": label}
            )
            st.markdown("**Checklist – CARO clause (xi)(a)**")
            if needs_clause:
                if ok:
                    st.success(f"✅ Clause {label} present in CARO Annexure.")
                    st.write(snippet)
                else:
                    st.error(f"❌ Clause {label} missing – auditor should have addressed the fraud reference.")
            else:
                if ok:
                    st.info(f"Clause {label} marked ‘Not applicable’.")
                    st.write(snippet)
                else:
                    st.info(f"Clause {label} correctly omitted as ‘Not applicable’.")


        # Section 143(12) reference → clause (xi)(b)
        run_caro_xib = run_all or st.button(
            "Run Check: Section 143(12) reference → CARO clause (xi)(b)",
            key="btn_caro_xib"
        )
        if run_caro_xib:
            st.markdown("##### Section 143(12) – clause (xi)(b)")
            ann_text = extract_pdf_text(annual_report_file).lower()
            found_143 = bool(re.search(r"sub[\s-]?section\s*\(?12\)?\s*of\s*section\s*143", ann_text, re.I))
            needs_clause = found_143
            if found_143:
                st.write("🔍 **Reference to Sub-section (12) of Section 143 found.**")
            else:
                st.success("No references to Section 143(12) – clause (xi)(b) not required.")

            label = "(xi)(b)"
            ok, snippet, *_ = process_pdf_for_check(
                annexure_a_file, model, "caro_clause_present", {"label": label}
            )
            st.markdown("**Checklist – CARO clause (xi)(b)**")
            if needs_clause:
                if ok:
                    st.success(f"✅ Clause {label} present in CARO Annexure.")
                    st.write(snippet)
                else:
                    st.error(f"❌ Clause {label} missing – auditor should have addressed reporting under Section 143(12).")
            else:
                if ok:
                    st.info(f"Clause {label} marked ‘Not applicable’.")
                    st.write(snippet)
                else:
                    st.info(f"Clause {label} correctly omitted as ‘Not applicable’.")

        # Whistle-blower complaint → clause (xi)(c)
        # ───────────────────────── clause (xi)(c) ─────────────────────────
        run_caro_xic = run_all or st.button(
            "Run Check: Whistle-blower complaint → CARO clause (xi)(c)",
            key="btn_caro_xic"
        )
        if run_caro_xic:
            st.markdown("##### Whistle-blower – clause (xi)(c)")
            ann_raw = extract_pdf_text(annual_report_file)
            wb_match = re.search(r"whistle[\s\-]?blower.*?complaint", ann_raw, re.I)
            needs_clause = bool(wb_match)
            if wb_match:
                st.write("🔍 **Whistle-blower complaint reference found:**")
                st.markdown(f"> {wb_match.group(0).strip()}")
            else:
                st.success("No whistle-blower complaint mention – clause (xi)(c) not required.")

            label = "(xi)(c)"
            ok, snippet, *_ = process_pdf_for_check(
                annexure_a_file, model, "caro_clause_present", {"label": label}
            )
            st.markdown("**Checklist – CARO clause (xi)(c)**")
            if needs_clause:
                if ok:
                    st.success(f"✅ Clause {label} present in CARO Annexure.")
                    st.write(snippet)
                else:
                    st.error(f"❌ Clause {label} missing – auditor should have addressed the whistle-blower complaint.")
            else:
                if ok:
                    st.info(f"Clause {label} marked ‘Not applicable’.")
                    st.write(snippet)
                else:
                    st.info(f"Clause {label} correctly omitted as ‘Not applicable’.")

        # Immovable-property disputes → clause (i)(c)
        run_caro_ic = run_all or st.button(
            "Run Check: Immovable-property dispute → CARO clause (i)(c)",
            key="btn_ar_immovable"
        )
        if run_caro_ic:
            st.markdown("##### Immovable-property disputes – clause (i)(c)")
            dispute_yes, dispute_snip = process_pdf_for_check(
                annual_report_file, model, "annual_immovable_dispute"
            )
            needs_clause = dispute_yes
            if dispute_yes:
                st.write("🔍 **Dispute reference in Annual Report:**")
                st.markdown(f"> {dispute_snip}")
            else:
                st.success("No immovable-property dispute – clause (i)(c) not required.")

            label = "(i)(c)"
            ok, snippet, *_ = process_pdf_for_check(
                annexure_a_file, model, "caro_clause_present", {"label": label}
            )
            st.markdown("**Checklist – CARO clause (i)(c)**")
            if needs_clause:
                if ok:
                    st.success("✅ Clause (i)(c) present – auditor addressed the property dispute.")
                    st.write(snippet)
                else:
                    st.error("❌ Dispute detected, but clause (i)(c) is missing.")
            else:
                if ok:
                    st.info(f"Clause {label} marked ‘Not applicable’.")
                    st.write(snippet)
                else:
                    st.info(f"Clause {label} correctly omitted as ‘Not applicable’.")

        # Benami reference → clause (i)(e)
        run_caro_ie = run_all or st.button(
            "Run Check: Benami reference → CARO clause (i)(e)",
            key="btn_benami_ie"
        )
        if run_caro_ie:
            st.markdown("##### Benami properties – clause (i)(e)")
            benami_found, benami_snip = process_pdf_for_check(
                annual_report_file, model, "find_benami", {}
            )
            needs_clause = benami_found
            if benami_found:
                st.write("🔍 **Benami reference in Annual Report:**")
                st.markdown(f"> …{benami_snip}…")
            else:
                st.success("No ‘benami’ reference – clause (i)(e) not required.")

            label = "(i)(e)"
            ok, snippet, *_ = process_pdf_for_check(
                annexure_a_file, model, "caro_clause_present", {"label": label}
            )
            st.markdown("**Checklist – CARO clause (i)(e)**")
            if needs_clause:
                if ok:
                    st.success("✅ Clause (i)(e) present – auditor has reported on Benami properties.")
                    st.write(snippet)
                else:
                    st.error("❌ ‘Benami’ reference detected, but clause (i)(e) is missing.")
            else:
                if ok:
                    st.info(f"Clause {label} marked ‘Not applicable’.")
                    st.write(snippet)
                else:
                    st.info(f"Clause {label} correctly omitted as ‘Not applicable’.")
    else:
        st.info("Upload both the Full Annual Report and Annexure A (CARO) to run these checks.")
            

#2.7  CARO – Secretarial Compliance

    st.subheader("2.7 CARO – Secretarial Compliance")

    if secretarial_comp_file and annexure_a_file:

        if run_all or st.button("Run Check: Sec-185/186 qualification  →  CARO clause (iv)",
                                key="btn_sec_185_186"):

            st.markdown("##### Section 185 & 186 of Companies Act – "
                        "Secretarial Report vs CARO clause (iv)")

            # 1)  Gemini scan of Secretarial Compliance/Audit Report
            qual_yes, qual_snip = process_pdf_for_check(
                secretarial_comp_file, model, "secretarial_185_186"
            )

            if qual_yes:
                st.write("🔍 **Qualification in Secretarial Report:**")
                st.markdown(f"> {qual_snip}")
            else:
                st.success("No qualification under Section 185/186 in Secretarial Report.")

            # 2)  Gemini scan of Annexure A clause (iv)
            iv_yes, iv_snip = process_pdf_for_check(
                annexure_a_file, model, "caro_iv_present"
            )

            # 3)  Verdict matrix
            if qual_yes and iv_yes:
                st.success("✅ CARO clause (iv) present – auditor has reported the Sec 185/186 issue.")
                st.write(iv_snip)

            elif qual_yes and not iv_yes:
                st.error("❌ Sec 185/186 qualification detected, but CARO clause (iv) is missing.")
                st.write(iv_snip)

            elif not qual_yes and iv_yes:
                st.info("CARO clause (iv) appears even though the Secretarial Report shows no "
                        "Sec 185/186 issue (acceptable if marked ‘Not applicable’).")
                st.write(iv_snip)

            else:   # no qualification, clause absent
                st.success("No Sec 185/186 qualification – clause (iv) correctly omitted.")


    # ────────────────────────────────────────────────────────────────────
    # Section 42 / 62 qualification  ⇄  CARO clause (x)(b)
    # ────────────────────────────────────────────────────────────────────
        if run_all or st.button("Run Check: Sec-42/62 qualification → CARO clause (x)(b)",
                                key="btn_sec_42_62"):
            st.markdown("##### Section 42 & 62 (Private Placement / Rights Issue) – "
                        "Secretarial Report vs CARO clause (x)(b)")

            # 1) Secretarial Compliance – does it flag Sec 42 / 62?
            qual42_62, snip42_62 = process_pdf_for_check(
                secretarial_comp_file, model, "secretarial_42_62"
            )

            if qual42_62:
                st.write("🔍 **Qualification in Secretarial Report:**")
                st.markdown(f"> {snip42_62}")
            else:
                st.success("No qualification under Section 42 or 62 in Secretarial Report.")

            # 2) CARO clause (x)(b) in Annexure A
            xb_yes, xb_snip = process_pdf_for_check(
                annexure_a_file, model, "caro_xb_present"
            )

            # 3) Verdict matrix
            if qual42_62 and xb_yes:
                st.success("✅ CARO clause (x)(b) present – auditor has reported the Sec 42/62 issue.")
                st.write(xb_snip)

            elif qual42_62 and not xb_yes:
                st.error("❌ Sec 42/62 qualification detected, but CARO clause (x)(b) is missing.")
                st.write(xb_snip)

            elif not qual42_62 and xb_yes:
                st.info("CARO clause (x)(b) appears even though Secretarial Report shows no "
                        "Sec 42/62 issue (acceptable if marked ‘Not applicable’).")
                st.write(xb_snip)

            else:
                st.success("No Sec 42/62 qualification – clause (x)(b) correctly omitted.")

        # ─────────────────────────────────────────────────────────────
        # Sec 177 / 188 qualification  ⇄  CARO clause (xiii)
        # ─────────────────────────────────────────────────────────────
        if run_all or st.button("Run Check: Sec-177/188 qualification → CARO clause (xiii)",
                                    key="btn_sec_177_188"):

                st.markdown("##### Sections 177 & 188 (Related-Party Transactions) – "
                            "Secretarial Report vs CARO clause (xiii)")

                # ①  Secretarial Audit – qualification?
                rpt_qual, rpt_snip = process_pdf_for_check(
                    secretarial_comp_file, model, "secretarial_177_188"
                )

                if rpt_qual:
                    st.write("🔍 **Qualification in Secretarial Report:**")
                    st.markdown(f"> {rpt_snip}")
                else:
                    st.success("No Sec 177 / 188 qualification in Secretarial Report.")

                # ②  CARO Annexure – clause (xiii)?
                caro_xiii, caro_snip = process_pdf_for_check(
                    annexure_a_file, model, "caro_xiii_present"
                )

                # ③  Verdict matrix
                if rpt_qual and caro_xiii:
                    st.success("✅ CARO clause (xiii) present – auditor has reported the Sec 177/188 issue.")
                    st.write(caro_snip)

                elif rpt_qual and not caro_xiii:
                    st.error("❌ Sec 177/188 qualification detected, but CARO clause (xiii) is missing.")
                    st.write(caro_snip)

                elif not rpt_qual and caro_xiii:
                    st.info("CARO clause (xiii) appears even though no Sec 177/188 issue was flagged "
                            "(acceptable if marked ‘Not applicable’).")
                    st.write(caro_snip)

                else:
                    st.success("No Sec 177/188 qualification – clause (xiii) correctly omitted.")


    # ────────────────────────────────────────────────────────────
    # Sec-192 qualification  ⇄  CARO clause (xv)
    # ────────────────────────────────────────────────────────────
        if run_all or st.button("Run Check: Sec-192 qualification → CARO clause (xv)",
                                key="btn_sec_192"):

            st.markdown("##### Section 192 (Non-cash transactions with directors) – "
                        "Secretarial Report vs CARO clause (xv)")

            # ① Secretarial Audit – is there a qualification?
            qual192, snip192 = process_pdf_for_check(
                secretarial_comp_file, model, "secretarial_192"
            )

            if qual192:
                st.write("🔍 **Qualification in Secretarial Report:**")
                st.markdown(f"> {snip192}")
            else:
                st.success("No Sec 192 qualification in Secretarial Report.")

            # ② Annexure A – does clause (xv) appear?
            xv_yes, xv_snip = process_pdf_for_check(
                annexure_a_file, model, "caro_xv_present"
            )

            # ③ Verdict matrix
            if qual192 and xv_yes:
                st.success("✅ CARO clause (xv) present – auditor has reported the Sec 192 issue.")
                st.write(xv_snip)

            elif qual192 and not xv_yes:
                st.error("❌ Sec 192 qualification detected, but CARO clause (xv) is missing.")
                st.write(xv_snip)

            elif not qual192 and xv_yes:
                st.info("CARO clause (xv) appears even though no Sec 192 issue was flagged "
                        "(acceptable if marked ‘Not applicable’).")
                st.write(xv_snip)

            else:  # no qualification & clause absent
                st.success("No Sec 192 qualification – clause (xv) correctly omitted.")

    # ────────────────────────────────────────────────────────────
    # Sec-135 qualification  ⇄  CARO clause (xx)
    # ────────────────────────────────────────────────────────────
    if secretarial_comp_file and annexure_a_file:
        if run_all or st.button("Run Check: Sec-135 qualification → CARO clause (xx)",
                                key="btn_sec_135"):

            st.markdown("##### Section 135 (CSR) – Secretarial Report vs CARO clause (xx)")

            # ①  Gemini scan – Secretarial qualification?
            qual135, snip135 = process_pdf_for_check(
                secretarial_comp_file, model, "secretarial_135"
            )

            if qual135:
                st.write("🔍 **Qualification in Secretarial Report:**")
                st.markdown(f"> {snip135}")
            else:
                st.success("No Sec 135 qualification in Secretarial Report.")

            # ②  Gemini scan – clause (xx) in Annexure A?
            xx_yes, xx_snip = process_pdf_for_check(
                annexure_a_file, model, "caro_xx_present"
            )

            # ③  Verdict matrix
            if qual135 and xx_yes:
                st.success("✅ CARO clause (xx) present – auditor has reported the Sec 135 issue.")
                st.write(xx_snip)

            elif qual135 and not xx_yes:
                st.error("❌ Sec 135 qualification detected, but CARO clause (xx) is missing.")
                st.write(xx_snip)

            elif not qual135 and xx_yes:
                st.info("CARO clause (xx) appears even though no Sec 135 issue was flagged "
                        "(acceptable if marked ‘Not applicable’).")
                st.write(xx_snip)

            else:  # no qualification & clause absent
                st.success("No Sec 135 qualification – clause (xx) correctly omitted.")

    #sec73-76 sce complinace

    if secretarial_comp_file and annexure_a_file:
        run_caro_v_sec = run_all or st.button(
            "Run Check: Sec 73–76 qualifiers → CARO clause (v)",
            key="btn_caro_v_sec"
        )
        if run_caro_v_sec:
            st.markdown("##### Qualifications under Sections 73–76 → CARO clause (v)")

            # 1) Secretarial Audit Report check
            sec_found, sec_detail = process_pdf_for_check(
                secretarial_comp_file, model, "secretarial_qual_73_76", {}
            )
            if sec_found:
                st.write("🔍 **Qualification found in Secretarial Audit Report:**")
                st.markdown(f"> {sec_detail}")
            else:
                st.success("No qualifications under Sections 73–76 – clause (v) not required.")

            # 2) CARO clause (v) check
            caro_ok, caro_detail = process_pdf_for_check(
                annexure_a_file, model,
                "caro_clause_present", {"label": "(v)"}
            )

            st.markdown("**Checklist – CARO clause (v):**")
            if sec_found:
                if caro_ok:
                    st.success("✅ Clause (v) present in CARO Annexure.")
                    st.write(caro_detail)
                else:
                    st.error("❌ Clause (v) missing – auditor should address public deposits.")
            else:
                if caro_ok and "not applicable" not in caro_detail.lower():
                    st.info("⚠️ Clause (v) appears although no Sec 73–76 issue (OK if marked ‘Not applicable’).")
                    st.write(caro_detail)
                else:
                    st.write("Clause (v) correctly omitted or marked ‘Not applicable’.")
    else:
        st.info("Upload both the Secretarial Audit Report and Annexure A (CARO) to run this check.")


#2.8 caro-csr

    st.subheader("2.8 CARO – CSR Note")
    
    # ────────────────────────────────────────────────────────────────
    #  CARO – CSR Note (prior-year unspent)
    # ────────────────────────────────────────────────────────────────
    if csr_notes_file and annexure_a_file:
        run_csr_xxa = run_all or st.button(
            "Run Check: CSR unspent → CARO clause (xx)(a)",
            key="btn_caro_xxa"
        )
        if run_csr_xxa:
            st.markdown("##### Unspent CSR funds from prior year – clause (xx)(a)")

            found, unspent_line, *_ = process_pdf_for_check(
                csr_notes_file, model, "notes_csr_unspent", {}
            )
            needs_clause = found

            if found:
                amt, status = unspent_line.replace("UNPELAT:", "").split("–", 1)
                st.write(f"🔍 **Previous-year CSR unspent:** {amt.strip()} – {status.strip()}")
            else:
                st.success("✅ No unspent CSR amount from previous year; clause (xx)(a) not required.")

            label = "(xx)(a)"
            ok, snippet, *_ = process_pdf_for_check(
                annexure_a_file, model, "caro_clause_present", {"label": label}
            )
            st.markdown("**Checklist – CARO clause (xx)(a)**")
            if needs_clause:
                if ok:
                    st.success(f"✅ Clause {label} present in CARO Annexure.")
                    st.write(snippet)
                else:
                    st.error("❌ Clause (xx)(a) missing – auditor should report CSR unspent transfers.")
            else:
                if ok:
                    st.write(f"Clause {label} marked ‘Not applicable’.")
                    st.write(snippet)
                else:
                    st.write(f"Clause {label} correctly omitted as ‘Not applicable’.")


    # ────────────────────────────────────────────────────────────────
    # 2.8 CARO – CSR Note (prior-year ongoing projects)
    # ────────────────────────────────────────────────────────────────
    if csr_notes_file and annexure_a_file:
        run_csr_xxb = run_all or st.button(
            "Run Check: CSR unspent (ongoing) → CARO clause (xx)(b)",
            key="btn_caro_xxb"
        )
        if run_csr_xxb:
            st.markdown("##### Ongoing CSR unspent – clause (xx)(b)")

            found, ongoing_line, *_ = process_pdf_for_check(
                csr_notes_file, model, "notes_csr_unspent_ongoing", {}
            )
            needs_clause = found

            if found:
                amt, status = ongoing_line.replace("ONGOING:", "").split("–", 1)
                st.write(f"🔍 **Previous-year ongoing CSR unspent:** {amt.strip()} – {status.strip()}")
            else:
                st.success("✅ No unspent amounts for ongoing CSR projects; clause (xx)(b) not required.")

            label = "(xx)(b)"
            ok, snippet, *_ = process_pdf_for_check(
                annexure_a_file, model, "caro_clause_present", {"label": label}
            )
            st.markdown("**Checklist – CARO clause (xx)(b)**")
            if needs_clause:
                if ok:
                    st.success(f"✅ Clause {label} present in CARO Annexure.")
                    st.write(snippet)
                else:
                    st.error("❌ Clause (xx)(b) missing – auditor should report ongoing-project transfers.")
            else:
                if ok:
                    st.write(f"Clause {label} marked ‘Not applicable’.")
                    st.write(snippet)
                else:
                    st.write(f"Clause {label} correctly omitted as ‘Not applicable’.")


    # ────────────────────────────────────────────────────────────────
    # CSR unspent – current year (excluding ongoing) → clause (xx)(a)
    # ────────────────────────────────────────────────────────────────
    if csr_notes_file and annexure_a_file:
        run_csr_curr = run_all or st.button(
            "Run Check: CSR current unspent → CARO clause (xx)(a)",
            key="btn_csr_curr_unspent"
        )
        if run_csr_curr:
            st.markdown("##### Current-year CSR unspent (excl. ongoing) – clause (xx)(a)")

            found, curr_line, *_ = process_pdf_for_check(
                csr_notes_file, model, "notes_csr_current_unspent", {}
            )
            needs_clause = found

            if found:
                amt, status = curr_line.replace("CURRENT_UNSPENT:", "").split("–", 1)
                st.write(f"🔍 **Year-end CSR unspent:** {amt.strip()} – {status.strip()}")
            else:
                st.success("✅ No unspent CSR amount (excluding ongoing) as of year-end; clause (xx)(a) not required.")

            label = "(xx)(a)"
            ok, snippet, *_ = process_pdf_for_check(
                annexure_a_file, model, "caro_clause_present", {"label": label}
            )
            st.markdown("**Checklist – CARO clause (xx)(a)**")
            if needs_clause:
                if ok:
                    st.success(f"✅ Clause {label} present in CARO Annexure.")
                    st.write(snippet)
                else:
                    st.error("❌ Clause (xx)(a) missing – auditor should report current-year unspent transfers.")
            else:
                if ok:
                    st.write(f"Clause {label} marked ‘Not applicable’.")
                    st.write(snippet)
                else:
                    st.write(f"Clause {label} correctly omitted as ‘Not applicable’.")


    # ────────────────────────────────────────────────────────────────
    # CSR current year – ongoing projects → clause (xx)(b)
    # ────────────────────────────────────────────────────────────────
    if csr_notes_file and annexure_a_file:
        run_csr_curr_ongoing = run_all or st.button(
            "Run Check: CSR current ongoing unspent → CARO clause (xx)(b)",
            key="btn_csr_curr_ongoing"
        )
        if run_csr_curr_ongoing:
            st.markdown("##### Current-year ongoing CSR unspent – clause (xx)(b)")

            found, ongoing_line, *_ = process_pdf_for_check(
                csr_notes_file, model, "notes_csr_current_ongoing_unspent", {}
            )
            needs_clause = found

            if found:
                amt, status = ongoing_line.replace("CURRENT_ONGOING:", "").split("–", 1)
                st.write(f"🔍 **Ongoing CSR unspent:** {amt.strip()} – {status.strip()}")
            else:
                st.success("✅ No unspent CSR amount for ongoing projects; clause (xx)(b) not required.")

            label = "(xx)(b)"
            ok, snippet, *_ = process_pdf_for_check(
                annexure_a_file, model, "caro_clause_present", {"label": label}
            )
            st.markdown("**Checklist – CARO clause (xx)(b)**")
            if needs_clause:
                if ok:
                    st.success(f"✅ Clause {label} present in CARO Annexure.")
                    st.write(snippet)
                else:
                    st.error("❌ Clause (xx)(b) missing – auditor should report ongoing unspent transfers.")
            else:
                if ok:
                    st.write(f"Clause {label} marked ‘Not applicable’.")
                    st.write(snippet)
                else:
                    st.write(f"Clause {label} correctly omitted as ‘Not applicable’.")
    else:
        st.info("Upload both the CSR Note and CARO Annexure to run this check.")



##############################
# CheckLists Page
##############################
# ╭─────────────────────────────────────────────────────────────╮
# │  REPLACE the entire checklists_page() with this version     │
# ╰─────────────────────────────────────────────────────────────╯
def checklists_page():
    # ========== sidebar bits ==========
    st.sidebar.success(f"Logged in as: {st.session_state.username}")
    st.sidebar.button("Logout", on_click=lambda: logout())

    # ========== custom CSS ==========
    st.markdown(
        """
        <style>
        /* ---- glass card shell ---- */
        .dash-card{
            background:rgba(255,255,255,.55);
            backdrop-filter:blur(14px);
            -webkit-backdrop-filter:blur(14px);
            border-radius:18px;
            padding:2rem 2.2rem 1.8rem 2.2rem;
            box-shadow:0 12px 32px rgba(0,0,0,.08);
            margin-top:1.4rem;
        }
        /* headings */
        .dash-card h3{margin:0 0 1.1rem 0;color:#002e5d;font-weight:700;}
        .dash-card h4{margin:.6rem 0;color:#003f7d;font-weight:600;}
        /* status pill */
        .pill{
            display:inline-block;padding:.15rem .55rem;border-radius:12px;
            font-size:.75rem;font-weight:600;color:#fff;
        }
        .pill-enabled{background:#3cb371;}
        .pill-soon{background:#ff9f43;}
        /* checklist bullets */
        .chk li{margin:.25rem 0;}
        </style>
        """,
        unsafe_allow_html=True
    )

    # ========== page title ==========
    st.title("🗂️ Documents & Requirements Dashboard")

    # --------------------------------------------------
    # 1) document status table (left) + checklists (right)
    # --------------------------------------------------
    col_left, col_right = st.columns([1, 1.6], gap="large")

    # ---------- left: documents ----------
    with col_left:
        st.markdown('<div class="dash-card">', unsafe_allow_html=True)
        st.markdown("### 📄 Documents")

        docs_data = [
            {"#": 1, "Document": "Audit Report",               "Status": "Enabled"},
            {"#": 2, "Document": "Annexure A – CARO",          "Status": "Enabled"},
            {"#": 3, "Document": "Annexure B – IFC",           "Status": "Enabled"},
            {"#": 4, "Document": "Balance Sheet",              "Status": "Enabled"},
            {"#": 5, "Document": "Profit & Loss A/c",          "Status": "Enabled"},
            {"#": 6, "Document": "Cash Flow Statement",        "Status": "Coming Soon.."},
            {"#": 7, "Document": "Statement of Changes in Equity","Status": "Coming Soon.."},
            {"#": 8, "Document": "Accounting Policies",        "Status": "Coming Soon.."},
            {"#": 9, "Document": "Notes to Accounts",          "Status": "Enabled"},
        ]
        # add coloured pills
        for d in docs_data:
            pill_cls = "pill-enabled" if d["Status"] == "Enabled" else "pill-soon"
            d["Status"] = f'<span class="pill {pill_cls}">{d["Status"]}</span>'

        st.write(
            pd.DataFrame(docs_data)
              .style.hide(axis="index")
              .to_html(escape=False),
            unsafe_allow_html=True,
        )
        st.markdown("</div>", unsafe_allow_html=True)

    # ---------- right: integrated checklist ----------
    with col_right:
        st.markdown('<div class="dash-card">', unsafe_allow_html=True)
        st.markdown("### ✅ Disclosures & Regulatory Requirements")

        # --- 1. Independent docs ---
        st.markdown("#### 1  Independent Documents")

        st.markdown(
            """
            <h4>Audit Report</h4>
            <ul class="chk">
              <li>Audit title present (“Independent Auditor’s Report”)</li>
              <li>Addressed to the members</li>
              <li>Audit‑report date matches user input</li>
              <li>Standalone / Consolidated wording consistent</li>
              <li>BRSR / BRR check (Top‑listed logic)</li>
              <li>≥ 1 Key Audit Matter reported</li>
              <li>Audit‑trail accounting software disclosure</li>
              <li>Section 197(16) reference</li>
              <li>Company‑name consistency</li>
              <li>PKF signature block present</li>
            </ul>

            <h4>CARO Annexure</h4>
            <ul class="chk">
              <li>AR‑date consistency</li>
              <li>Standalone / Consolidated wording</li>
              <li>Company‑name consistency</li>
              <li>Signature block (PKF)</li>
            </ul>

            <h4>IFC Annexure</h4>
            <ul class="chk">
              <li>AR‑date consistency</li>
              <li>Standalone / Consolidated wording</li>
              <li>Company‑name consistency</li>
              <li>Signature block (PKF)</li>
            </ul>

            <hr style="margin:1.2rem 0;">

            <!-- 2. Inter‑linked References -->

            <h4>2.1 Audit Report ⇄ CARO</h4>
            <ul class="chk">
            <li><b>(Audit Report ⇄ CARO):</b> Paragraph number matches in both docs</li>
            <li><b>(Audit Report ⇄ CARO):</b> If AR mentions disputes over land/buildings/ROU, ensure clause (i)(c) addresses title‑deeds & dispute coverage</li>
            </ul>

            <h4>2.2 Audit Report ⇄ IFC</h4>
            <ul class="chk">
            <li><b>(Audit Report ⇄ IFC):</b> Paragraph number matches in both docs</li>
            </ul>

            <h4>2.3 CARO ⇄ Balance Sheet</h4>
            <ul class="chk">
            <li><b>(CARO ⇄ Balance Sheet):</b> If PPE &gt; 0, verify sub‑clauses (i)(a)(A) &amp; (i)(b)</li>
            <li><b>(CARO ⇄ Balance Sheet):</b> If Intangible‑asset cost &gt; 0, verify sub‑clause (i)(a)(B)</li>
            <li><b>(CARO ⇄ Balance Sheet):</b> If Inventory cost &gt; 0, verify sub‑clause (ii)(a)</li>
            <li><b>(CARO ⇄ Balance Sheet):</b> If secured borrowings &gt; ₹5 cr, verify clause (ii)(b) on inventory vs quarterly returns</li>
            <li><b>(CARO ⇄ Balance Sheet):</b> If any “fixed deposit” appears under Current or Non‑Current Liabilities, verify clause (v)</li>
            </ul>

            <h4>2.4 CARO ⇄ Notes to Accounts</h4>
            <ul class="chk">
            <li><b>(Notes ⇄ CARO):</b> If any immovable property appears in Notes, verify clause (i)(c) on title‑deeds</li>
            <li><b>(Notes ⇄ CARO):</b> If revaluation reserve appears in Notes, verify clause (i)(d) on revaluation status</li>
            <li><b>(Notes ⇄ CARO):</b> If “goods in transit” or stock with third parties appears in Inventory Note, verify clause (ii)(a) explicitly excludes them from physical verification</li>
            <li><b>(Notes &amp; Balance Sheet ⇄ CARO):</b> If aggregate of new loans/investments/guarantees exceeds 60% of opening net‑worth, verify clause (iv) disclosure</li>
            <li><b>(Notes ⇄ CARO):</b> If Notes show new investments/loans/guarantees, verify clause (iii) covers them</li>
            <li><b>(Contingent Liabilities ⇄ CARO):</b> If Contingent‑Liability note shows financial guarantees, verify total matches clause (iii)(a)(A)+(B)</li>
            <li><b>(Notes ⇄ CARO):</b> If Notes show any GST, PF, ESI, taxes, duties, VAT, Cess, etc., verify sub‑clause (vii)(b)</li>
            <li><b>(RPT ⇄ CARO):</b> If Related‑Party Transactions note discloses loans, advances, guarantees or securities, verify CARO clause (iii)(f)</li>
            
            </ul>

            <h4>2.5 P&amp;L ⇄ CARO</h4>
            <ul class="chk">
            <li><b>(P&amp;L ⇄ CARO):</b> If inventory write‑off/write‑down appears, verify clause (ii)(a) covers ≥10% discrepancy per class</li>
            </ul>



            <h4>2.6 Full Annual Report ⇄ CARO</h4>
            <ul class="chk">
            <li><b>(Annual Report ⇄ CARO):</b> If income‑tax raids or unrecorded income mentioned, verify clause (viii)</li>
            <li><b>(Annual Report ⇄ CARO):</b> If defaults/wilful defaulter/diversion mentioned, verify clause (ix)</li>
            <li><b>(Annual Report ⇄ CARO):</b> If rights issue or convertible debenture mentioned, verify clause (x)(b)</li>
            <li><b>(Annual Report ⇄ CARO):</b> If “fraud” mentioned, verify clause (xi)(a)</li>
            <li><b>(Annual Report ⇄ CARO):</b> If Sub‑section (12) of Section 143 referenced, verify clause (xi)(b)</li>
            <li><b>(Annual Report ⇄ CARO):</b> If whistle‑blower complaint referenced, verify clause (xi)(c)</li>
            <li><b>(Annual Report ⇄ CARO):</b> If benami properties referenced, verify clause (i)(e)</li>
            <li><b>(Annual Report ⇄ CARO):</b> If immovable‑property dispute referenced, verify clause (i)(c)</li>

            </ul>

            <h4>2.7 Secretarial Compliance ⇄ CARO</h4>
            <ul class="chk">
            <li><b>(Secretarial Report ⇄ CARO):</b> If Sec 185/186 qualification appears, verify clause (iv)</li>
            <li><b>(Secretarial Report ⇄ CARO):</b> If Sec 42/62 qualification appears, verify clause (x)(b)</li>
            <li><b>(Secretarial Report ⇄ CARO):</b> If Sec 177/188 qualification appears, verify clause (xiii)</li>
            <li><b>(Secretarial Report ⇄ CARO):</b> If Sec 192 qualification appears, verify clause (xv)</li>
            <li><b>(Secretarial Report ⇄ CARO):</b> If Sec 135 (CSR) qualification appears, verify clause (xx)</li>
            </ul>

            """,
            unsafe_allow_html=True,
        )
        
        
        st.markdown("</div>", unsafe_allow_html=True)

    # tiny footnote
    st.caption("ℹ️ Documents marked *Coming Soon* are in active development and will be enabled in a future release.")

# Logout function
def logout():
    st.session_state.logged_in = False
    st.session_state.username = ""
    st.session_state.current_page = 'login'
    st.rerun()


##############################
# Sidebar & main
##############################
# ──────────────────────────────────────────────────────────────────────  
# Sidebar & main
# ──────────────────────────────────────────────────────────────────────  
def sidebar_menu():
    st.sidebar.title("Navigation")

    if not st.session_state.logged_in:
        return

    if st.sidebar.button("PDF Analysis"):
        st.session_state.current_page = 'analyze'
    if st.sidebar.button("Prompt Management"):
        st.session_state.current_page = 'prompt'
    if st.sidebar.button("Check Lists"):
        st.session_state.current_page = 'checklists'


def main():

    # 1) If not logged in → show login page
    if not st.session_state.logged_in:
        login_page()
        return

    # 2) Logged in → render sidebar and selected page
    sidebar_menu()

    if st.session_state.current_page == 'analyze':
        analysis_page()
    elif st.session_state.current_page == 'checklists':
        checklists_page()
    else:
        # fallback
        analysis_page()


if __name__ == "__main__":
    main()
