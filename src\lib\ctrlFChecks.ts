// ctrlFChecks.ts - Simple text-based checks using Ctrl+F logic

import { CheckResult } from './checkDefinitions';

/**
 * Extract text content from PDF file (requires PDF.js or similar library)
 * For now, this is a placeholder - you'll need to implement PDF text extraction
 */
async function extractTextFromPDF(pdfFile: File): Promise<string> {
  // Placeholder implementation
  // In real implementation, use PDF.js library:
  // import * as pdfjsLib from 'pdfjs-dist';
  
  try {
    // This would be the actual PDF text extraction logic
    console.warn('PDF text extraction not implemented. Using placeholder.');
    return 'PDF text content would be extracted here';
  } catch (error) {
    console.error('Error extracting text from PDF:', error);
    return '';
  }
}

/**
 * Count occurrences of a phrase in text (case-insensitive)
 */
function countOccurrences(text: string, phrase: string): number {
  const regex = new RegExp(phrase.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi');
  const matches = text.match(regex);
  return matches ? matches.length : 0;
}

/**
 * Find context around a phrase in text
 */
function findContext(text: string, phrase: string, contextWords: number = 10): string[] {
  const regex = new RegExp(phrase.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi');
  const contexts: string[] = [];
  let match;
  
  while ((match = regex.exec(text)) !== null) {
    const words = text.split(/\s+/);
    const matchIndex = text.substring(0, match.index).split(/\s+/).length - 1;
    
    const start = Math.max(0, matchIndex - contextWords);
    const end = Math.min(words.length, matchIndex + contextWords + 1);
    
    const context = words.slice(start, end).join(' ');
    contexts.push(context);
  }
  
  return contexts;
}

/**
 * Check for CARO Clause (xxi) - should appear exactly once in consolidated reports
 */
export async function checkCAROClauseXXI(caroFile: File): Promise<CheckResult> {
  try {
    const text = await extractTextFromPDF(caroFile);
    const occurrences = countOccurrences(text, '(xxi)');
    
    const isCompliant = occurrences === 1;
    
    if (isCompliant) {
      const contexts = findContext(text, '(xxi)', 5);
      return {
        isCompliant: true,
        explanation: 'CARO clause (xxi) found exactly once as expected for consolidated reports',
        confidence: 0.95,
        extractedData: {
          occurrences,
          context: contexts[0] || 'Context not available'
        }
      };
    } else {
      return {
        isCompliant: false,
        explanation: `CARO clause (xxi) found ${occurrences} times. Expected exactly 1 occurrence for consolidated reports.`,
        confidence: 0.95,
        extractedData: {
          occurrences,
          contexts: findContext(text, '(xxi)', 5)
        }
      };
    }
  } catch (error) {
    return {
      isCompliant: false,
      explanation: `Error checking CARO clause (xxi): ${error instanceof Error ? error.message : String(error)}`,
      confidence: 0.5
    };
  }
}

/**
 * Check consolidated wording consistency
 */
export async function checkConsolidatedWording(auditReportFile: File): Promise<CheckResult> {
  try {
    const text = await extractTextFromPDF(auditReportFile);
    
    // Find all instances of "financial statements"
    const financialStatementsRegex = /\b\w*\s*financial\s+statements\b/gi;
    const matches = text.match(financialStatementsRegex) || [];
    
    // Check if "consolidated" precedes each instance
    const consolidatedCount = countOccurrences(text, 'consolidated financial statements');
    const standaloneCount = countOccurrences(text, 'standalone financial statements');
    const totalFinancialStatements = matches.length;
    
    // For consolidated reports, all instances should say "consolidated"
    const isCompliant = consolidatedCount > 0 && standaloneCount === 0;
    
    return {
      isCompliant,
      explanation: isCompliant 
        ? `Consolidated wording is consistent throughout the document. Found ${consolidatedCount} instances of "consolidated financial statements".`
        : `Inconsistent wording found. Consolidated: ${consolidatedCount}, Standalone: ${standaloneCount}, Total: ${totalFinancialStatements}`,
      confidence: 0.9,
      extractedData: {
        consolidatedCount,
        standaloneCount,
        totalFinancialStatements,
        allMatches: matches
      }
    };
  } catch (error) {
    return {
      isCompliant: false,
      explanation: `Error checking consolidated wording: ${error instanceof Error ? error.message : String(error)}`,
      confidence: 0.5
    };
  }
}

/**
 * Check for specific phrase presence (generic function)
 */
export async function checkPhrasePresence(
  pdfFile: File,
  phrase: string,
  description: string,
  shouldBePresent: boolean = true
): Promise<CheckResult> {
  try {
    const text = await extractTextFromPDF(pdfFile);
    const occurrences = countOccurrences(text, phrase);
    
    const isPresent = occurrences > 0;
    const isCompliant = shouldBePresent ? isPresent : !isPresent;
    
    const contexts = isPresent ? findContext(text, phrase, 8) : [];
    
    return {
      isCompliant,
      explanation: isCompliant
        ? `${description}: ${isPresent ? 'Found' : 'Not found'} as expected (${occurrences} occurrences)`
        : `${description}: ${isPresent ? 'Found' : 'Not found'} but ${shouldBePresent ? 'expected' : 'not expected'} (${occurrences} occurrences)`,
      confidence: 0.9,
      extractedData: {
        phrase,
        occurrences,
        contexts: contexts.slice(0, 3) // Limit to first 3 contexts
      }
    };
  } catch (error) {
    return {
      isCompliant: false,
      explanation: `Error checking phrase "${phrase}": ${error instanceof Error ? error.message : String(error)}`,
      confidence: 0.5
    };
  }
}

/**
 * Check for Independent Auditor's Report title
 */
export async function checkAuditTitle(auditReportFile: File): Promise<CheckResult> {
  return checkPhrasePresence(
    auditReportFile,
    'Independent Auditor\'s Report',
    'Independent Auditor\'s Report title',
    true
  );
}

/**
 * Check for "To the Members of [Company]" format
 */
export async function checkMembersAddress(auditReportFile: File, companyName: string): Promise<CheckResult> {
  return checkPhrasePresence(
    auditReportFile,
    `To the Members of ${companyName}`,
    `Address to Members format`,
    true
  );
}

/**
 * Check for Key Audit Matters section
 */
export async function checkKeyAuditMatters(auditReportFile: File): Promise<CheckResult> {
  try {
    const text = await extractTextFromPDF(auditReportFile);
    
    // Look for various forms of Key Audit Matters
    const kamVariations = [
      'Key Audit Matters',
      'Key Audit Matter',
      'KAM'
    ];
    
    let totalOccurrences = 0;
    const foundVariations: string[] = [];
    
    for (const variation of kamVariations) {
      const count = countOccurrences(text, variation);
      if (count > 0) {
        totalOccurrences += count;
        foundVariations.push(`${variation} (${count})`);
      }
    }
    
    const isCompliant = totalOccurrences > 0;
    
    return {
      isCompliant,
      explanation: isCompliant
        ? `Key Audit Matters section found. Variations: ${foundVariations.join(', ')}`
        : 'Key Audit Matters section not found in the audit report',
      confidence: 0.9,
      extractedData: {
        totalOccurrences,
        foundVariations,
        contexts: foundVariations.length > 0 ? findContext(text, kamVariations[0], 10) : []
      }
    };
  } catch (error) {
    return {
      isCompliant: false,
      explanation: `Error checking Key Audit Matters: ${error instanceof Error ? error.message : String(error)}`,
      confidence: 0.5
    };
  }
}

/**
 * Check for NBFC-specific clause omissions in CARO
 */
export async function checkNBFCCAROExemptions(caroFile: File): Promise<CheckResult> {
  try {
    const text = await extractTextFromPDF(caroFile);
    
    // For NBFCs, clauses (iii)(a) and (iii)(e) should be omitted or marked as N/A
    const clause3a = countOccurrences(text, '(iii)(a)');
    const clause3e = countOccurrences(text, '(iii)(e)');
    
    // Check for "not applicable" or "N/A" mentions near these clauses
    const notApplicable3a = countOccurrences(text, '(iii)(a)') > 0 && 
                           (countOccurrences(text, 'not applicable') > 0 || countOccurrences(text, 'N/A') > 0);
    
    const isCompliant = clause3a === 0 && clause3e === 0 || notApplicable3a;
    
    return {
      isCompliant,
      explanation: isCompliant
        ? 'NBFC CARO exemptions properly handled - clauses (iii)(a) and (iii)(e) are appropriately omitted or marked as not applicable'
        : `NBFC CARO exemptions not properly handled. Found (iii)(a): ${clause3a}, (iii)(e): ${clause3e}`,
      confidence: 0.9,
      extractedData: {
        clause3aOccurrences: clause3a,
        clause3eOccurrences: clause3e,
        notApplicableMentions: countOccurrences(text, 'not applicable')
      }
    };
  } catch (error) {
    return {
      isCompliant: false,
      explanation: `Error checking NBFC CARO exemptions: ${error instanceof Error ? error.message : String(error)}`,
      confidence: 0.5
    };
  }
}